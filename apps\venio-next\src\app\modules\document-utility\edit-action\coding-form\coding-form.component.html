<ng-container
  *ngIf="
    (isFetchCodingFieldByIdLoading$ | async) === false ||
    (isFetchCodingFieldByIdLoading$ | async) === null ||
    (isFetchCodingFieldByIdLoading$ | async) === undefined
  ">
  <div
    class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg"
    [formGroup]="codingFormGroup">
    <div
      *ngIf="formErrorMessage()"
      venioAutoScrollOrFocus
      [shouldFocusOrScroll]="formErrorMessage()"
      class="t-flex t-w-full t-gap-4 t-p-4 t-bg-opacity-40 t-bg-tertiary t-text-error">
      <kendo-svg-icon [icon]="iconExclamation"></kendo-svg-icon>
      <span>
        {{ formErrorMessage() }}
      </span>
    </div>
    <div class="t-flex t-w-full t-flex-wrap t-gap-[1.25%] t-gap-y-3">
      <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
        <kendo-formfield>
          <kendo-label
            [for]="fn"
            class="t-text-xs t-uppercase t-tracking-widest">
            Field Name <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-textbox
            (input)="replaceSpaceWithUnderscore()"
            formControlName="fieldName"
            placeholder="Custom Field Name"
            required
            #fn></kendo-textbox>
          <kendo-formerror
            >{{
              fieldNameControl?.errors?.pattern
                ? 'Field Name contains invalid characters'
                : 'Field name is required.'
            }}
          </kendo-formerror>
        </kendo-formfield>
      </div>
      <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
        <kendo-formfield>
          <kendo-label
            [for]="ft"
            class="t-text-xs t-uppercase t-tracking-widest">
            Type <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-dropdownlist
            #ft
            [loading]="isCustomFieldTypeLoading$ | async"
            [data]="fieldTypesData"
            [valuePrimitive]="true"
            formControlName="uiInputType"
            data-qa="customType"
            textField="displayName"
            valueField="value"
            required>
          </kendo-dropdownlist>
          <kendo-formerror>Field type is required.</kendo-formerror>
        </kendo-formfield>
      </div>
      <div
        *ngIf="isPrecisionAndScale()"
        class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
        <kendo-formfield>
          <kendo-label
            [for]="len"
            class="t-text-xs t-uppercase t-tracking-widest">
            Precision <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-numerictextbox
            formControlName="length"
            placeholder="Precision"
            data-qa="precision"
            [min]="0"
            [required]="isPrecisionAndScale()"
            #len></kendo-numerictextbox>
          <kendo-formerror>Precision is required.</kendo-formerror>
        </kendo-formfield>
      </div>
      <div
        *ngIf="isPrecisionAndScale()"
        class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
        <kendo-formfield>
          <kendo-label
            [for]="scaleType"
            class="t-text-xs t-uppercase t-tracking-widest">
            Scale <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-numerictextbox
            formControlName="scale"
            placeholder="Scale"
            data-qa="scaleType"
            [min]="0"
            [required]="isPrecisionAndScale()"
            #scaleType></kendo-numerictextbox>
          <kendo-formerror>Scale is required.</kendo-formerror>
        </kendo-formfield>
      </div>
      <div
        *ngIf="isLength()"
        class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
        <kendo-formfield>
          <kendo-label
            [for]="typeLength"
            class="t-text-xs t-uppercase t-tracking-widest">
            Length <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-numerictextbox
            formControlName="length"
            placeholder="Length"
            data-qa="TypeLength"
            [min]="0"
            [required]="isLength()"
            #typeLength></kendo-numerictextbox>
          <kendo-formerror>Length is required.</kendo-formerror>
        </kendo-formfield>
      </div>
    </div>
    <div class="t-flex t-w-full t-flex-wrap t-gap-3">
      <div class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
        <kendo-label
          for="Description"
          class="t-text-xs t-uppercase t-tracking-widest">
          Description
        </kendo-label>
        <kendo-textarea
          formControlName="description"
          #Description
          placeholder="Description"
          [rows]="3"
          resizable="vertical"></kendo-textarea>
      </div>
    </div>
  </div>
  <kendo-tabstrip class="t-w-full t-mt-3">
    <kendo-tabstrip-tab title="Advance Options" [selected]="true">
      <ng-template kendoTabContent [formGroup]="codingFormGroup">
        <div
          class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 t-gap-y-5 v-custom-grey-bg">
          <div class="t-flex t-w-full t-flex-wrap t-gap-[1.24%] t-gap-y-3">
            <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
              <kendo-label
                for="Parent"
                class="t-text-xs t-uppercase t-tracking-widest">
                Allow Empty Value
              </kendo-label>
              <div class="t-flex t-gap-3 t-mt-2">
                <input
                  type="radio"
                  #Yes
                  [value]="true"
                  formControlName="allowEmptyValues"
                  kendoRadioButton
                  checked />
                <kendo-label
                  [for]="Yes"
                  [ngClass]="{
                    't-text-primary t-font-medium':
                      codingFormGroup.get('allowEmptyValues')?.value === true &&
                      !Yes.disabled
                  }"
                  text="Yes"></kendo-label>
                <input
                  #No
                  type="radio"
                  [value]="false"
                  formControlName="allowEmptyValues"
                  kendoRadioButton />
                <kendo-label
                  [for]="No"
                  [ngClass]="{
                    't-text-primary t-font-medium':
                      codingFormGroup.get('allowEmptyValues')?.value ===
                        false && !No.disabled
                  }"
                  text="No"></kendo-label>
              </div>
            </div>
            <div
              *ngIf="!allowEmptyValuesControl.value"
              class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
              <kendo-formfield>
                <kendo-label
                  [for]="df"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Default Value
                  <span class="t-text-error">*</span>
                </kendo-label>
                <kendo-textbox
                  formControlName="defaultValue"
                  placeholder="Default value"
                  [required]="!allowEmptyValuesControl.value"
                  #df></kendo-textbox>
                <kendo-formerror>Default value is required.</kendo-formerror>
              </kendo-formfield>
            </div>
          </div>
          <div class="t-flex t-w-full t-mt-2">
            <div class="t-flex-none t-font-bold t-text-base t-w-3/5">
              <span class="t-text-primary">Coding</span>
            </div>
          </div>
          <div class="t-flex t-w-full t-flex-wrap t-gap-[1.24%] t-gap-y-3">
            <!--          TODO: these are commented for phase 1 as will be implemented in phase 2-->
            <!--            <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">-->
            <!--              <kendo-label-->
            <!--                for="Label"-->
            <!--                class="t-text-xs t-uppercase t-tracking-widest">-->
            <!--                Enable Coding-->
            <!--              </kendo-label>-->

            <!--              <div class="t-flex t-gap-3 t-mt-2">-->
            <!--                <input-->
            <!--                  type="radio"-->
            <!--                  #enableYes-->
            <!--                  value="Yes"-->
            <!--                  kendoRadioButton-->
            <!--                  formControlName="enableYesNo"-->
            <!--                  checked />-->
            <!--                <kendo-label [for]="enableYes" text="Yes"></kendo-label>-->

            <!--                <input-->
            <!--                  type="radio"-->
            <!--                  #enableNo-->
            <!--                  value="No"-->
            <!--                  kendoRadioButton-->
            <!--                  formControlName="enableYesNo"-->
            <!--                <kendo-label [for]="enableNo" text="No"></kendo-label>-->
            <!--              </div>-->
            <!--            </div>-->

            <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
              <kendo-label
                for="Reviewer"
                class="t-text-xs t-uppercase t-tracking-widest">
                Enable Multiple Values
              </kendo-label>
              <div class="t-flex t-gap-3 t-mt-2">
                <input
                  type="radio"
                  #multipleYes
                  [value]="true"
                  formControlName="allowMultipleCodingValues"
                  kendoRadioButton />
                <kendo-label
                  [for]="multipleYes"
                  [ngClass]="{
                    't-text-primary t-font-medium':
                      codingFormGroup.get('allowMultipleCodingValues')
                        ?.value === true && !multipleYes.disabled
                  }"
                  text="Yes"></kendo-label>
                <input
                  type="radio"
                  #multipleNo
                  formControlName="allowMultipleCodingValues"
                  [value]="false"
                  kendoRadioButton
                  checked />
                <kendo-label
                  [for]="multipleNo"
                  [ngClass]="{
                    't-text-primary t-font-medium':
                      codingFormGroup.get('allowMultipleCodingValues')
                        ?.value === false && !multipleNo.disabled
                  }"
                  text="No"></kendo-label>
              </div>
            </div>

            <div
              *ngIf="allowMultipleCodingValuesControl.value"
              class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
              <kendo-formfield>
                <kendo-label
                  [for]="delimiterValue"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Delimiter <span class="t-text-error">*</span>
                </kendo-label>
                <kendo-dropdownlist
                  [loading]="isDelimiterLoading$ | async"
                  #delimiterValue
                  formControlName="delimiterForCodingValues"
                  [data]="delimiterData"
                  [filterable]="true"
                  [virtual]="{ itemHeight: 28 }"
                  [kendoDropDownFilter]="{
                    caseSensitive: false,
                    operator: 'contains'
                  }"
                  required>
                </kendo-dropdownlist>
                <kendo-formerror>Delimiter is required.</kendo-formerror>
              </kendo-formfield>
            </div>
          </div>
          <div class="t-flex t-w-full t-flex-wrap t-gap-3 t-mt-1">
            <div class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
              <kendo-label
                [for]="codingValues"
                class="t-text-xs t-uppercase t-tracking-widest">
                Coding Values
              </kendo-label>
              <kendo-textarea
                #codingValues
                formControlName="codingValues"
                placeholder="Coding Values"
                data-qa="codingValues"
                [rows]="3"
                resizable="vertical"></kendo-textarea>
            </div>
          </div>
          <div
            *ngIf="isPredefinedValueOnly()"
            class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
            <kendo-label
              for="Reviewer"
              class="t-text-xs t-uppercase t-tracking-widest">
              Predefined Values Only <span class="t-text-error">*</span>
            </kendo-label>
            <div class="t-flex t-gap-3 t-mt-2">
              <input
                type="radio"
                #multipleYes
                [value]="true"
                formControlName="allowPredefinedCodingValuesOnly"
                kendoRadioButton />
              <kendo-label
                [for]="multipleYes"
                [ngClass]="{
                  't-text-primary t-font-medium':
                    codingFormGroup.get('allowPredefinedCodingValuesOnly')
                      ?.value === true && !multipleYes.disabled
                }"
                text="Yes"></kendo-label>
              <input
                type="radio"
                #multipleNo
                formControlName="allowPredefinedCodingValuesOnly"
                [value]="false"
                kendoRadioButton
                checked />
              <kendo-label
                [for]="multipleNo"
                [ngClass]="{
                  't-text-primary t-font-medium':
                    codingFormGroup.get('allowPredefinedCodingValuesOnly')
                      ?.value === false && !multipleNo.disabled
                }"
                text="No"></kendo-label>
            </div>
          </div>
        </div>
        <venio-project-role
          permissionType="VISIBILITY"
          (projectRoleChange)="projectRoleChange($event)"
          [savedGroup]="codingGroupAccessControl.value" />
      </ng-template>
    </kendo-tabstrip-tab>
  </kendo-tabstrip>
</ng-container>
<div
  *ngIf="isFetchCodingFieldByIdLoading$ | async"
  class="t-flex t-w-full t-flex-col t-min-h-[15rem]">
  <div class="t-block t-w-full" *ngFor="let x of [1, 2]">
    <div class="t-flex t-gap-5 t-w-full t-flex-row t-relative">
      <kendo-skeleton
        class="t-w-1/3"
        [height]="35"
        [animation]="x > 1 ? 'wave' : 'pulse'"></kendo-skeleton>
      <kendo-skeleton class="t-w-1/3" [height]="35"></kendo-skeleton>
      <kendo-skeleton
        class="t-w-1/3"
        [height]="35"
        [animation]="x > 1 ? 'pulse' : 'wave'"></kendo-skeleton>
    </div>
  </div>
</div>
