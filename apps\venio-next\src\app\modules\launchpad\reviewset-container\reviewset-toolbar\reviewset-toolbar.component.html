<div
  class="t-flex t-justify-end t-gap-3 t-px-5 t-py-3 t-items-center t-border-y-[1px] t-border-y-[#ccc]">
  @for(action of toolbarActions(); track action.uniqueId){
  <button
    [id]="action.uniqueId"
    *ngIf="action.type === 'icon-button' && !isReviewer()"
    kendoButton
    (click)="toolbarActionClicked(action.actionType, action)"
    fillMode="outline"
    rounded="medium"
    class="t-w-8 t-px-6">
    <span
      class="t-inline-flex t-w-8 t-justify-center t-align-text-top"
      [ngClass]="action.cssClass"
      venioSvgLoader
      height="1.2rem"
      width="1.4rem"
      [svgUrl]="action.svgIconPath">
    </span>
  </button>
  <kendo-dropdownbutton
    [id]="action.uniqueId"
    *ngIf="action.type === 'dropdown-button' && !isReviewer()"
    [data]="action.menuItems"
    fillMode="outline"
    rounded="medium"
    buttonClass="t-w-12 t-px-5"
    (itemClick)="toolbarActionClicked($event['actionType'], $event)"
    class="">
    <div class="t-flex t-justify-between">
      <span
        class="t-inline-flex t-justify-self-start t-align-text-top"
        [ngClass]="action.cssClass"
        venioSvgLoader
        height="1.1rem"
        width="1.3rem"
        [svgUrl]="action.svgIconPath">
      </span>
      <kendo-svg-icon
        class="t-text-[#1EBADC]"
        [icon]="chevronDownIcon"></kendo-svg-icon>
    </div>
  </kendo-dropdownbutton>
  <button
    [id]="action.uniqueId"
    *ngIf="action.type === 'label-button' && !isReviewer()"
    kendoButton
    (click)="toolbarActionClicked(action.actionType, action)"
    [ngClass]="action.cssClass"
    fillMode="outline"
    class="v-custom-secondary-button"
    themeColor="secondary"
    kendoTooltip
    [title]="action.title || ''"
    [attr.data-qa]="action['label']">
    {{ action['label'] }}
  </button>
  }
</div>
