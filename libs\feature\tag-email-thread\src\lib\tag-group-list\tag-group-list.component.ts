import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  TrackByFunction,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  ButtonGroupModule,
  ButtonModule,
} from '@progress/kendo-angular-buttons'
import {
  GridDataResult,
  GridItem,
  GridModule,
  PageChangeEvent,
} from '@progress/kendo-angular-grid'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { TagsFacade } from '@venio/data-access/common'
import { Subject, combineLatest, takeUntil, filter, debounceTime } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { TagGroupModel } from '@venio/shared/models/interfaces'
import { BodyModule } from '@progress/kendo-angular-treelist'
import { CompositeFilterDescriptor, filterBy } from '@progress/kendo-data-query'
import { ReviewSetStateService, UserRights } from '@venio/data-access/review'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TooltipModule } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-tag-group-list',
  standalone: true,
  imports: [
    CommonModule,
    ButtonGroupModule,
    ButtonModule,
    GridModule,
    SvgLoaderDirective,
    BodyModule,
    UserGroupRightCheckDirective,
    IndicatorsModule,
    TooltipModule,
  ],
  templateUrl: './tag-group-list.component.html',
  styleUrls: ['./tag-group-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagGroupListComponent implements AfterViewInit, OnDestroy {
  public readonly toDestroy$ = new Subject<void>()

  public isTagGroupLoading = true

  public tagGroups: TagGroupModel[] = []

  public filter: CompositeFilterDescriptor = {
    logic: 'and',
    filters: [],
  }

  public gridView: GridDataResult

  public pageSize = 25

  public skip = 0

  public commonActionTypes = CommonActionTypes

  private reviewSetId: number

  public tagGroupSvgIcons = [
    {
      actionType: CommonActionTypes.DELETE,
      iconPath: 'assets/svg/icon-tagedit-delete.svg',
      allowedPermission: UserRights.DELETE_TAG,
    },
    {
      actionType: CommonActionTypes.EDIT,
      iconPath: 'assets/svg/icon-action-grid-pencil.svg',
      allowedPermission: UserRights.EDIT_EXISTING_TAG,
    },
  ]

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  constructor(
    private changeDetectorRef: ChangeDetectorRef,
    private tagsFacade: TagsFacade,
    private activatedRoute: ActivatedRoute,
    private reviewSetState: ReviewSetStateService
  ) {}

  public ngAfterViewInit(): void {
    this.#initReviewSetId()
    this.#selectTagGroupSuccessResponse()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #initReviewSetId(): void {
    this.reviewSetId = this.reviewSetState.isBatchReview()
      ? this.reviewSetState.reviewSetId()
      : 0
  }

  public tagGroupTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['tagGroupId'] as TrackByFunction<GridItem>

  public filterChange(filter: CompositeFilterDescriptor): void {
    this.filter = filter
    this.loadTagGroups()
  }

  public pageChange(event: PageChangeEvent): void {
    this.skip = event.skip
    this.loadTagGroups()
  }

  private loadTagGroups(): void {
    this.changeDetectorRef.markForCheck()
    const filteredTagGroups = filterBy(this.tagGroups, this.filter)
    this.gridView = {
      data: filteredTagGroups.slice(this.skip, this.skip + this.pageSize),
      total: filteredTagGroups.length,
    }
  }

  #selectTagGroupSuccessResponse(): void {
    this.tagsFacade.fetchTagGroup(this.projectId, this.reviewSetId)
    combineLatest([
      this.tagsFacade.selectTagGroupSuccessResponse$,
      this.tagsFacade.selectTagGroupErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.changeDetectorRef.markForCheck()
        this.tagGroups = success?.data || []
        this.isTagGroupLoading = false
        this.loadTagGroups()
      })
  }

  public onTagGroupAction(
    actionType: CommonActionTypes,
    selected: TagGroupModel
  ): void {
    this.tagsFacade.setSelectedTagGroup({
      selected,
      actionType,
    })
  }

  protected readonly CommonActionTypes = CommonActionTypes
}
