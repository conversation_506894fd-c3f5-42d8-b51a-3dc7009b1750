import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewEncapsulation,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject } from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

import { TreeListModule } from '@progress/kendo-angular-treelist'
import { FieldSelectorViewModel } from '@venio/data-access/review'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { differenceBy, union } from 'lodash'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
@Component({
  selector: 'lib-feature-field-selector',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    TreeListModule,
    SvgLoaderDirective,
    TooltipsModule,
    DialogsModule,
  ],
  templateUrl: './feature-field-selector.component.html',
  styleUrl: './feature-field-selector.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class FeatureFieldSelectorComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private toDestroy$: Subject<void> = new Subject<void>()

  private allFields: FieldSelectorViewModel[]

  @Input()
  public availableFields: FieldSelectorViewModel[]

  @Input()
  public visibleFields: FieldSelectorViewModel[]

  @Output()
  public readonly fieldSelectionChanged = new EventEmitter<
    FieldSelectorViewModel[]
  >()

  public selectedAvailableFields: any[]

  public selectedVisibleFields: any[] = []

  constructor(private cdr: ChangeDetectorRef, public dialogRef: DialogRef) {}

  public ngOnInit(): void {
    if (!Array.isArray(this.visibleFields)) this.visibleFields = []
    this.availableFields = differenceBy(
      this.availableFields,
      this.visibleFields,
      'displayFieldName'
    )
    this.allFields = union(this.availableFields, this.visibleFields)
  }

  public ngAfterViewInit(): void {
    // Set the placeholder for the filter inputs
    // This is a workaround for the issue with the filter inputs not having a placeholder
    // The QueryList<ElementRef<HTMLInputElement>> is not working as expected so we are using the document.querySelectorAll
    const filterInputs: NodeListOf<HTMLInputElement> =
      document.querySelectorAll('.k-filtercell .k-input')
    filterInputs.forEach((filterInput: HTMLInputElement) => {
      filterInput.setAttribute('placeholder', 'Search')
    })
  }

  public onActionClick(action: string): void {
    switch (action) {
      case 'move-right':
        this.transferItemsToVisible()
        break
      case 'move-left':
        this.transferItemsToAvailable()
        break
      case 'exchange':
        this.exchangeItems()
        break
      case 'move-all-left':
        this.transferAllItemsToAvailable()
        break
      case 'move-all-right':
        this.transferAllItemsToVisible()
        break
      case 'move-up':
        this.moveItemUp()
        break
      case 'move-down':
        this.moveItemDown()
        break
      case 'move-top':
        this.moveItemToTop()
        break
      case 'move-bottom':
        this.moveItemToBottom()
        break
    }

    this.visibleFields = this.visibleFields.map((f, index) => ({
      ...f,
      fieldDisplayOrder: index,
    }))
    this.fieldSelectionChanged.emit(this.visibleFields)
  }

  private transferItemsToVisible(): void {
    this.cdr.markForCheck()
    this.visibleFields = [
      ...(this.visibleFields || []),
      ...this.selectedAvailableFields.map((f) =>
        this.mapToFieldSelectorViewModel(f.itemKey)
      ),
    ]
    this.availableFields = this.availableFields.filter(
      (f) =>
        !this.selectedAvailableFields.some(
          (sf) => sf.itemKey === f.displayFieldName
        )
    )
    this.selectedAvailableFields = []
  }

  private transferItemsToAvailable(): void {
    this.cdr.markForCheck()
    this.availableFields = [
      ...this.availableFields,
      ...this.selectedVisibleFields.map((f) =>
        this.mapToFieldSelectorViewModel(f.itemKey)
      ),
    ]
    this.visibleFields = this.visibleFields.filter(
      (f) =>
        !this.selectedVisibleFields.some(
          (sf) => sf.itemKey === f.displayFieldName
        )
    )
    this.selectedVisibleFields = []
  }

  private exchangeItems(): void {
    this.cdr.markForCheck()
    const tempAvailableFields = [...this.availableFields]
    this.availableFields = [...this.visibleFields]
    this.visibleFields = [...tempAvailableFields]
  }

  private transferAllItemsToVisible(): void {
    this.cdr.markForCheck()
    this.visibleFields = [...this.visibleFields, ...this.availableFields]
    this.availableFields = []
  }

  private transferAllItemsToAvailable(): void {
    this.cdr.markForCheck()
    this.availableFields = [...this.availableFields, ...this.visibleFields]
    this.visibleFields = []
  }

  private moveItemUp(): void {
    this.cdr.markForCheck()
    const index = this.getIndexOfItem(true)
    const headItems = index > 0 ? this.visibleFields.slice(0, index - 1) : []
    const sliceIndex = index > 0 ? index - 1 : 0
    const tailItems = this.visibleFields
      .slice(sliceIndex)
      .filter(
        (f) =>
          !this.selectedVisibleFields.some(
            (sf) => sf.itemKey === f.displayFieldName
          )
      )

    this.visibleFields = [
      ...headItems,
      ...this.selectedVisibleFields.map((f) =>
        this.mapToFieldSelectorViewModel(f.itemKey)
      ),
      ...tailItems,
    ]
  }

  private moveItemDown(): void {
    this.cdr.markForCheck()
    const index = this.getIndexOfItem(false)
    const sliceIndex = index + 2
    const headItems = this.visibleFields
      .slice(0, sliceIndex)
      .filter(
        (f) =>
          !this.selectedVisibleFields.some(
            (sf) => sf.itemKey === f.displayFieldName
          )
      )

    const tailItems = this.visibleFields.slice(sliceIndex)

    this.visibleFields = [
      ...headItems,
      ...this.selectedVisibleFields.map((f) =>
        this.mapToFieldSelectorViewModel(f.itemKey)
      ),
      ...tailItems,
    ]
  }

  private moveItemToTop(): void {
    this.cdr.markForCheck()
    const headItems = this.selectedVisibleFields.map((f) =>
      this.mapToFieldSelectorViewModel(f.itemKey)
    )
    const tailItems = this.visibleFields.filter(
      (f) =>
        !this.selectedVisibleFields.some(
          (sf) => sf.itemKey === f.displayFieldName
        )
    )
    this.visibleFields = [...headItems, ...tailItems]
  }

  private moveItemToBottom(): void {
    this.cdr.markForCheck()
    const headItems = this.visibleFields.filter(
      (f) =>
        !this.selectedVisibleFields.some(
          (sf) => sf.itemKey === f.displayFieldName
        )
    )
    const tailItems = this.selectedVisibleFields.map((f) =>
      this.mapToFieldSelectorViewModel(f.itemKey)
    )
    this.visibleFields = [...headItems, ...tailItems]
  }

  private mapToFieldSelectorViewModel(
    displayFieldName: string
  ): FieldSelectorViewModel {
    return this.allFields.find((f) => f.displayFieldName === displayFieldName)
  }

  private getIndexOfItem(isFirst: boolean): number {
    let resultIndex = this.visibleFields.findIndex(
      (f) => f.displayFieldName === this.selectedVisibleFields[0].itemKey
    )
    this.selectedVisibleFields.forEach((svf) => {
      const index = this.visibleFields.findIndex(
        (f) => f.displayFieldName === svf.itemKey
      )
      if (isFirst && index < resultIndex) resultIndex = index
      else if (!isFirst && index > resultIndex) resultIndex = index
    })
    return resultIndex
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
