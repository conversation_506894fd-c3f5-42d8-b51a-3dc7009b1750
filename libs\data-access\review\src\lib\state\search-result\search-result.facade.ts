import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as SearchResultSelectors from './search-result.selectors'
import * as SearchResultActions from './search-result.actions'
import { map, Observable } from 'rxjs'
import {
  DocumentSearchScopeModel,
  SearchDocumentMetadata,
  SearchResponseData,
} from '../../models/interfaces'
import { storeDocumentTablePaging } from './search-result.actions'
import { NativePrefetchDocumentModel } from '@venio/shared/models/interfaces'

@Injectable()
export class SearchResultFacade {
  constructor(private readonly store: Store) {}

  public getSearchResults$ = this.store.pipe(
    select(SearchResultSelectors.getStateOfSearchResult('searchResults'))
  )

  public getAllSearchResults = this.store.pipe(
    select(SearchResultSelectors.selectAllSearchResults)
  )

  public getAllSearchResultEntities = this.store.pipe(
    select(SearchResultSelectors.selectSearchResultEntities)
  )

  public getSearchResultFileIds = this.store.pipe(
    select(SearchResultSelectors.selectSearchResultFileIds),
    map((fileIds) => fileIds?.map((fileId) => +fileId))
  )

  public getSearchResultFieldValues = this.store.pipe(
    select(SearchResultSelectors.getSearchResultFieldValues)
  )

  public readonly selectAreDocumentsLoading$ = this.store.pipe(
    select(SearchResultSelectors.getStateOfSearchResult('areDocumentsLoading'))
  )

  public getSeqNoByFileId = (fileId: number): Observable<number> =>
    this.store.pipe(select(SearchResultSelectors.getSeqNoByDocumentId(fileId)))

  public getSeqNoToFileIdMap = this.store.pipe(
    select(SearchResultSelectors.getSeqNoToFileIdMap)
  )

  public getSearchResultEntityByFileId = (
    id: number
  ): Observable<SearchResponseData> =>
    this.store.pipe(select(SearchResultSelectors.getSearchResultByFileId(id)))

  public getSearchResultEntityByFileIds = (
    ids: number[]
  ): Observable<SearchResponseData[]> =>
    this.store.pipe(select(SearchResultSelectors.getSearchResultByFileIds(ids)))

  public getSearchResultFieldValuesByFileId = (
    id: number
  ): Observable<SearchDocumentMetadata[]> =>
    this.store.pipe(
      select(SearchResultSelectors.getSearchResultFieldValuesByFileId(id))
    )

  public getIsDocumentReviewedByFileId = (id: number): Observable<boolean> =>
    this.store.pipe(
      select(SearchResultSelectors.getIsDocumentReviewedByFileId(id))
    )
  public getCurrentDocumentTablePaging$ = this.store.pipe(
    select(SearchResultSelectors.getStateOfSearchResult('documentPaging'))
  )

  public getDocumentExistsInSearchScope$ = this.store.pipe(
    select(
      SearchResultSelectors.getStateOfSearchResult('currentDocumentSearchScope')
    )
  )

  public getHtmlConversionElegibleFileIds = (
    payload: NativePrefetchDocumentModel
  ): Observable<number[]> =>
    this.store.pipe(
      select(
        SearchResultSelectors.getHtmlConversionEligibleDocumentIds(payload)
      )
    )

  public fetchDocumentTablePage = (): void => {
    // this.store.dispatch(
    //   SearchResultActions.fetchDocumentTablePage({
    //     payload: {
    //       resetSelectionItem: '', // setting to empty string to set none as selected documents
    //     },
    //   })
    // )
  }

  public storeDocumentTablePaging(page: number, pageSize: number): void {
    this.store.dispatch(
      storeDocumentTablePaging({
        payload: {
          pageNumber: page,
          resetSelectionItem: 'first',
          pageSize: pageSize,
        },
      })
    )
  }

  public setSearchResults = (searchResults: SearchResponseData[]): void => {
    this.store.dispatch(
      SearchResultActions.setSearchResults({
        payload: { searchResults },
      })
    )
  }

  public resetSearchResults = (): void => {
    this.store.dispatch(SearchResultActions.resetSearchResults())
  }

  public setDocumentExistsInSearchScope = (
    currentDocumentSearchScope: DocumentSearchScopeModel
  ): void => {
    this.store.dispatch(
      SearchResultActions.setDocumentExistsInSearchScope({
        currentDocumentSearchScope,
      })
    )
  }
  public updateSearchResult = (searchResultData: SearchResponseData): void => {
    this.store.dispatch(
      SearchResultActions.updateSearchResult({
        update: {
          id: searchResultData.fileId,
          changes: { metadata: searchResultData.metadata },
        },
      })
    )
  }

  public updateManySearchResult = (
    searchResultData: SearchResponseData[]
  ): void => {
    const updates = searchResultData.map((item) => ({
      id: item.fileId,
      changes: {
        metadata: item.metadata,
      },
    }))
    this.store.dispatch(SearchResultActions.updateManySearchResult({ updates }))
  }
}
