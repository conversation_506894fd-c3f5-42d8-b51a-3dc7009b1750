<div class="t-w-full">
  <form [formGroup]="slipSheetOption">
    <kendo-label
      class="t-py-1 t-font-semibold"
      text="Slipsheet for document without image"></kendo-label>
    <div class="">
      <div class="t-flex t-flex-col t-px-1">
        <div class="t-pt-2 t-flex t-items-center">
          <input
            id="slipsheet"
            size="small"
            type="radio"
            value="ENABLE_SLIPSHEET"
            formControlName="slipsheetSettings"
            class=""
            kendoRadioButton
            #slipsheetRadio />
          <kendo-label
            for="slipsheet"
            class="t-k-radio-label t-px-[5px] t-relative"
            [ngClass]="{
              't-text-primary t-font-medium':
                slipSheetOption.get('slipsheetSettings')?.value ===
                  'ENABLE_SLIPSHEET' && !slipsheetRadio.disabled
            }"
            text="Slipsheet text"></kendo-label>
        </div>
        <div>
          <div class="t-w-full t-flex t-flex-row t-mt-3">
            <!-- Slipsheet text area div-->
            <div
              class="t-bg-white t-bg-no-repeat t-border-[1px] t-border-solid t-border-[#BEBEBE] t-rounded-[4px] t-opacity-100 t-p-3 t-flex t-flex-col t-w-full"
              [ngClass]="{
                't-pointer-events-none t-cursor-not-allowed t-opacity-50':
                  this.slipSheetOption.get('slipsheetSettings').value !==
                  'ENABLE_SLIPSHEET'
              }">
              <div class="t-px-2 t-flex t-flex-row t-gap-x-2">
                <form
                  [formGroup]="fontSelectionForm"
                  class="t-flex t-flex-row t-gap-x-2 t-w-[310px]">
                  <kendo-dropdownlist
                    class="t-w-1/2"
                    id="fontName"
                    [data]="fontNames"
                    formControlName="fontName"
                    [popupSettings]="{ width: 225 }"
                    data-qa="slipsheet-font-name-dropdown"></kendo-dropdownlist>

                  <kendo-dropdownlist
                    class="t-w-1/4"
                    id="fontSize"
                    [data]="fontSizes"
                    data-qa="slipsheet-font-size-dropdown"
                    formControlName="fontSize"></kendo-dropdownlist>

                  <kendo-buttongroup [selection]="selectionMode">
                    <button
                      kendoButton
                      [svgIcon]="boldSVG"
                      title="Bold"
                      [value]="'Bold'"
                      [ngClass]="{
                        'k-selected k-focus': isSelected('Bold')
                      }"
                      (click)="onFontStyleChange('Bold')"></button>
                    <button
                      kendoButton
                      [svgIcon]="italicSVG"
                      title="Italic"
                      [value]="'Italic'"
                      [ngClass]="{
                        'k-selected k-focus': isSelected('Italic')
                      }"
                      (click)="onFontStyleChange('Italic')"></button>
                  </kendo-buttongroup>
                </form>

                <kendo-dropdownlist
                  class="t-w-[95px]"
                  id="placeHolderPosition"
                  formControlName="placeHolderPosition"
                  [popupSettings]="{ width: 225 }"
                  [data]="stampLocationList">
                </kendo-dropdownlist>
                <kendo-dropdownlist
                  id="slipsheetField"
                  #slipsheetField
                  [data]="slipsheetFields()"
                  textField="displayFieldName"
                  valueField="internalFieldName"
                  [defaultItem]="defaultFieldItem"
                  (valueChange)="fieldSelectionChange($event)"
                  class="t-w-[120px]"
                  data-qa="slipsheet-field-dropdownlist"
                  [popupSettings]="{ width: 250 }"
                  [itemDisabled]="disableDefaultItem"></kendo-dropdownlist>
              </div>
              <hr class="t-my-2 t-border-[#0000001F]" />

              <div>
                <kendo-textarea
                  [rows]="3"
                  resizable="vertical"
                  class="textarea-min-height t-border-0 t-border-transparent"
                  id="slipSheet-text"
                  resizable="none"
                  formControlName="slipSheetText"></kendo-textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="t-mr-4 t-pt-2 t-flex t-items-center" *ngIf="showButton">
          <input
            id="redaction"
            size="small"
            type="radio"
            value="PUT_ORIGINAL_IMAGE_WITH_REDACTION"
            formControlName="slipsheetSettings"
            class=""
            kendoRadioButton
            #redactionRadio />
          <kendo-label
            for="redaction"
            class="t-k-radio-label t-px-[5px] t-relative"
            [ngClass]="{
              't-text-primary t-font-medium':
                slipSheetOption.get('slipsheetSettings')?.value ===
                  'PUT_ORIGINAL_IMAGE_WITH_REDACTION' &&
                !redactionRadio.disabled
            }"
            text="Put original image with Redaction"></kendo-label>
        </div>
        <div class="t-mr-4 t-pt-2 t-flex t-items-center" *ngIf="showButton">
          <input
            id="no-redaction"
            size="small"
            type="radio"
            value="PUT_ORIGINAL_IMAGE_WITHOUT_REDACTION"
            formControlName="slipsheetSettings"
            class=""
            kendoRadioButton
            #noRedactionRadio />
          <kendo-label
            for="no-redaction"
            class="t-k-radio-label t-px-[5px] t-relative"
            [ngClass]="{
              't-text-primary t-font-medium':
                slipSheetOption.get('slipsheetSettings')?.value ===
                  'PUT_ORIGINAL_IMAGE_WITHOUT_REDACTION' &&
                !noRedactionRadio.disabled
            }"
            text="Put original image without Redaction"></kendo-label>
        </div>
        <div class="t-mr-4 t-pt-2 t-flex t-items-center">
          <input
            id="skip"
            size="small"
            type="radio"
            value="SKIP"
            formControlName="slipsheetSettings"
            kendoRadioButton
            #skipRadio />
          <kendo-label
            for="skip"
            class="t-k-radio-label t-px-[5px] t-relative"
            [ngClass]="{
              't-text-primary t-font-medium':
                slipSheetOption.get('slipsheetSettings')?.value === 'SKIP' &&
                !skipRadio.disabled
            }"
            text="Skip"></kendo-label>
        </div>
      </div>
    </div>
  </form>
</div>
