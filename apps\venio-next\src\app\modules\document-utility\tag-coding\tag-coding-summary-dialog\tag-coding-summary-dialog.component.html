<div class="t-flex t-flex-col">
  <kendo-dialog-titlebar (close)="close('cancel')">
    <div class="t-flex t-w-[65%] t-justify-between">
      <div class="t-block">{{ dialogTitle }}</div>
      <div class="t-block">
        <div
          #appendNotification
          class="v-append-notification-container t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-8 t-w-[420px]"></div>
      </div>
    </div>
  </kendo-dialog-titlebar>

  <kendo-tabstrip (tabSelect)="onSelect($event)" class="v-custom-tabstrip">
    <kendo-tabstrip-tab
      title="Tags"
      [selected]="isSelected(0)"
      *ngIf="taggedItems?.length > 0 || untaggedItems?.length > 0">
      <ng-template kendoTabContent>
        <div
          class="t-flex t-justify-end t-w-full t-items-end t-flex-col t-mt-4">
          <label
            class="t-flex t-items-center t-min-h-[2.25rem]"
            [ngClass]="{ 't-text-primary t-font-medium': showHiddenColumns }">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              name="status"
              size="small"
              (change)="toggleHiddenColumns($event)" />
            <span class="t-pl-2 t-tracking-tight">Show Detail Count</span>
          </label>
        </div>
        <div
          class="t-flex t-flex-col t-w-full t-mt-3 t-mb-4 t-relative t-overflow-hidden">
          <div *ngIf="taggedItems?.length > 0" class="t-text-base t-m-2">
            <kendo-label text="Tagged Document Summary"></kendo-label>
          </div>
          <div class="t-flex">
            <kendo-grid
              class="t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
              [kendoGridBinding]="taggedItems"
              *ngIf="taggedItems?.length > 0"
              [height]="getGridHeight()"
              [sortable]="true"
              [groupable]="false"
              [reorderable]="true"
              [resizable]="true">
              <kendo-grid-column
                field="tagName"
                title="Tag Name"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Tag Name">
                    Tag Name
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="selectedDocCount"
                title="Selected"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Selected">
                    Selected
                  </span>
                </ng-template>
              </kendo-grid-column>

              @if(showHiddenColumns){
              <!-- hidden columns starts -->
              <kendo-grid-column
                field="duplicateDocCount"
                title="Duplicate Propagation"
                headerClass="t-text-primary t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Duplicate Propagation">
                    Duplicate Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="emailThreadCount"
                title="Email Thread Propagation"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Email Thread Propagation">
                    Email Thread Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="parentFileCount"
                title="PC Propagation"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="PC Propagation">
                    PC Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="nddFileCount"
                title="NDD Propagation"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="NDD Propagation">
                    NDD Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>
              <!-- hidden columns end -->
              } @else {
              <kendo-grid-column
                title="Total Propagated Files"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Total Propagated Files">
                    Total Propagated Files
                  </span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  {{ dataItem.totalCount - dataItem.selectedDocCount }}
                </ng-template>
              </kendo-grid-column>
              }
              <kendo-grid-column
                field="totalCount"
                title="Tagged"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Tagged">
                    Tagged
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="Tagged"
                title=""
                headerClass="t-text-primary"
                [width]="70"
                [minResizableWidth]="70">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span
                    class="hover:t-cursor-pointer hover:t-text-[var(--kendo-custom-secondary-100)] t-flex t-w-full t-mr-7"
                    (click)="popoverShow(firstAnchor, $event, dataItem.tagId)">
                    <kendo-svg-icon [icon]="icons.eyeIcon"></kendo-svg-icon>
                  </span>
                </ng-template>
              </kendo-grid-column>
            </kendo-grid>
          </div>
          <div *ngIf="untaggedItems?.length > 0" class="t-text-base t-m-2">
            <kendo-label text="UnTagged Document Summary"></kendo-label>
          </div>
          <div class="t-flex">
            <kendo-grid
              class="t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
              [kendoGridBinding]="untaggedItems"
              *ngIf="untaggedItems?.length > 0"
              [height]="getGridHeight()"
              [sortable]="true"
              [groupable]="false"
              [reorderable]="true"
              [resizable]="true">
              <kendo-grid-column
                field="tagName"
                title="Tag Name"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Tag Name">
                    Tag Name
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="selectedDocCount"
                title="Selected"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Selected">
                    Selected
                  </span>
                </ng-template>
              </kendo-grid-column>

              @if(showHiddenColumns){
              <!-- hidden columns starts -->
              <kendo-grid-column
                field="duplicateDocCount"
                title="Duplicate Propagation"
                headerClass="t-text-primary t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Duplicate Propagation">
                    Duplicate Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>

              <kendo-grid-column
                field="emailThreadCount"
                title="Email Thread Propagation"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Email Thread Propagation">
                    Email Thread Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="parentFileCount"
                title="PC Propagation"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="PC Propagation">
                    PC Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="nddFileCount"
                title="NDD Propagation"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="NDD Propagation">
                    NDD Propagation
                  </span>
                </ng-template>
              </kendo-grid-column>
              <!-- hidden columns end -->
              } @else {
              <kendo-grid-column
                title="Total Propagated Files"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="Total Propagated Files">
                    Total Propagated Files
                  </span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  {{ dataItem.totalCount - dataItem.selectedDocCount }}
                </ng-template>
              </kendo-grid-column>
              }
              <kendo-grid-column
                field="totalCount"
                title="UnTagged"
                headerClass="t-text-primary">
                <ng-template kendoGridHeaderTemplate let-column>
                  <span
                    kendoTooltip
                    class="t-text-ellipsis t-overflow-hidden"
                    title="UnTagged">
                    UnTagged
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                title=""
                headerClass="t-text-primary"
                [width]="70"
                [minResizableWidth]="70">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span
                    class="hover:t-cursor-pointer hover:t-text-[var(--kendo-custom-secondary-100)] t-flex t-w-full t-mr-7"
                    (click)="popoverShow(firstAnchor, $event, dataItem.tagId)">
                    <kendo-svg-icon [icon]="icons.eyeIcon"> </kendo-svg-icon>
                  </span>
                </ng-template>
              </kendo-grid-column>
            </kendo-grid>
          </div>
        </div>

        <!-- Popover Starts here -->

        <div
          kendoPopoverContainer
          [popover]="info"
          filter=".has-popover"
          class="wrapper t-fixed"
          #container="kendoPopoverContainer"
          showOn="none"
          [style.top.px]="popoverPosition.top"
          [style.left.px]="popoverPosition.left">
          <span #firstAnchor></span>
        </div>

        <kendo-popover #info position="left" [width]="480">
          <ng-template kendoPopoverTitleTemplate>
            <span class="t-text-[#54595E] t-font-semibold t-text-xl">
              Tag Propagation
            </span>
            <button
              kendoButton
              rounded="full"
              class="t-bg-[#ED7425] t-absolute t-right-[30px] t-text-white t-w-[17px] t-h-[17px]"
              fillMode="clear"
              (click)="popoverHide()"
              [svgIcon]="icons.closeIcon"
              title="Cart"></button>
          </ng-template>
          <ng-template kendoPopoverBodyTemplate>
            <div class="t-flex t-w-full t-flex-col t-gap-2 t-p-4 t-pt-0">
              <div class="t-flex t-flex-col t-gap-2 t-w-full">
                <div class="t-text-primary t-font-semibold">
                  <span>Applied Tag Rules</span>
                </div>
                <div class="t-flex t-flex-col t-bg-[#F7F7F7] t-p-4 t-w-full">
                  <ol
                    class="t-list-decimal t-text-[#5E6366] t-w-full t-pl-4 t-flex t-flex-col t-gap-2">
                    <li>{{ duplicateTagRuleText }}</li>
                    <li>{{ parentChildTagRuleText }}</li>
                    <li>{{ emailThreadTagRuleText }}</li>
                    <li>{{ nddTagRuleText }}</li>
                  </ol>
                </div>
              </div>

              <div class="t-flex t-w-full t-flex-col t-gap-2 t-mt-3">
                <div class="t-text-primary t-font-semibold">
                  <span>Current Search Results Options</span>
                </div>
                <div class="t-flex t-flex-col t-w-full t-bg-[#F7F7F7] t-p-4">
                  <ol
                    class="t-list-decimal t-pl-4 t-text-[#5E6366] t-w-full t-flex t-flex-col t-gap-2">
                    <li>{{ searchPCOptionText }}</li>
                    <li>{{ searchDuplicationOptionText }}</li>
                  </ol>
                </div>
              </div>
            </div>
          </ng-template>
        </kendo-popover>

        <!-- end -->
      </ng-template>
    </kendo-tabstrip-tab>
    <kendo-tabstrip-tab
      title="Coding"
      *ngIf="codingItems?.length > 0"
      [selected]="isSelected(1)">
      <ng-template kendoTabContent>
        <div class="t-flex t-flex-col t-w-full t-mt-4">
          <kendo-grid
            class="t-border-l-0 t-border-r-0 t-mt-4"
            [kendoGridBinding]="codingItems"
            kendoGridSelectBy="id"
            [height]="400"
            [sortable]="true"
            [groupable]="false"
            [reorderable]="true"
            [resizable]="true">
            <kendo-grid-column
              [width]="240"
              field="fieldDisplayName"
              title="Field Name"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate let-column>
                <span
                  kendoTooltip
                  class="t-text-ellipsis t-overflow-hidden"
                  title="Field Name">
                  Field Name
                </span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              [width]="240"
              field="oldValue"
              title="Previous Value"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate let-column>
                <span
                  kendoTooltip
                  class="t-text-ellipsis t-overflow-hidden"
                  title="Previous Value">
                  Previous Value
                </span>
              </ng-template>
              <ng-template
                kendoGridCellTemplate
                let-dataItem
                let-rowIndex="rowIndex">
                {{ isBulkTagCoding ? '' : dataItem.oldValue }}
              </ng-template></kendo-grid-column
            >
            <kendo-grid-column
              field="newValue"
              title="Current Value"
              headerClass="t-text-primary">
              <ng-template kendoGridHeaderTemplate let-column>
                <span
                  kendoTooltip
                  class="t-text-ellipsis t-overflow-hidden"
                  title="Current Value">
                  Current Value
                </span>
              </ng-template>
              <ng-template
                kendoGridCellTemplate
                let-dataItem
                let-rowIndex="rowIndex">
                {{ isBulkTagCoding ? '' : dataItem.newValue }}
              </ng-template>
            </kendo-grid-column>
          </kendo-grid>
        </div>
      </ng-template>
    </kendo-tabstrip-tab>
  </kendo-tabstrip>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline"
        data-qa="cancel-button">
        CLOSE
      </button>
    </div>
  </kendo-dialog-actions>
</div>
