import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { catchError, from, map, mergeMap, switchMap } from 'rxjs'
import { CaseConvertorService } from '@venio/util/utilities'
import * as CaseInfo from './case-info.actions'
import { CaseInfoService } from '../../services/case-info.service'
import { CaseModel, Project } from '../../models/interfaces'
import { ResponseModel } from '@venio/shared/models/interfaces'

@Injectable()
export class CaseInfoEffects {
  constructor(
    private actions$: Actions,
    private caseInfoService: CaseInfoService
  ) {}

  public fetchProjectList = createEffect(() =>
    this.actions$.pipe(
      ofType(CaseInfo.fetchProjectList),
      mergeMap(() =>
        this.caseInfoService.fetchProjectList$().pipe(
          switchMap((response: any) => {
            const camelCaseConvertorService = new CaseConvertorService()
            return from(
              camelCaseConvertorService.convertToCase<Project[]>(
                response,
                'camelCase'
              )
            )
          }),
          map((projectList: Project[]) =>
            CaseInfo.fetchProjectListSuccess({
              projects: projectList,
            })
          ),
          catchError((error: unknown) => {
            throw error
          })
        )
      )
    )
  )

  public fetchExternalUserProjectList = createEffect(() =>
    this.actions$.pipe(
      ofType(CaseInfo.fetchExternalUserProjectList),
      mergeMap(({ projectId }) =>
        this.caseInfoService.fetchExternalUserProjectList$(projectId).pipe(
          switchMap((response: any) => {
            const camelCaseConvertorService = new CaseConvertorService()
            const convertedData = camelCaseConvertorService.convertToCase<
              Project[]
            >(response, 'camelCase')
            return from(convertedData)
          }),
          map((projectList: Project[]) =>
            CaseInfo.fetchProjectListSuccess({
              projects: projectList,
            })
          ),
          catchError((error: unknown) => {
            throw error
          })
        )
      )
    )
  )

  public fetchCaseInfoById = createEffect(() =>
    this.actions$.pipe(
      ofType(CaseInfo.fetchCaseInfoByIdAction),
      mergeMap(({ projectId }) =>
        this.caseInfoService.fetchCaseInfoById$(projectId).pipe(
          switchMap((response: ResponseModel) => {
            const camelCaseConvertorService = new CaseConvertorService()
            return from(
              camelCaseConvertorService.convertToCase<CaseModel>(
                response.data,
                'camelCase'
              )
            )
          }),
          map((caseInfo: any) =>
            CaseInfo.fetchCaseInfoByIdSuccess({
              caseInfo,
            })
          ),
          catchError((error: unknown) => {
            throw error
          })
        )
      )
    )
  )
}
