<kendo-dialog-titlebar (close)="close('')">
  <div>
    {{ dialogTitle() }}
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-flex-col t-h-full t-overflow-hidden">
  <div
    [formGroup]="folderingForm"
    class="t-flex t-w-full t-mt-2 t-flex-col t-gap-3 v-custom-grey-bg">
    <div class="t-flex t-w-full t-flex-wrap t-gap-3">
      <div
        class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
        <label
          class="k-checkbox-label t-font-bold"
          for="documentFamilies"
          [ngClass]="{
            't-text-primary t-font-medium':
              folderingForm.get('documentFamilies').value &&
              !folderingForm.get('documentFamilies').disabled
          }"
          >Document families</label
        >
        <input
          type="checkbox"
          id="documentFamilies"
          data-qa="documentFamilies"
          kendoCheckBox
          formControlName="documentFamilies" />
      </div>

      <div
        class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
        <label
          class="k-checkbox-label t-font-bold"
          for="documentThreads"
          [ngClass]="{
            't-text-primary t-font-medium':
              folderingForm.get('documentThreads').value &&
              !folderingForm.get('documentThreads').disabled
          }"
          >Discussion threads</label
        >
        <input
          type="checkbox"
          id="documentThreads"
          data-qa="documentThreads"
          kendoCheckBox
          formControlName="documentThreads" />
      </div>
    </div>

    <div class="t-flex mt-2">
      <div class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
        <kendo-label
          for="eventComments"
          class="t-text-xs t-uppercase t-tracking-widest"
          [ngClass]="{
            't-text-error': folderingForm.get('eventComments').invalid
          }"
          >Comments</kendo-label
        >
        <kendo-textarea
          id="eventComments"
          formControlName="eventComments"
          placeholder="Event Comment"
          [rows]="2"
          resizable="vertical"></kendo-textarea>
      </div>
    </div>

    <div class="t-flex t-gap-2">
      <div class="t-flex t-flex-0 t-basis-1/2 t-gap-1 t-flex-col">
        <div class="t-flex t-items-center t-gap-1">
          <kendo-dropdownlist
            [defaultItem]="defaultItemFolder"
            formControlName="folderType"
            data-qa="folderType"
            [data]="folderTypeList"
            textField="folderType"
            valueField="folderTypeId">
          </kendo-dropdownlist>
          <span class="t-text-error t-self-start">*</span>
        </div>
        <div
          class="t-text-error t-mb-2 t-w-full"
          *ngIf="
            showValidationMessage &&
            (folderingForm.get('folderType')?.invalid ||
              folderingForm.get('folderType')?.value?.folderTypeId === -1)
          ">
          Please Select Type
        </div>
      </div>
      <div
        class="t-flex t-flex-0 t-basis-1/2 t-gap-1 t-flex-col"
        *ngIf="isSelectedCopyFolder || isSelectedRemoveFolder">
        <kendo-textbox
          formControlName="selectedFolder"
          id="selectedFolder"
          placeholder="Folder name"
          (keydown)="$event.preventDefault()"></kendo-textbox>
      </div>

      <div
        *ngIf="isSelectedMoveFolder"
        class="t-flex t-flex-0 t-basis-1/2 t-gap-1 t-flex-col">
        <kendo-textbox
          formControlName="fromFolder"
          id="fromFolder"
          placeholder="From Folder"
          (focus)="onFocusFromFolder()"
          (keydown)="$event.preventDefault()"></kendo-textbox>
      </div>

      <div
        *ngIf="isSelectedMoveFolder"
        class="t-flex t-flex-0 t-basis-1/2 t-gap-1 t-flex-col">
        <kendo-textbox
          formControlName="toFolder"
          id="toFolder"
          placeholder="To Folder"
          (focus)="onFocusToFolder()"
          (keydown)="$event.preventDefault()"></kendo-textbox>
      </div>
    </div>

    <div class="t-flex" *ngIf="isSelectedMoveFolder || isSelectedRemoveFolder">
      <div
        class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
        <label class="k-checkbox-label" for="includeSubFolders"
          >Include sub Folders</label
        >
        <input
          type="checkbox"
          id="includeSubFolders"
          formControlName="includeSubFolders"
          data-qa="includeSubFolders"
          kendoCheckBox />
      </div>
    </div>

    <div class="t-flex">
      <!-- tree list -->

      <kendo-treelist
        [kendoTreeListFlatBinding]="folders"
        idField="id"
        parentIdField="parentId"
        kendoTreeListExpandable
        [initiallyExpanded]="false"
        kendoTreeListSelectable
        [selectable]="settings"
        [(selectedItems)]="selected"
        class="v-custom-folder-tree-list v-custom-tagtree t-mb-5"
        [columnMenu]="false"
        [sortable]="true"
        [resizable]="true"
        [navigable]="true"
        [pageable]="false"
        (selectionChange)="onSelectionChange($event)"
        [rowHeight]="36"
        [pageSize]="30"
        scrollable="virtual"
        [trackBy]="trackByFolderFn">
        <kendo-treelist-column
          class="!t-px-0"
          headerClass="t-text-primary v-custom-tagheader"
          [expandable]="true"
          field="folderName"
          title="Folder"
          [width]="350">
          <ng-template kendoTreeListCellTemplate let-dataItem>
            <span
              class="t-inline-block t-absolute t-mt-[2px] t-cursor-pointer"
              venioSvgLoader
              svgUrl="assets/svg/icon-folder-outline.svg"
              height="1.1rem"
              width="1.1rem">
              <kendo-loader size="small" />
            </span>
            <div
              class="t-inline-block t-absolute t-ml-5 t-cursor-pointer t-w-[calc(100%_-_57px)]">
              {{ dataItem.folderName }}
            </div>
          </ng-template>
        </kendo-treelist-column>
      </kendo-treelist>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="col-md-9">
    <span
      class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-2"
      *ngIf="formMessage"
      >{{ formMessage }}</span
    >
  </div>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      [disabled]="!isFolderVisible"
      (click)="save()"
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      {{
        folderingForm.controls.selectedFolderType.value === 0 ||
        folderingForm.controls.selectedFolderType.value === 1
          ? 'SEND'
          : 'REMOVE'
      }}
    </button>
    <button
      kendoButton
      (click)="close('yes')"
      themeColor="dark"
      fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
