<kendo-dialog-titlebar (close)="close()">
  <div>
    {{ dialogTitle }}
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-flex-col">
  <div class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
    <div class="t-flex t-w-full t-flex-wrap t-gap-3">
      <div showHints="always" class="t-flex t-flex-1 t-gap-1 t-flex-col">
        <div
          class="t-flex t-gap-3 t-mt-1 t-flex-col t-gap-4"
          [formGroup]="saveSearchFormGroup">
          <div class="t-flex t-flex-col t-gap-1">
            <kendo-label
              for="save-search-name"
              class="t-text-xs t-uppercase t-tracking-widest">
              Search Name <span class="t-text-error">*</span>
            </kendo-label>

            <kendo-textbox
              placeholder="Enter a Search Name"
              formControlName="searchName"
              id="save-search-name"
              data-qa="save-search-name"></kendo-textbox>
            <div
              *ngIf="displayMessage?.searchName"
              class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-3">
              {{ displayMessage?.searchName }}
            </div>
          </div>

          <div class="t-flex t-gap-2 t-flex-row-reverse t-justify-end">
            <label
              class="k-checkbox-label"
              for="apply-auto-tag"
              [ngClass]="{
                't-text-primary !t-font-medium':
                  saveSearchFormGroup.get('applyAutoTagBasedOnSearchTerm')
                    ?.value &&
                  !saveSearchFormGroup.get('applyAutoTagBasedOnSearchTerm')
                    ?.disabled
              }"
              >Create and apply tags using search terms as tag name</label
            >
            <input
              type="checkbox"
              formControlName="applyAutoTagBasedOnSearchTerm"
              id="apply-auto-tag"
              data-qa="apply-auto-tag"
              (change)="isCreateApplyTagsChecked = !isCreateApplyTagsChecked"
              kendoCheckBox />
          </div>

          <div class="t-flex t-gap-2 t-flex-row-reverse t-justify-end t-pl-3">
            <label
              class="k-checkbox-label"
              for="use-existing-tag"
              [class.label-disabled]="isCreateApplyTagsChecked"
              [ngClass]="{
                't-text-primary !t-font-medium':
                  saveSearchFormGroup.get('useExistingTag')?.value &&
                  !saveSearchFormGroup.get('useExistingTag')?.disabled
              }"
              >Use existing tag structure from search</label
            >
            <input
              type="checkbox"
              formControlName="useExistingTag"
              id="use-existing-tag"
              data-qa="use-existing-tag"
              (change)="
                isUseExistingTagStructureChecked =
                  !isUseExistingTagStructureChecked
              "
              kendoCheckBox />
          </div>

          <div class="t-flex t-flex-col t-gap-1 t-pl-3">
            <kendo-multicolumncombobox
              [data]="savedSearchTagGroups"
              [disabled]="!saveSearchFormGroup.get('useExistingTag')?.value"
              textField="tagGroupName"
              valueField="tagGroupId"
              placeholder="Select a value..."
              [filterable]="true"
              (filterChange)="handleFilterChange($event)"
              (selectionChange)="onSelectionChanged($event)"
              [valuePrimitive]="true">
              <kendo-combobox-column
                field="tagGroupName"
                title="Tag Name"
                [width]="150">
              </kendo-combobox-column>
              <kendo-combobox-column
                field="searchName"
                title="Search Name"
                [width]="150">
              </kendo-combobox-column>
            </kendo-multicolumncombobox>
            <div
              class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-3"
              *ngIf="displayMessage?.tagGroupIdOfExistingSavedSearch">
              {{ displayMessage?.tagGroupIdOfExistingSavedSearch }}
            </div>
          </div>

          <div class="t-flex t-gap-2 t-mt-2 t-flex-row-reverse t-justify-end">
            <label
              class="k-checkbox-label"
              for="save-custom-field"
              [ngClass]="{
                't-text-primary t-font-medium':
                  saveSearchFormGroup.get('saveOnCustomField')?.value &&
                  !saveSearchFormGroup.get('saveOnCustomField')?.disabled
              }"
              >Save search terms in a custom field</label
            >
            <input
              type="checkbox"
              formControlName="saveOnCustomField"
              id="save-custom-field"
              data-qa="save-custom-field"
              (change)="newCustomFieldDisabled = !newCustomFieldDisabled"
              kendoCheckBox />
          </div>

          <div class="t-flex t-gap-3 t-pl-3 t-flex-col t-gap-4">
            <div class="t-flex">
              <input
                type="radio"
                formControlName="isNewCustomField"
                #newCustomFieldYes
                [value]="true"
                kendoRadioButton
                [disabled]="newCustomFieldDisabled"
                (change)="isUseExistingCustomFieldChecked = true"
                name="isNewCustomField" />
              <kendo-label
                class="t-ml-2"
                [for]="newCustomFieldYes"
                [class.label-disabled]="newCustomFieldDisabled"
                [ngClass]="{
                  't-text-primary t-font-medium':
                    saveSearchFormGroup.get('isNewCustomField')?.value ===
                      true &&
                    !saveSearchFormGroup.get('isNewCustomField')?.disabled
                }"
                text="Create a new custom field"></kendo-label>
            </div>

            <div class="t-flex">
              <input
                type="radio"
                formControlName="isNewCustomField"
                #newCustomFieldNo
                [value]="false"
                kendoRadioButton
                [disabled]="newCustomFieldDisabled"
                (change)="isUseExistingCustomFieldChecked = false"
                name="isNewCustomField" />
              <kendo-label
                class="t-ml-2"
                [for]="newCustomFieldNo"
                [class.label-disabled]="newCustomFieldDisabled"
                [ngClass]="{
                  't-text-primary t-font-medium':
                    saveSearchFormGroup.get('isNewCustomField')?.value ===
                      false &&
                    !saveSearchFormGroup.get('isNewCustomField')?.disabled
                }"
                text=" Use existing custom field"></kendo-label>
            </div>
          </div>

          <div class="t-flex t-flex-col t-gap-1 t-pl-3">
            <kendo-dropdownlist
              #customField
              formControlName="customFieldId"
              [disabled]="isUseExistingCustomFieldChecked"
              [defaultItem]="defaultSavedCustomValue"
              [data]="customFields"
              textField="displayFieldName"
              valueField="venioFieldId"
              placeholder="Select saved custom field"
              [valuePrimitive]="true">
            </kendo-dropdownlist>
            <div
              class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-3"
              *ngIf="displayMessage?.customFieldId">
              {{ displayMessage?.customFieldId }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="saveSearch()"
      [disabled]="isSearchSaving | async"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button">
      <kendo-loader *ngIf="isSearchSaving | async" size="small"> </kendo-loader>
      SAVE
    </button>
    <button
      kendoButton
      (click)="close()"
      fillMode="outline"
      themeColor="dark"
      data-qa="cancel-button">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
