<div class="t-flex t-flex-row t-gap-4">
  <kendo-multiselect
    [filterable]="true"
    [clearButton]="false"
    [virtual]="true"
    [listHeight]="300"
    [checkboxes]="true"
    [autoClose]="false"
    [tagMapper]="tagMapper"
    [value]="selectedLegalHold()"
    #userSelection
    (filterChange)="filterUsers($event)"
    (valueChange)="legalHoldSelectionChange($event)"
    class="t-min-w-[18rem] t-max-w-[18rem]"
    [data]="filteredLegalHold()"
    (removeTag)="removeTag($event)"
    [valuePrimitive]="true"
    valueField="holdId"
    textField="clientMatterName">
    <ng-template kendoMultiSelectHeaderTemplate let-dataItem>
      <div
        class="t-flex t-p-2 t-items-center t-h-full t-w-full t-relative t-z-50"
        (click)="handleAllLegalHoldClick($event, allUser)">
        <input
          [checked]="isAllLegalHoldChecked()"
          (click)="handleAllLegalHoldClick($event, allUser)"
          (change)="allLegalHoldSelectionChange($event.target['checked'])"
          type="checkbox"
          #allUser
          id="all-legal-hold"
          kendoCheckBox />
        <kendo-label
          (click)="handleAllLegalHoldClick($event, allUser)"
          class="k-checkbox-label t-w-full t-h-full"
          for="all-legal-hold"
          text="All Legal Hold"></kendo-label>
      </div>
    </ng-template>
    <ng-template kendoSuffixTemplate>
      <kendo-svg-icon
        class="t-cursor-pointer t-w-[19px] t-h-[19px] t-text-[#333333]"
        (click)="chevDownIconClick(userSelection)"
        [icon]="chevronDownIcon"></kendo-svg-icon>
    </ng-template>
    <ng-template kendoMultiSelectGroupTagTemplate let-dataItems>
      {{ dataItems.length }} Legal Hold(s) selected
    </ng-template>
  </kendo-multiselect>

  <kendo-dropdownlist
    [filterable]="true"
    [virtual]="true"
    [listHeight]="300"
    [value]="selectedStatus()"
    (valueChange)="statusSelectionChange($event)"
    class="t-min-w-[18rem] t-max-w-[18rem]"
    [data]="filteredStatuses()"
    [valuePrimitive]="true"
    #activeTerminatedStatus
    valueField="value"
    textField="statusName">
  </kendo-dropdownlist>
</div>
