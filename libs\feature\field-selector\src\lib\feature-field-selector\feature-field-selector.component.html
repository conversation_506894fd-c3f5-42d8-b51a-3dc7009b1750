<div class="t-flex t-gap-6">
  <div>
    <kendo-treelist
      [kendoTreeListFlatBinding]="availableFields"
      [navigatable]="true"
      idField="displayFieldName"
      kendoTreeListSelectable
      [selectable]="{ mode: 'row', multiple: true, drag: false }"
      [(selectedItems)]="selectedAvailableFields"
      kendoTreeListExpandable
      [filterable]="true"
      [initiallyExpanded]="true"
      [sortable]="true"
      class="v-custom-view-tree t-h-[358px]"
      data-qa="selectedTree">
      <kendo-treelist-checkbox-column [width]="25" [showSelectAll]="true">
        select
      </kendo-treelist-checkbox-column>
      <kendo-treelist-column
        [expandable]="true"
        field="displayFieldName"
        title="UNSELECTED">
        <ng-template
          kendoTreeListFilterCellTemplate
          let-filter
          let-column="column">
          <kendo-treelist-string-filter-cell
            [column]="column"
            [filter]="filter"
            [showOperators]="false">
          </kendo-treelist-string-filter-cell>
        </ng-template>
      </kendo-treelist-column>

      <!-- Custom No Records Template -->
      <ng-template kendoTreeListNoRecordsTemplate>
        <div
          class="t-w-full t-h-full t-grid t-place-content-center t-absolute t-uppercase t-text-[#979797]">
          No records available.
        </div>
      </ng-template>
    </kendo-treelist>
  </div>
  <div class="t-flex t-w-[8%] t-flex-col t-justify-center t-items-center">
    <ul class="t-flex t-flex-col t-gap-3 t-justify-center t-mt-20" kendoTooltip>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Move all to selected'"
          data-qa="iconLeft"
          (click)="onActionClick('move-all-right')"
          #iconeNext>
          <span
            venioSvgLoader
            [parentElement]="iconeNext.element"
            svgUrl="assets/svg/icon-e-next-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Swap fields'"
          data-qa="iconRight"
          (click)="onActionClick('exchange')"
          #iconExchange>
          <span
            venioSvgLoader
            [parentElement]="iconExchange.element"
            svgUrl="assets/svg/icon-exchange-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          data-qa="iconRightArrow"
          (click)="onActionClick('move-right')"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Move checked to selected'"
          #iconRightArrow>
          <span
            venioSvgLoader
            [parentElement]="iconRightArrow.element"
            svgUrl="assets/svg/icon-right-arrow-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Move checked to unselected'"
          data-qa="iconLeftArrow"
          (click)="onActionClick('move-left')"
          #iconLeft>
          <span
            venioSvgLoader
            [parentElement]="iconLeft.element"
            svgUrl="assets/svg/icon-arrow-left-5-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          data-qa="iconLeftArrow"
          (click)="onActionClick('move-all-left')"
          [title]="'Move all to unselected'"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          #iconElement>
          <span
            venioSvgLoader
            [parentElement]="iconElement.element"
            svgUrl="assets/svg/icon-e-back-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
    </ul>
  </div>
  <div>
    <kendo-treelist
      [kendoTreeListFlatBinding]="visibleFields"
      [navigatable]="true"
      idField="displayFieldName"
      kendoTreeListSelectable
      [selectable]="{ mode: 'row', multiple: true, drag: false }"
      [(selectedItems)]="selectedVisibleFields"
      kendoTreeListExpandable
      [initiallyExpanded]="true"
      [filterable]="true"
      [sortable]="true"
      class="v-custom-view-tree t-h-[358px]"
      data-qa="unselectedlist">
      <kendo-treelist-checkbox-column [width]="25" [showSelectAll]="true">
        select
      </kendo-treelist-checkbox-column>
      <kendo-treelist-column
        [expandable]="true"
        field="displayFieldName"
        title="SELECTED">
        <ng-template
          kendoTreeListFilterCellTemplate
          let-filter
          let-column="column">
          <kendo-treelist-string-filter-cell
            [column]="column"
            [filter]="filter"
            [showOperators]="false">
          </kendo-treelist-string-filter-cell>
        </ng-template>
      </kendo-treelist-column>

      <!-- Custom No Records Template -->
      <ng-template kendoTreeListNoRecordsTemplate>
        <div
          class="t-w-full t-h-full t-grid t-place-content-center t-absolute t-uppercase t-text-[#979797]">
          No records available.
        </div>
      </ng-template>
    </kendo-treelist>
  </div>
  <div class="t-flex t-w-[8%] t-flex-col t-justify-center t-items-center">
    <ul class="t-flex t-flex-col t-gap-3 t-justify-center t-mt-20" kendoTooltip>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          class="!t-p-1 !t-rotate-90 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Move top'"
          data-qa="iconTop"
          (click)="onActionClick('move-top')"
          #icontop>
          <span
            venioSvgLoader
            [parentElement]="icontop.element"
            svgUrl="assets/svg/icon-e-back-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Move up'"
          data-qa="iconUp"
          (click)="onActionClick('move-up')"
          #iconUp>
          <span
            venioSvgLoader
            [parentElement]="iconUp.element"
            svgUrl="assets/svg/icon-up-chevron-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          data-qa="iconRightDown"
          (click)="onActionClick('move-down')"
          class="!t-p-1 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Move down'"
          #iconDown>
          <span
            venioSvgLoader
            [parentElement]="iconDown.element"
            svgUrl="assets/svg/icon-down-chevron-svg.svg"
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
      <li>
        <button
          kendoButton
          fillMode="outline"
          size="none"
          class="!t-p-1 !t-rotate-90 hover:!t-border-[#C6B3FF] hover:!t-bg-[#DDD1FF]"
          [title]="'Move bottom'"
          data-qa="iconBottom"
          (click)="onActionClick('move-bottom')"
          #iconBottom>
          <span
            venioSvgLoader
            [parentElement]="iconBottom.element"
            svgUrl="assets/svg/icon-e-next-svg.svg "
            hoverColor="#FFFFFF"
            height=".95rem"
            width=".95rem"></span>
        </button>
      </li>
    </ul>
  </div>
</div>
