import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridModule } from '@progress/kendo-angular-grid'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GroupModel } from '@venio/shared/models/interfaces'
import { ProjectFacade } from '@venio/data-access/common'
import { combineLatest, filter, Subject, takeUntil } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { DebounceTimer } from '@venio/util/utilities'
import { isEqual } from 'lodash'

@Component({
  selector: 'venio-project-role',
  standalone: true,
  imports: [CommonModule, GridModule, DropDownListModule],
  templateUrl: './project-role.component.html',
  styleUrl: './project-role.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProjectRoleComponent
  implements AfterViewInit, OnDestroy, OnChanges
{
  private readonly toDestroy$ = new Subject<void>()

  @Input()
  public savedGroup: GroupModel[] = []

  /**
   * Sets the permission type of the grid. default is READ_WRITE.
   * The dropdown will change based on the permission type.
   * If the permission type is READ_WRITE, the dropdown will have READ_WRITE, READ_ONLY, NONE options.
   * (string type)
   * If the permission type is VISIBILITY, the dropdown will have SHOW, HIDE options.
   * (boolean type)
   */
  @Input()
  public permissionType: 'READ_WRITE' | 'VISIBILITY' = 'READ_WRITE'

  @Output()
  public readonly projectRoleChange = new EventEmitter<GroupModel[]>()

  public projectGroup: GroupModel[]

  private projectFacade = inject(ProjectFacade)

  private activatedRoute = inject(ActivatedRoute)

  private get projectId(): number {
    return +this.activatedRoute.snapshot?.queryParams['projectId']
  }

  /**
   * Dropdown for permission type READ_WRITE
   */
  private readWritePermissionDropdown = [
    { value: 'READ_WRITE', label: 'Read/Write' },
    { value: 'READ_ONLY', label: 'Read Only' },
    { value: 'NONE', label: 'None' },
  ]

  /**
   * Dropdown for permission type VISIBILITY
   */
  private visibilityPermissionDropdown = [
    { value: true, label: 'Show' },
    { value: false, label: 'Hide' },
  ]

  public get permissionDropdown(): {
    value: string | boolean
    label: string
  }[] {
    // currently we have 2 permission types: READ_WRITE, VISIBILITY
    // if we have more permission types in the future, we can add them here
    return this.permissionType === 'READ_WRITE'
      ? this.readWritePermissionDropdown
      : this.visibilityPermissionDropdown
  }

  public isProjectGroupLoading$ =
    this.projectFacade.selectIsProjectGroupLoading$

  public ngOnChanges(changes: SimpleChanges): void {
    const { savedGroup } = changes

    if (!savedGroup?.currentValue) return

    if (!isEqual(savedGroup?.previousValue, savedGroup?.currentValue)) {
      this.savedGroup = savedGroup.currentValue
      this.#mapPermissionToProjectGroup()
      this.permissionChange()
    }
  }

  public ngAfterViewInit(): void {
    this.#selectProjectGroupResponse()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetProjectGroupState()
  }

  @DebounceTimer(100)
  public permissionChange(): void {
    this.projectRoleChange.emit(this.projectGroup)
  }

  #resetProjectGroupState(): void {
    this.projectFacade.resetProjectGroupState()
  }

  #mapPermissionToProjectGroup(): void {
    // if the permission type is VISIBILITY, we need to set the default permission to true
    // because the dropdown will have SHOW, HIDE options
    // if the permission type is READ_WRITE, we need to set the default permission to READ_WRITE
    // because the dropdown will have READ_WRITE, READ_ONLY, NONE options
    const permissionType =
      this.permissionType === 'READ_WRITE' ? 'READ_WRITE' : true

    const getPermission = (groupId: number): string | boolean => {
      const foundSaved = this.savedGroup.find((g) => g.groupId === groupId)
      // explicitly check for undefined and null to avoid the case where permission is false or default value
      // needs to return the permission type if the permission is undefined or null
      if (
        typeof foundSaved?.permission === 'undefined' ||
        foundSaved?.permission === null
      ) {
        return permissionType
      }
      return foundSaved?.permission
    }

    this.projectGroup = (this.projectGroup || []).map((group) => ({
      ...group,
      /*
         We have 3 permission types: READ_WRITE, READ_ONLY, NONE
         We need to set the default permission to READ_WRITE unless
        a user has already set the permission for the group.
       */
      permission: getPermission(group.groupId),
    }))
  }

  #selectProjectGroupResponse(): void {
    this.projectFacade.fetchProjectGroup(this.projectId)
    combineLatest([
      this.projectFacade.selectProjectGroupSuccessResponse$,
      this.projectFacade.selectProjectGroupErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.projectGroup = success?.data || []
        this.#mapPermissionToProjectGroup()
        // we need to notify there has been data arrived and the base changes should reflect
        this.permissionChange()
      })
  }
}
