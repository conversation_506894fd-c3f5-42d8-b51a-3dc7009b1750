import { createAction, props } from '@ngrx/store'
import {
  ResponseModel,
  TagGroupModel,
  TagReOrderModel,
  TagsModel,
} from '@venio/shared/models/interfaces'
import { TagsState } from './tags.reducer'
import { CommonActionTypes } from '@venio/shared/models/constants'

export enum TagsActionTypes {
  // Actions for Fetch Tag Tree
  FetchTagTree = '[Tags] Fetch Tag Tree',
  FetchTagTreeSuccess = '[Tags] Fetch Tag Tree: Success',
  FetchTagTreeFailure = '[Tags] Fetch Tag Tree: Failure',

  // Actions for fetch tags with count
  FetchTagWithCount = '[Tags] Fetch Tag With Count',
  FetchTagWithCountSuccess = '[Tags] Fetch Tag With Count: Success',
  FetchTagWithCountFailure = '[Tags] Fetch Tag With Count: Failure',

  // Actions for Tag CRUD
  SelectedTag = '[Tags] Set Selected Tag',
  FetchSelectedTag = '[Tags] Fetch Selected Tag',
  TagAddOrUpdate = '[Tags] Add or Update Tag',
  TagAddOrUpdateSuccess = '[Tags] Add or Update Tag: Success',
  TagAddOrUpdateFailure = '[Tags] Add or Update Tag: Failure',

  // Actions for Fetch Tag Group
  FetchTagGroup = '[Tags] Fetch Tag Group',
  FetchTagGroupSuccess = '[Tags] Fetch Tag Group: Success',
  FetchTagGroupFailure = '[Tags] Fetch Tag Group: Failure',

  // Actions for Fetch Tag Group
  TagGroupActions = '[Tags] Tag Group CRUD Actions',
  TagGroupSelected = '[Tags] Tag Group Selected',
  TagGroupActionsSuccess = '[Tags] Tag Group CRUD Actions: Success',
  TagGroupActionsFailure = '[Tags] Tag Group CRUD Actions: Failure',

  // Actions for Tag Deletion
  DeleteTag = '[Tags] Delete Tag',
  DeleteTagSuccess = '[Tags] Delete Tag: Success',
  DeleteTagFailure = '[Tags] Delete Tag: Failure',

  // Actions for Fetch Tag Propagation Settings
  FetchTagPropagationSettings = '[Tags] Fetch Tag Propagation Settings',
  FetchTagPropagationSettingSuccess = '[Tags] Fetch Tag Propagation Settings: Success',
  FetchTagPropagationSettingFailure = '[Tags] Fetch Tag Propagation Settings: Failure',

  // Action for notification
  NotifyAddOrUpdateTagField = '[Tags] Notify Add or Update Tag Field',
  NotifyTagFormGroupValid = '[Tags] Notify Tag Form Group Valid',
  NotifyToSwitchTagView = '[Tags] Notify To Switch Tag View',

  // Action for save reorder field
  SaveTagReorder = '[Tags] Save  Tag Reorder',
  FetchTagReorderSuccess = '[Tags] Fetch Tag Reorder: Success',
  FetchTagReorderFailure = '[Tags] Fetch Tag Reorder: Failure',

  // Resetting Tags State
  ResetTagsState = '[Tags] Reset State',
}

export const resetTagsState = createAction(
  TagsActionTypes.ResetTagsState,
  props<{ stateKey: keyof TagsState | Array<keyof TagsState> }>()
)

export const fetchTagTree = createAction(
  TagsActionTypes.FetchTagTree,
  props<{ projectId: number; reviewSetId: number }>()
)
export const fetchTagGroup = createAction(
  TagsActionTypes.FetchTagGroup,
  props<{ projectId: number; reviewSetId: number }>()
)

export const fetchTagGroupFailure = createAction(
  TagsActionTypes.FetchTagGroupFailure,
  props<{ tagGroupErrorResponse: ResponseModel }>()
)

export const fetchTagGroupSuccess = createAction(
  TagsActionTypes.FetchTagGroupSuccess,
  props<{ tagGroupSuccessResponse: ResponseModel }>()
)

export const fetchTagTreeFailure = createAction(
  TagsActionTypes.FetchTagTreeFailure,
  props<{ tagTreeErrorResponse: ResponseModel }>()
)

export const fetchTagTreeSuccess = createAction(
  TagsActionTypes.FetchTagTreeSuccess,
  props<{ tagTreeSuccessResponse: ResponseModel }>()
)

export const deleteTag = createAction(
  TagsActionTypes.DeleteTag,
  props<{ projectId: number; tagId: number; isTagGroup?: boolean }>()
)

export const deleteTagFailure = createAction(
  TagsActionTypes.DeleteTagFailure,
  props<{ tagDeleteErrorResponse: ResponseModel }>()
)

export const deleteTagSuccess = createAction(
  TagsActionTypes.DeleteTagSuccess,
  props<{ tagDeleteSuccessResponse: ResponseModel }>()
)

export const tagGroupActionFailure = createAction(
  TagsActionTypes.TagGroupActionsFailure,
  props<{ tagGroupActionErrorResponse: ResponseModel }>()
)

export const tagGroupActionSuccess = createAction(
  TagsActionTypes.TagGroupActionsSuccess,
  props<{ tagGroupActionSuccessResponse: ResponseModel }>()
)

export const tagGroupActions = createAction(
  TagsActionTypes.TagGroupActions,
  props<{
    payload: {
      projectId?: number
      actionType: CommonActionTypes
      data: TagGroupModel
    }
  }>()
)

export const setSelectedTagGroup = createAction(
  TagsActionTypes.TagGroupSelected,
  props<{
    payload: {
      actionType: CommonActionTypes
      data: TagGroupModel
    }
  }>()
)

export const setSelectedTag = createAction(
  TagsActionTypes.SelectedTag,
  props<{
    selectedTag: {
      projectId: number
      actionType: CommonActionTypes
      selected: TagsModel
    }
  }>()
)

export const fetchSelectedTag = createAction(
  TagsActionTypes.FetchSelectedTag,
  props<{
    selectedTag: {
      projectId: number
      actionType: CommonActionTypes
      selected: TagsModel
    }
  }>()
)

export const tagAddOrUpdate = createAction(
  TagsActionTypes.TagAddOrUpdate,
  props<{
    payload: {
      projectId?: number
      actionType: CommonActionTypes
      data: TagsModel
    }
  }>()
)

export const tagAddOrUpdateFailure = createAction(
  TagsActionTypes.TagAddOrUpdateFailure,
  props<{ tagAddOrUpdateErrorResponse: ResponseModel }>()
)

export const tagAddOrUpdateSuccess = createAction(
  TagsActionTypes.TagAddOrUpdateSuccess,
  props<{ tagAddOrUpdateSuccessResponse: ResponseModel }>()
)

export const fetchTagPropagationSetting = createAction(
  TagsActionTypes.FetchTagPropagationSettings,
  props<{ projectId: number }>()
)

export const fetchTagPropagationSettingFailure = createAction(
  TagsActionTypes.FetchTagPropagationSettingFailure,
  props<{ tagPropagationSettingErrorResponse: ResponseModel }>()
)

export const fetchTagPropagationSettingSuccess = createAction(
  TagsActionTypes.FetchTagPropagationSettingSuccess,
  props<{ tagPropagationSettingSuccessResponse: ResponseModel }>()
)

export const notifyAddOrUpdateTagField = createAction(
  TagsActionTypes.NotifyAddOrUpdateTagField,
  props<{ isAddOrUpdateTagFieldNotified: boolean }>()
)

export const notifyTagFormGroupValid = createAction(
  TagsActionTypes.NotifyTagFormGroupValid,
  props<{ tagFormGroupValid: boolean }>()
)

export const notifyToSwitchTagView = createAction(
  TagsActionTypes.NotifyToSwitchTagView,
  props<{ switchToTagView: boolean }>()
)

export const fetchTagWithCount = createAction(
  TagsActionTypes.FetchTagWithCount,
  props<{ projectId: number }>()
)

export const fetchTagWithCountFailure = createAction(
  TagsActionTypes.FetchTagWithCountFailure,
  props<{ tagWithCountErrorResponse: ResponseModel }>()
)

export const fetchTagWithCountSuccess = createAction(
  TagsActionTypes.FetchTagWithCountSuccess,
  props<{ tagWithCountSuccessResponse: ResponseModel }>()
)

export const saveTagReorder = createAction(
  TagsActionTypes.SaveTagReorder,
  props<{ projectId: number; tagReorder: TagReOrderModel }>()
)

export const fetchTagReorderFailure = createAction(
  TagsActionTypes.FetchTagReorderFailure,
  props<{ tagReorderErrorResponse: ResponseModel }>()
)

export const fetchTagReorderSuccess = createAction(
  TagsActionTypes.FetchTagReorderSuccess,
  props<{ tagReorderSuccessResponse: ResponseModel }>()
)
