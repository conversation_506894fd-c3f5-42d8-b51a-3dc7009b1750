import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  TextBoxComponent,
  TextBoxSuffixTemplateDirective,
} from '@progress/kendo-angular-inputs'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { ProjectFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { DebounceTimer } from '@venio/util/utilities'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
@Component({
  selector: 'venio-reviewset-sub-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    TextBoxComponent,
    TextBoxSuffixTemplateDirective,
    ButtonComponent,
    UiPaginationModule,
    TooltipsModule,
  ],
  templateUrl: './reviewset-sub-toolbar.component.html',
  styleUrl: './reviewset-sub-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetSubToolbarComponent {
  private readonly projectFacade = inject(ProjectFacade)

  private readonly currentPagingInfo = toSignal(
    this.projectFacade.selectReviewSetSummaryDetailPagingInfo$
  )

  /**
   * Whether the review set summary detail is loading.
   */
  public readonly isReviewSetLoading = toSignal(
    this.projectFacade.selectIsReviewSetSummaryDetailLoading$,
    {
      initialValue: true,
    }
  )

  public readonly searchChange = output<string>()

  public readonly pagingChange = output<PageArgs>()

  public readonly totalRecords = computed(
    () => this.currentPagingInfo()?.totalReviewSetCount || 0
  )

  public readonly pageSize = computed(
    () => this.currentPagingInfo()?.pageSize || 0
  )

  @DebounceTimer(800)
  public searchValueChange(value: string): void {
    this.searchChange.emit(value)
  }

  public pagingControlChange(arg: PageArgs): void {
    this.pagingChange.emit(arg)
  }
}
