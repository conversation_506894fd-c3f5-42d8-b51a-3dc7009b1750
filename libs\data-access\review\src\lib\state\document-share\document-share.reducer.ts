import { createReducer, on } from '@ngrx/store'
import * as DocumentShareActions from './document-share.actions'
import {
  DocumentShareUserModel,
  SharedDocumentDetailRequestInfo,
  SharedDocumentDetailModel,
  SharedDocRequestType,
} from '../../models/interfaces/document-share.model'
import { UserMessageModel } from '../../models/interfaces/common.models'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { resetStateProperty } from '@venio/util/utilities'
import { HttpErrorResponse } from '@angular/common/http'

export const DOCUMENT_SHARE_FEATURE_KEY = 'documentShare'

export interface DocumentShareState {
  invitationInProgress: boolean
  userMessage: UserMessageModel | null
  documentShareInternalUsers: DocumentShareUserModel[] | null
  documentShareExternalUsers: DocumentShareUserModel[] | null

  // Loading State Indicators
  isSharedDocumentsLoading: boolean | undefined
  isSharedDocumentDetailLoading: boolean | undefined

  // Shared Documents Operation Responses
  documentShareAvailable: boolean | undefined
  sharedDocumentsSuccessResponse: SharedDocumentDetailModel[] | undefined
  sharedDocumentsErrorResponse: HttpErrorResponse | undefined

  // Shared Document Detail Operation Responses
  sharedDocumentDetailSuccessResponse: ResponseModel | undefined
  sharedDocumentDetailErrorResponse: HttpErrorResponse | undefined

  // Shared Document Request Info
  sharedDocumentRequestInfo: SharedDocumentDetailRequestInfo

  isSharedDocumentCountDetailLoading: boolean
  sharedDocumentCountDetailSuccessResponse: ResponseModel
  sharedDocumentCountDetailErrorResponse: ResponseModel

  // Update Expiry Date Responses
  updatedDocumentResponse: ResponseModel | undefined
  updateExpiryDateErrorResponse: HttpErrorResponse | undefined

  // Unshare Document Responses
  unsharedDocumentResponse: ResponseModel | undefined
  unShareDocumentErrorResponse: HttpErrorResponse | undefined
}

export const initialDocumentShareState: DocumentShareState = {
  invitationInProgress: false,
  userMessage: null,
  documentShareInternalUsers: null,
  documentShareExternalUsers: null,

  isSharedDocumentsLoading: undefined,
  isSharedDocumentDetailLoading: undefined,
  documentShareAvailable: undefined,
  sharedDocumentsSuccessResponse: undefined,
  sharedDocumentsErrorResponse: undefined,
  sharedDocumentDetailSuccessResponse: undefined,
  sharedDocumentDetailErrorResponse: undefined,
  isSharedDocumentCountDetailLoading: undefined,
  sharedDocumentCountDetailSuccessResponse: undefined,
  sharedDocumentCountDetailErrorResponse: undefined,
  sharedDocumentRequestInfo: {
    pageNumber: 1,
    pageSize: 25,
    sortField: 'DocumentShareID',
    sortOrder: 'asc',
    sharedDocRequestType: SharedDocRequestType.All,
  },
  updatedDocumentResponse: undefined,
  updateExpiryDateErrorResponse: undefined,
  unsharedDocumentResponse: undefined,
  unShareDocumentErrorResponse: undefined,
}

export const DocumentShareReducer = createReducer(
  initialDocumentShareState,
  on(DocumentShareActions.setUserMessage, (state, { messageModel }) => ({
    ...state,
    userMessage: messageModel,
  })),
  on(
    DocumentShareActions.setDocumentShareUsersInternal,
    (state, { userModels }) => ({
      ...state,
      documentShareInternalUsers: userModels,
    })
  ),
  on(
    DocumentShareActions.setDocumentShareUsersExternal,
    (state, { userModels }) => ({
      ...state,
      documentShareExternalUsers: userModels,
    })
  ),
  on(
    DocumentShareActions.setInvitationInProgressFlag,
    (state, { isInvitationInProgress }) => ({
      ...state,
      invitationInProgress: isInvitationInProgress,
    })
  ),
  on(DocumentShareActions.resetDocumentShareState, () => ({
    ...initialDocumentShareState,
  })),
  on(DocumentShareActions.resetSharedDocumentState, (state, { stateKey }) =>
    resetStateProperty<DocumentShareState>(
      state,
      initialDocumentShareState,
      stateKey
    )
  ),
  on(DocumentShareActions.fetchSharedDocuments, (state) => ({
    ...state,
    isSharedDocumentsLoading: true,
  })),
  on(
    DocumentShareActions.fetchSharedDocumentsSuccess,
    (state, { sharedDocumentsSuccessResponse }) => {
      // parsing to share type enum
      const enrichedResponse = sharedDocumentsSuccessResponse.map((item) => {
        const type = item.sharedType?.toLowerCase()
        let shareEnum: SharedDocRequestType | undefined

        if (type === 'shared by me') {
          shareEnum = SharedDocRequestType.SharedByMe
        } else if (type === 'shared to me') {
          shareEnum = SharedDocRequestType.SharedToMe
        }
        return {
          ...item,
          shareTypeEnum: shareEnum,
        }
      })

      return {
        ...state,
        sharedDocumentsSuccessResponse: enrichedResponse,
        sharedDocumentsErrorResponse: undefined,
        isSharedDocumentsLoading: false,
      }
    }
  ),
  on(
    DocumentShareActions.fetchSharedDocumentsFailure,
    (state, { sharedDocumentsErrorResponse }) => ({
      ...state,
      sharedDocumentsSuccessResponse: undefined,
      sharedDocumentsErrorResponse,
      isSharedDocumentsLoading: false,
    })
  ),
  on(DocumentShareActions.fetchSharedDocumentDetail, (state) => ({
    ...state,
    isSharedDocumentDetailLoading: true,
  })),
  on(
    DocumentShareActions.fetchSharedDocumentDetailSuccess,
    (state, { sharedDocumentDetailSuccessResponse }) => ({
      ...state,
      sharedDocumentDetailSuccessResponse,
      sharedDocumentDetailErrorResponse: undefined,
      unsharedDocumentResponse: undefined,
      isSharedDocumentDetailLoading: false,
    })
  ),
  on(
    DocumentShareActions.fetchSharedDocumentDetailFailure,
    (state, { sharedDocumentDetailErrorResponse }) => ({
      ...state,
      sharedDocumentDetailSuccessResponse: undefined,
      sharedDocumentDetailErrorResponse,
      isSharedDocumentDetailLoading: false,
    })
  ),
  on(DocumentShareActions.fetchSharedDocumentCountDetail, (state) => ({
    ...state,
    isSharedDocumentCountDetailLoading: true,
  })),
  on(
    DocumentShareActions.fetchSharedDocumentCountDetailSuccess,
    (state, { sharedDocumentCountDetailSuccessResponse }) => ({
      ...state,
      sharedDocumentCountDetailSuccessResponse,
      isSharedDocumentCountDetailLoading: false,
    })
  ),
  on(
    DocumentShareActions.fetchSharedDocumentCountDetailError,
    (state, { sharedDocumentCountDetailErrorResponse }) => ({
      ...state,
      sharedDocumentCountDetailErrorResponse,
      isSharedDocumentCountDetailLoading: false,
    })
  ),
  on(DocumentShareActions.updateSharedDocumentRequestInfo, (state, action) => {
    const filteredRequestInfo = Object.fromEntries(
      Object.entries(action.sharedDocumentRequestInfo).filter(
        ([_, value]) => value !== null && value !== undefined
      )
    )

    return {
      ...state,
      sharedDocumentRequestInfo: {
        ...state.sharedDocumentRequestInfo,
        ...filteredRequestInfo,
        sortOrder: filteredRequestInfo.sortOrder || 'asc',
        sortField: filteredRequestInfo.sortField || 'DocumentId',
      } as SharedDocumentDetailRequestInfo,
    }
  }),
  on(DocumentShareActions.updateExpiryDate, (state) => ({
    ...state,
  })),
  on(
    DocumentShareActions.updateExpiryDateSuccess,
    (state, { updatedDocumentResponse }) => ({
      ...state,
      updatedDocumentResponse,
      updateExpiryDateErrorResponse: undefined,
    })
  ),
  on(
    DocumentShareActions.updateExpiryDateFailure,
    (state, { errorResponse }) => ({
      ...state,
      updatedDocumentResponse: undefined,
      updateExpiryDateErrorResponse: errorResponse,
    })
  ),
  on(DocumentShareActions.unShareDocument, (state) => ({
    ...state,
  })),
  on(
    DocumentShareActions.unShareDocumentSuccess,
    (state, { unsharedDocumentResponse }) => ({
      ...state,
      unsharedDocumentResponse,
      sharedDocumentDetailSuccessResponse: undefined,
      unShareDocumentErrorResponse: undefined,
    })
  ),
  on(
    DocumentShareActions.unShareDocumentFailure,
    (state, { errorResponse }) => ({
      ...state,
      unsharedDocumentResponse: undefined,
      unShareDocumentErrorResponse: errorResponse,
    })
  ),
  on(
    DocumentShareActions.setSharedDocumentAvailability,
    (state, { documentShareAvailable }) => ({
      ...state,
      documentShareAvailable,
    })
  )
)
