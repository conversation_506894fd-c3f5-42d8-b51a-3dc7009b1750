import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms'
import {
  DocumentsFacade,
  DynamicFolderModel,
  FolderModel,
  SearchDupOption,
  SearchFacade,
  SearchInputParams,
  SearchRequestModel,
  StartupsFacade,
  ViewFacade,
} from '@venio/data-access/review'
import { DocumentDuplicateOptionsComponent } from '../document-duplicate-options/document-duplicate-options.component'
import { DocumentSearchControlComponent } from '../document-search-control/document-search-control.component'
import { DocumentAdditionalActionsComponent } from '../document-additional-actions/document-additional-actions.component'
import {
  AppIdentitiesTypes,
  IframeMessengerFacade,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { distinctUntilChanged, filter, map, Subject, takeUntil } from 'rxjs'
import { FolderTabType, GoldenLayoutFacade } from '@venio/golden-layout'
import { debounceTime, take } from 'rxjs/operators'
import { isEqual } from 'lodash'
import { ActivatedRoute, Router } from '@angular/router'
import {
  BreadcrumbFacade,
  BreadcrumbService,
} from '@venio/data-access/breadcrumbs'
import {
  ConditionElement,
  ConditionGroup,
  ConditionType,
  GroupStackType,
} from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'
import { CommandsFacade, ProjectFacade } from '@venio/data-access/common'
import {
  CommandEventTypes,
  DocumentMenuType,
} from '@venio/shared/models/constants'
import { AiSearchDialogContainerComponent } from '../../../ai-search/ai-search-dialog-container/ai-search-dialog-container.component'
import { DialogService } from '@progress/kendo-angular-dialog'
import { LocalStorage } from '@venio/shared/storage'
import { WindowManagerService } from '../../../../services/window.manager.service'
import {
  WindowMessengerService,
  WindowMessageType,
} from '../../../../services/window.messenger.service'

interface SearchFormModel {
  searchExpression: string
  includePC: boolean
  searchDuplicateOption: SearchDupOption
}

@Component({
  selector: 'venio-document-toolbar-container',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DocumentDuplicateOptionsComponent,
    DocumentSearchControlComponent,
    DocumentAdditionalActionsComponent,
  ],
  templateUrl: './document-toolbar-container.component.html',
  styleUrls: ['./document-toolbar-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentToolbarContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogService = inject(DialogService)

  private formBuilder = inject(FormBuilder)

  private searchFacade = inject(SearchFacade)

  private iframeMessengerFacade = inject(IframeMessengerFacade)

  private changeDetectorRef = inject(ChangeDetectorRef)

  private goldenLayoutFacade = inject(GoldenLayoutFacade)

  private viewFacade = inject(ViewFacade)

  private activatedRoute = inject(ActivatedRoute)

  private breadcrumbFacade = inject(BreadcrumbFacade)

  private breadcrumbService = inject(BreadcrumbService)

  private commandsFacade = inject(CommandsFacade)

  private projectFacade = inject(ProjectFacade)

  private documentsFacade = inject(DocumentsFacade)

  private router = inject(Router)

  private startupsFacade = inject(StartupsFacade)

  private messengerService = inject(WindowMessengerService)

  private windowManager = inject(WindowManagerService)

  private aiSearchBreadcrumb = signal<ConditionGroup>(undefined)

  private currentFileId: number

  private get searchExpressionControl(): AbstractControl {
    return this.searchFormGroup.get('searchExpression')
  }

  private get includeFamilyControl(): AbstractControl {
    return this.searchFormGroup.get('includePC')
  }

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public get isReviewPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isReviewPanelPopout')
  }

  public searchFormGroup: FormGroup

  private selectedFolderType: FolderTabType

  private dynamicFolderScope: DynamicFolderModel

  private staticFolderScope: FolderModel

  private selectedViewDefaultExpression: string

  public defaultSearchDupOption: SearchDupOption

  public ngOnInit(): void {
    this.#getSelectedDocuments()
    this.#selectProjectSearchOptions()
    this.#selectDynamicFolderSearchScope()
    this.#selectStaticFolderSearchScope()
    this.#selectSelectedFolderType()
    this.#initSearchFormGroup()
    this.#selectSearchChange()
    this.#selectResetInputControl()
    this.#selectSelectedView()
    this.#searchFormValueChange()
    this.#selectSearchFormValues()
    this.#handleProjectIdQueryParamChange()
    this.#handleTranscriptMenu()
    this.#selectAiSearchBreadcrumb()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Selects and handles changes in the selected folder type.
   * @returns {void}
   */
  #selectSelectedFolderType(): void {
    this.goldenLayoutFacade.getFolderTabType$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((folderTabType) => {
        this.selectedFolderType = folderTabType
      })
  }

  /**
   * clears the search input when the project changes
   * @returns {void}
   */
  #handleProjectIdQueryParamChange(): void {
    this.activatedRoute.queryParams
      .pipe(
        filter((q) => Boolean(q['projectId'])),
        distinctUntilChanged((a, b) => a['projectId'] === b['projectId']),
        takeUntil(this.toDestroy$)
      )
      .subscribe((params) => {
        this.#clearSearchInput()
      })
  }

  #handleTranscriptMenu(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.TRANSCRIPT),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        // redirect to viewer
        if (this.isReviewPanelPopout) {
          this.#notifyToShowTranscriptViewerInPopoutWindow()
        } else {
          this.router.navigate(['/document-detail'], {
            queryParams: {
              projectId: this.projectId,
              docShareToken: '',
              defaultViewer: 'Transcript',
            },
          })
        }
      })
  }

  #selectSelectedView(): void {
    this.viewFacade.selectSelectedViewDefaultExpression$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((selectedViewDefaultExpression) => {
        this.selectedViewDefaultExpression = (
          selectedViewDefaultExpression || ''
        ).trim()
      })
  }

  #searchFormValueChange(): void {
    this.searchFormGroup.valueChanges
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.#storeFormValues()
      })
  }

  #storeFormValues(): void {
    const formValue = this.#getSearchFormValue()

    this.searchFacade.storeSearchFormValues({
      ...formValue,
      searchExpression: this.searchExpressionControl.value,
    })
  }

  #selectSearchFormValues(): void {
    this.searchFacade.selectSearchFormValues$
      .pipe(
        distinctUntilChanged((a, b) => isEqual(a, b)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((formValues) => {
        if (formValues) {
          this.searchFormGroup.patchValue(formValues, { emitEvent: false })
        }
      })
  }

  #performResetInputControls(): void {
    // Fetch default search options
    this.projectFacade.fetchSearchOptions(this.projectId)
    // Reset includes family control to false without emitting an event
    this.includeFamilyControl.reset(false, { emitEvent: false })
    // Reset search expression control to empty without emitting an event
    this.searchExpressionControl.reset('', { emitEvent: false })
    // once the reset is done, the state should be default, so it can emit the event again.
    this.searchFacade.resetSearchState('shouldResetSearchInputControls')
    this.iframeMessengerFacade.resetMessengerState(MessageType.SEARCH_CHANGE)
  }

  #selectResetInputControl(): void {
    this.searchFacade.selectResetSearchInputControl$
      .pipe(
        filter((reset) => reset),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#performResetInputControls()
      })
  }

  /**
   * Selects and handles changes in the dynamic folder search scope.
   * @returns {void}
   */
  #selectDynamicFolderSearchScope(): void {
    this.searchFacade.getDynamicFolderSearchScope$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((folder) => {
        this.dynamicFolderScope = folder
      })
  }

  /**
   * Selects and handles changes in the static folder search scope.
   * @returns {void}
   */
  #selectStaticFolderSearchScope(): void {
    this.searchFacade.getStaticFolderSearchScope$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((folder) => {
        this.staticFolderScope = folder
      })
  }

  /**
   * Clears the search input.
   * @returns {void}
   */
  #clearSearchInput(): void {
    // Fetch and load a default duplicate option
    this.#fetchSearchDuplicationOption()
    // Reset controls
    this.searchExpressionControl.reset('')
    this.includeFamilyControl.reset(false)
  }

  #fetchSearchDuplicationOption(): void {
    this.projectFacade.fetchSearchOptions(this.projectId)
  }

  /**
   * Performs a search based on the form values.
   * @param {boolean} isReset - Whether the search is a reset search.
   * @returns {void}
   */
  #performSearch(isReset = false): void {
    const formValue = this.#getSearchFormValue()
    this.addBreadcrumb(formValue.searchExpression)

    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((searchExpression) => {
        // If the search expression is empty, default to FileId>0
        searchExpression = searchExpression || 'FileId>0'

        if (isReset) {
          formValue.viewTagRuleConflictFiles = null
          this.searchFacade.updateTagRuleIdPatternList({
            ruleIds: [],
            syntax: [],
          })
        }

        const payload = { ...formValue, searchExpression }
        this.searchFacade.search(payload)
      })
  }

  private addBreadcrumb(conditionSyntax: string): void {
    // If the condition syntax is FileId>0, do not add a breadcrumb as it is the default search condition
    if (
      (conditionSyntax || '').replace(/\s+/g, '').trim().toLowerCase() ===
      'fileid>0'
    )
      return

    const payload: ConditionGroup = {
      id: UuidGenerator.uuid,
      conditions: [{ conditionSyntax }] as ConditionElement[],
      conditionType: ConditionType.Group,
      groupStackType: GroupStackType.QUICK_SEARCH,
      checked: true,
    }
    this.breadcrumbFacade.storeBreadcrumbs([payload])
  }

  #getSearchExpression(): string {
    const expression: string = (this.searchExpressionControl.value || '').trim()

    return (
      // Use the search expression if it is not empty
      expression ||
      // If all are empty, default to FileId>0
      'FileId>0'
    )
  }

  /**
   * Returns the search form values with additional properties.
   * @returns {SearchInputParams} The search form value with additional properties.
   */
  #getSearchFormValue(): SearchInputParams {
    const formValue = this.searchFormGroup.value

    return {
      ...formValue,
      searchExpression: this.#getSearchExpression(),
      isResetBaseGuid: true,
      // navigationType: this.#getNavigationType(),
      // folderList: this.#getFolderList(),
      // dynamicFolderScope: this.#getDynamicFolderScope(),
    } as SearchInputParams
  }

  /**
   * Handles search or input clearing based on the action provided.
   * @param {string} action - The action to be performed, either 'SEARCH' or 'CLEAR_INPUT'.
   * @returns {void}
   */
  public search(
    action: 'SEARCH' | 'CLEAR_INPUT' | 'RESET_SEARCH' | 'LAUNCH_AI_SEARCH'
  ): void {
    if (action === 'CLEAR_INPUT') {
      this.#clearSearchInput()
      return
    }

    if (action === 'LAUNCH_AI_SEARCH') {
      this.launchAiSearchDialog()
      return
    }

    if (action === 'RESET_SEARCH') {
      // If it is a reset search, default search should be used
      // @see #getSearchExpression
      this.selectedViewDefaultExpression = ''
      this.iframeMessengerFacade.resetMessengerState(MessageType.SEARCH_CHANGE)
      this.breadcrumbFacade.resetBreadcrumbCurrentStates()
      this.breadcrumbService.setConditionChecked(
        GroupStackType.VIEW_SEARCH,
        true
      )
      this.searchFacade.setDynamicFolderSearchScope(null)
      this.searchFacade.setStaticFolderSearchScope(null)
      this.commandsFacade.dispatchCommand({
        type: CommandEventTypes.ResetSelectedCustomFolder,
        data: { resetSelectedItemKeys: true },
      })
    }

    this.#performSearch(action === 'RESET_SEARCH')
  }

  #initSearchFormGroup(): void {
    this.searchFormGroup = this.formBuilder.group<SearchFormModel>({
      searchExpression: '',
      includePC: false,
      searchDuplicateOption: SearchDupOption.HIDE_ALL_DUPS_DYNAMIC,
    })
  }

  /**
   * Sets the search term based on the message content.
   * This method updates the search form group with values from the message content.
   * @param {MessageContent} mc - The message content containing search parameters.
   * @returns {void} - This method does not return a value.
   */
  #setSearchTerm(mc: MessageContent): void {
    // Exit if no content is provided
    if (!mc.content) return

    const { includeFamily, searchTerm, searchDuplicateOption } = mc.content
    this.changeDetectorRef.markForCheck()
    this.searchFormGroup.patchValue({
      includePC: Boolean(includeFamily),
      searchExpression: searchTerm,
      searchDuplicateOption:
        searchDuplicateOption || SearchDupOption.HIDE_ALL_DUPS_DYNAMIC,
      // TODO: maybe more parameter coming through and we add them here
    })
  }

  /**
   * Listens for search change messages when such a message is received.
   * This method subscribes to a stream of messages, filters for specific message types that
   * indicate a search change, and then calls `#setSearchTerm` set the search criteria in the form.
   * @returns {void} - This method does not return a value.
   */
  #selectSearchChange(): void {
    this.iframeMessengerFacade
      .selectIframeMessengerContent$(MessageType.SEARCH_CHANGE)
      .pipe(
        map((mc) => mc as MessageContent),
        filter((mc) => Boolean(mc?.content?.['searchTerm'])),
        takeUntil(this.toDestroy$)
      )
      .subscribe((mc: MessageContent) => {
        this.#setSearchTerm(mc)
      })
  }

  #selectProjectSearchOptions(): void {
    this.projectFacade.selectSearchOptionSuccessResponse$
      .pipe(
        filter(
          (searchDuplicateOption) => typeof searchDuplicateOption === 'number'
        ),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((searchDuplicateOption) => {
        this.defaultSearchDupOption = searchDuplicateOption
        this.searchFormGroup?.patchValue({
          searchDuplicateOption,
        })

        const searchRequest: SearchRequestModel = {
          searchDuplicateOption: searchDuplicateOption,
          includePC: false,
          lstMedia: null,
          projectId: this.projectId.toString(),
          searchExpression: this.#getSearchExpression(),
          searchGuid: null,
          userType:
            localStorage.getItem('DocShareUserRole')?.toLowerCase() ===
            'external'
              ? 'EXTERNAL'
              : 'INTERNAL',
          baseGUID: '',
          isForwardFilter: false,
        }

        this.startupsFacade.updateSearchRequestParam(searchRequest)

        // Once we receive the value, we reset this to the default state so, we can emit the event again.
        this.projectFacade.resetProjectState('searchOptionSuccessResponse')
      })
  }

  public launchAiSearchDialog(): void {
    const dialogRef = this.dialogService.open({
      content: AiSearchDialogContainerComponent,
      animation: { type: 'fade', duration: 100 },
      cssClass: 'v-custom-dialog-pos',
      maxHeight: '90vh',
      width: 'calc(100% - 300px)',
      minWidth: '700px',
    })

    dialogRef.result
      .pipe(
        filter(
          (result) =>
            typeof result === 'object' &&
            Array.isArray(result) &&
            result?.[0] > 0
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((fileIds: number[]) => {
        const conditionSyntax = `INTERNAL_FILE_ID IN(${fileIds.join(',')})`
        this.searchFacade.search({ searchExpression: conditionSyntax })

        const id = this.aiSearchBreadcrumb()?.id || UuidGenerator.uuid

        const payload: ConditionGroup = {
          id,
          conditions: [{ conditionSyntax }] as ConditionElement[],
          conditionType: ConditionType.Group,
          groupStackType: GroupStackType.AI_SEARCH,
          checked: true,
        }
        if (this.aiSearchBreadcrumb()) {
          this.breadcrumbFacade.updateBreadcrumb(payload)
        } else {
          this.breadcrumbFacade.storeBreadcrumbs([payload])
        }
      })
  }

  #selectAiSearchBreadcrumb(): void {
    this.breadcrumbFacade.selectBreadcrumbStack$
      .pipe(
        map((c) =>
          c?.find((x) => x?.groupStackType === GroupStackType.AI_SEARCH)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((breadcrumb) => {
        this.aiSearchBreadcrumb.set(breadcrumb)
      })
  }

  #getSelectedDocuments(): void {
    this.documentsFacade.getSelectedDocuments$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((selectedDocuments) => {
        this.currentFileId = selectedDocuments[selectedDocuments.length - 1]
      })
  }

  #notifyToShowTranscriptViewerInPopoutWindow(): void {
    const popoutWindow: Window = this.windowManager.getWindow(
      AppIdentitiesTypes.REVIEW_PANEL
    )
    if (popoutWindow) {
      this.messengerService.sendMessage(
        {
          payload: {
            type: WindowMessageType.DATA_UPDATE,
            content: {
              isTranscriptRequest: true,
              currentFileId: this.currentFileId,
            },
          },
        },
        popoutWindow
      )
      this.#resetMenuEventState()
    }
  }

  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }
}
