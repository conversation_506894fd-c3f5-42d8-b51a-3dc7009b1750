import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  input,
  OnDestroy,
  OnInit,
} from '@angular/core'
import { FormGroup } from '@angular/forms'
import { DocumentShareFacade } from '@venio/data-access/review'
import { Subject, takeUntil } from 'rxjs'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import { SharedDocInjectModel } from '../../models/document-share.models'
import dayjs from 'dayjs'

@Component({
  selector: 'venio-document-share-options',
  templateUrl: './document-share-options.component.html',
  styleUrl: './document-share-options.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentShareOptionsComponent implements OnInit, OnDestroy {
  public sharedDocData = input<SharedDocInjectModel | null>(null)

  private readonly toDestroy$ = new Subject<void>()

  public invitationInProgress$ =
    this.documentShareFacade.getInvitationInProgressFlag$

  private originalLinkExpiryDate: string

  public get documentShareForm(): FormGroup {
    return this.documentShareFormService.documentShareForm
  }

  public projectId: number

  public linkExpired = 7

  public sendCopyToMe = false

  public showNumericSpinner = false

  constructor(
    private documentShareFacade: DocumentShareFacade,
    private documentShareFormService: DocumentShareFormService,
    private cdr: ChangeDetectorRef
  ) {}

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngOnInit(): void {
    this.initSlices()
    this.originalLinkExpiryDate =
      this.sharedDocData()?.shareDocData?.sharedExpiryDate
  }

  private initSlices(): void {
    this.invitationInProgress$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((inProgress) => {
        if (!inProgress) {
          this.documentShareForm.get('shareName').enable()
          this.documentShareForm.get('sendCopyToMe').enable()
          this.documentShareForm.get('sharedExpiryDate').enable()
        } else {
          this.documentShareForm.get('shareName').disable()
          this.documentShareForm.get('sendCopyToMe').disable()
          this.documentShareForm.get('sharedExpiryDate').disable()
        }
        if (this.sharedDocData()?.isDocShareEdit) {
          this.documentShareForm
            .get('shareName')
            .setValue(this.sharedDocData()?.shareDocData?.shareName)
        }
        this.cdr.detectChanges()
      })
  }

  public onExpiryDateChange($event: Date): void {
    const sharedExpiryDate: Date | null =
      this.documentShareForm.get('sharedExpiryDate')?.value
    const originalLinkExpiryDate: string = this.originalLinkExpiryDate

    if (!sharedExpiryDate) {
      return
    }

    const areDatesDifferent = !this.#areDatesEqual(
      sharedExpiryDate,
      originalLinkExpiryDate
    )

    this.documentShareFormService.toggleResharedBtn(areDatesDifferent)
    this.cdr.detectChanges()
  }

  #areDatesEqual(date1: Date, date2: string): boolean {
    const d1 = dayjs(date1)
    const d2 = dayjs(date2)
    return d1.isSame(d2, 'day')
  }
}
