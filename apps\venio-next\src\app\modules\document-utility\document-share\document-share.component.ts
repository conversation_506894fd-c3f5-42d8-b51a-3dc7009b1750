import {
  ChangeDetectionStrategy,
  Component,
  OnD<PERSON>roy,
  OnInit,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'
import { DocumentMenuType } from '@venio/shared/models/constants'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import { NotificationDialogComponent } from '@venio/feature/notification'

@Component({
  selector: 'venio-document-share',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './document-share.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentShareComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  constructor(
    private documentsFacade: DocumentsFacade,
    private dialogService: DialogService,
    private searchFacade: SearchFacade
  ) {}

  public ngOnInit(): void {
    this.#selectedDocumentEvent()
  }

  /**
   * When the dialog is closed, it is no point to keep the menu event state
   * @returns {void}
   */
  #resetMenuEventState(): void {
    this.documentsFacade.resetDocumentState('menuEventPayload')
  }

  /**
   * When a menu link is clicked, the loading indicator is displayed to
   * indicate the user that there is something happening in the background
   * such as loading the dialog component lazily and then once loaded, turn off the indicator
   * @returns {void}
   */
  #resetMenuLoadingState(): void {
    this.documentsFacade.resetDocumentState('isDocumentMenuLoading')
  }

  /**
   * Handle the dialog close event such as cleanup or resetting the state
   * @returns {void}
   */
  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetMenuEventState()
      // maybe more cleanup or event trigger if required.
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      content: dialogContent,
      maxWidth: '1400px',
      maxHeight: '750px',
      width: '95%',
      height: '90vh',
      cssClass: '',
    })
  }

  /**
   * Load the dialog component lazily and open it
   * @returns {void}
   */
  #handleLazyLoadedDialog(): void {
    import('@venio/feature/document-share').then((d) => {
      // reset the loading indicator
      this.#resetMenuLoadingState()

      // launch the dialog
      this.#launchDialogContent(d.DocumentShareComponent)

      // once the dialogRef instance is created
      this.#handleEditDialogCloseEvent()
    })
  }

  /**
   * Select the document menu event state when it is triggered
   * @returns {void}
   */
  #selectedDocumentEvent(): void {
    this.documentsFacade.selectDocumentMenuEvent$
      .pipe(
        filter((event) => event === DocumentMenuType.SHARE_DOCUMENT),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#handleDocumentShare()
      })
  }

  /**
   * Handles document share menu selection. It takes the latest document selection, validates and opens document share dialog.
   * @returns {void}
   */
  #handleDocumentShare(): void {
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
    ])
      .pipe(take(1))
      .subscribe(
        ([isBatchSelected, selectedDocs, unselectedDocs, totalHitCount]) => {
          // if no documents is selected, show an error message
          if (
            !this.#validateDocSelection(
              isBatchSelected,
              selectedDocs,
              unselectedDocs,
              totalHitCount
            )
          ) {
            return
          }

          // launch the dialog
          this.#handleLazyLoadedDialog()
        }
      )
  }

  /**
   * Validates document selection. If no document is selected, it shows a notification message.
   * @param {boolean} isBatchSelected flag indicating if all documents were selected as "select all from all pages"
   * @param {number[]} selectedDocs array of selected documents
   * @param {number[]} unselectedDocs array of unselected documents
   * @param {number} totalHitCount total hit count of the search
   * @returns {boolean} true if at least one document is selected, otherwise false.
   */
  #validateDocSelection(
    isBatchSelected: boolean,
    selectedDocs: number[],
    unselectedDocs: number[],
    totalHitCount: number
  ): boolean {
    let selectedDocCount = 0
    if (isBatchSelected) {
      selectedDocCount = totalHitCount - unselectedDocs.length
    } else {
      selectedDocCount = selectedDocs.length
    }
    if (!selectedDocCount) {
      this.#showNotificationMessage(
        'Please select at least one document to share.'
      )
      return false
    }
    return true
  }

  /**
   * Shows a notification dialog with the given message.
   * @param {string} message message to be displayed in the notification dialog
   * @returns {void}
   */
  #showNotificationMessage(message: string): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-warning',
      width: '35rem',
    })

    this.#setDialogInput(notificationDialogRef.content.instance, message)

    notificationDialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.#resetEvents()
      })
  }

  /**
   * Sets the title and message in the notification dialog.
   * @param {NotificationDialogComponent} instance notification dialog instance
   * @param {string} message message to be displayed in the dialog
   * @returns {void}
   */
  #setDialogInput(
    instance: NotificationDialogComponent,
    message: string
  ): void {
    instance.title = 'Document Share'
    instance.message = message
  }

  #resetEvents(): void {
    this.#resetMenuLoadingState()
    this.#resetMenuEventState()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetMenuEventState()
  }
}
