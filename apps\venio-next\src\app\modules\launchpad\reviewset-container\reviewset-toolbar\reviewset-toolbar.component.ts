import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnInit,
  output,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import {
  ButtonComponent,
  DropDownButtonComponent,
} from '@progress/kendo-angular-buttons'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CommonActionTypes, ReportTypes } from '@venio/shared/models/constants'
import { UuidGenerator } from '@venio/util/uuid'
import { DebounceTimer } from '@venio/util/utilities'
import { StartupsFacade, UserRights } from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'
import { UserFacade } from '@venio/data-access/common'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'

/**
 * Interface for menu item local state.
 */
interface MenuItem {
  text?: string
  requiredRight?: UserRights
  actionType: CommonActionTypes
  type?: ReportTypes
  uniqueId: string
}

/**
 * Interface for toolbar action local state.
 */
interface ToolbarAction {
  svgIconPath: string
  cssClass?: string
  menuItems?: MenuItem[]
  type: 'icon-button' | 'dropdown-button' | 'label-button'
  actionType: CommonActionTypes
  title?: string
  label?: string
  uniqueId: string
  requiredRight?: UserRights
}

@Component({
  selector: 'venio-reviewset-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DropDownButtonComponent,
    SVGIconComponent,
    SvgLoaderDirective,
    TooltipsModule,
  ],
  templateUrl: './reviewset-toolbar.component.html',
  styleUrl: './reviewset-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReviewsetToolbarComponent implements OnInit {
  public readonly chevronDownIcon = chevronDownIcon

  private readonly permissionFacade = inject(StartupsFacade)

  private readonly userFacade = inject(UserFacade)

  /**
   * Grab the current user details from the user facade of observable and create a signal.
   */
  public readonly currentUser = toSignal(
    this.userFacade.selectCurrentUserDetails$
  )

  /**
   * Whether the currently logged-in user global role is reviewer.
   * If the global role contains the word 'reviewer', then it is a reviewer which
   * only the reviewer can see the review set tab only.
   */
  public readonly isReviewer = computed(
    () =>
      this.currentUser()
        ?.globalRoleName?.trim()
        .match(/reviewer/i) !== null
  )

  /**
   * Output event for toolbar action click. Emits `CommonActionTypes`.
   * @see CommonActionTypes
   */
  public readonly toolbarActionClick =
    output<Record<CommonActionTypes, object>>()

  public readonly commonActionTypes = CommonActionTypes

  /**
   * Toolbar actions configuration including icon, CSS class, menu items
   * and action type.
   * @see CommonActionTypes
   **/
  public readonly toolbarActions = signal<ToolbarAction[]>([
    {
      svgIconPath: '',
      cssClass: 'v-custom-secondary-button t-uppercase',
      menuItems: [],
      type: 'label-button',
      label: 'Create New',
      title: 'Create New Review Set',
      actionType: CommonActionTypes.REVIEW_SET_CREATE,
      uniqueId: UuidGenerator.uuid,
    },
  ])

  public ngOnInit(): void {
    this.#fetchRights()
  }

  /**
   * Emits the toolbar action click event.
   * @param {CommonActionTypes} actionType - The action type.
   * @param {Object} menuRef - The menu reference.
   * @returns {void}
   */
  @DebounceTimer(200)
  public toolbarActionClicked(
    actionType: CommonActionTypes,
    menuRef: ToolbarAction | MenuItem
  ): void {
    // There might be a review set that view is not updated after the action is clicked.
    // We use this uniqueId to force the view to update in ngFor.
    menuRef['uniqueId'] = UuidGenerator.uuid

    const actionEvent = { [actionType]: menuRef } as unknown as Record<
      CommonActionTypes,
      object
    >

    this.toolbarActionClick.emit(actionEvent)
  }

  #fetchRights(): void {
    this.permissionFacade.fetchUserRights(0)
  }
}
