import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  output,
} from '@angular/core'
import { toSignal } from '@angular/core/rxjs-interop'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  TextBoxComponent,
  TextBoxSuffixTemplateDirective,
} from '@progress/kendo-angular-inputs'
import { DocumentShareFacade } from '@venio/data-access/review'
import { UiPaginationModule, PageArgs } from '@venio/ui/pagination'
import { DebounceTimer } from '@venio/util/utilities'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-shared-document-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    TextBoxComponent,
    TextBoxSuffixTemplateDirective,
    ButtonComponent,
    UiPaginationModule,
    TooltipsModule,
  ],
  templateUrl: './shared-document-toolbar.component.html',
  styleUrl: './shared-document-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharedDocumentToolbarComponent {
  private readonly sharedDocFacade = inject(DocumentShareFacade)

  private readonly sharedDocCurrentPagingInfo = toSignal(
    this.sharedDocFacade.selectSharedDocDetailPagingInfo$
  )

  private readonly shareDocList = toSignal(
    this.sharedDocFacade.selectSharedDocumentList$
  )

  /** Signal for the total case count */
  private readonly totalSharedDocs = computed<number>(() => {
    return this.shareDocList()?.length || 0
  })

  /**
   * Pass any loading state to the sub-toolbar for performing
   * loading actions if required.
   */
  public readonly isLoading = input<boolean>(false)

  public readonly searchChange = output<string>()

  public readonly pagingChange = output<PageArgs>()

  public readonly totalRecords = computed(() => this.totalSharedDocs() || 0)

  public readonly pageSize = computed(() => {
    return this.sharedDocCurrentPagingInfo()?.pageSize || 0
  })

  public readonly currentPage = computed(() => {
    return this.sharedDocCurrentPagingInfo()?.pageNumber || 1
  })

  @DebounceTimer(300)
  public searchValueChange(value: string): void {
    this.searchChange.emit(value)
  }

  public pagingControlChange(arg: PageArgs): void {
    this.pagingChange.emit(arg)
  }
}
