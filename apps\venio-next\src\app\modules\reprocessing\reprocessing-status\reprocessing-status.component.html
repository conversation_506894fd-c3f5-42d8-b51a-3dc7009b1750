<div
  class="t-flex t-border-y-[1px] t-border-[#cccccc] t-p-4 t-justify-between t-items-center t-mt-4 t-px-6">
  <span class="t-font-semibold">Status</span>

  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    data-qa="reprocess-button"
    (click)="showReprocessOption()">
    Reprocess
  </button>
</div>

<div
  class="t-bg-white t-border t-border-[#E0E0E0] t-rounded t-shadow-xl t-p-4 t-flex t-flex-col t-gap-3 t-w-[450px] t-m-5">
  <div>
    <h2 class="t-text-base t-font-semibold t-text-primary">
      Overall Processing
    </h2>
  </div>

  <div class="t-flex t-items-center t-gap-4 t-flex-col">
    <div class="t-block t-w-full">
      <kendo-progressbar
        [value]="statusLoading() ? 0 : overallProgressValue()"
        [progressCssStyle]="getProgressBarStyle(overallProgressValue())"
        [min]="0"
        [max]="100"
        [label]="progressLabel()"
        [animation]="{ duration: 2000 }"
        class="t-flex-1 t-w-full t-h-2 t-rounded-md t-h-3"></kendo-progressbar>
    </div>
    <div class="t-text-sm t-text-[#979797] t-flex t-justify-between t-w-full">
      <span
        >Date
        <span class="t-text-[#000000] t-font-medium">{{
          latestUpdatedDate() === null
            ? 'N/A'
            : (latestUpdatedDate() | date : 'dd MM yyyy')
        }}</span></span
      >
      <span
        >Last updated
        <span class="t-text-[#000000] t-font-medium">{{
          getLastUpdateInfo()
        }}</span></span
      >
    </div>
  </div>
</div>

<div class="t-p-6" *ngIf="reprocessingStatus().length > 0">
  <div class="t-mb-4 t-flex t-items-center t-gap-4">
    <h1 class="t-text-lg t-font-semibold">Processing Details</h1>
    <div class="t-flex t-items-center t-gap-4 t-text-xs t-font-medium">
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#9BD2A7]"></span> COMPLETED
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#FFBC3E]"></span> INPROGRESS
      </div>
      <div class="t-flex t-items-center t-gap-1">
        <span class="t-w-4 t-h-4 t-rounded-sm t-bg-[#EDEBE9]"></span> NOT
        STARTED
      </div>
    </div>
  </div>
  <div class="t-flex t-flex-wrap t-gap-4">
    @if(statusLoading()){
    <kendo-skeleton
      *ngFor="let n of [1, 2]"
      height="213px"
      width="450px"
      shape="rectangle"
      class="t-rounded-md" />
    }@else { @defer{
    <div *ngFor="let status of reprocessingStatus()">
      <venio-custodian-status [reprocessingStatus]="status" />
    </div>
    } @placeholder{
    <kendo-skeleton
      *ngFor="let n of [1, 2]"
      height="213px"
      width="450px"
      shape="rectangle"
      class="t-rounded-md" />
    } }
  </div>
</div>
