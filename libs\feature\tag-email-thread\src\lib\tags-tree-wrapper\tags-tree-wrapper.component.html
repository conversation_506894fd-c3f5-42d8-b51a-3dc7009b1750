<kendo-treelist
  [kendoTreeListHierarchyBinding]="treeNodes"
  childrenField="children"
  [loading]="isDocumentTagsLoading"
  [hasChildren]="hasChildrenNodes"
  kendoTreeListExpandable
  expandBy="TagId"
  [expandedKeys]="expandedKeys"
  (expandedKeysChange)="onExpandedIdsChange($event)"
  [trackBy]="tagTreeTrackByFn"
  [pageSize]="15"
  [hideHeader]="true"
  [autoSize]="true"
  [navigable]="true"
  venioDynamicHeight
  [useMaxHeight]="true"
  [isKendoDialog]="isBulkDocument ? true : false"
  class="!t-border-0 !t-w-full">
  <kendo-treelist-column
    [expandable]="true"
    field="TagName"
    title="TagName"
    [minResizableWidth]="200"
    class="t-flex t-items-baseline !t-border-0">
    <ng-template
      kendoTreeListCellTemplate
      let-column
      let-columnIndex="columnIndex"
      let-dataItem>
      <div
        class="t-flex t-flex-wrap t-flex-col t-grow t-w-10"
        #tagsParentContainer>
        <div
          class="t-text-base t-font-bold t-max-w-[100%]"
          [ngClass]="{
            't-w-56': tagsParentContainer.clientWidth <= 400
          }"
          *ngIf="!dataItem.ParentTagId; else childTag"
          kendoTooltip
          [title]="hasTruncatedText() ? dataItem.TagName : ''"
          venioTextTruncate
          (isTextTruncated)="isTextTruncated($event)">
          {{ dataItem.TagName }}
        </div>
        <ng-template #childTag>
          <div class="t-flex t-flex-col t-w-full">
            <div class="t-flex t-items-center t-space-x-2 t-w-full">
              <input
                #tagCheckboxRef
                type="checkbox"
                [attr.data-qa]="dataItem.TagId"
                id="{{ dataItem.TagId }}"
                class="t-self-auto"
                kendoCheckBox
                size="medium"
                rounded="small"
                [indeterminate]="
                  currentDocumentTagsState &&
                  currentDocumentTagsState[dataItem.TagId] &&
                  currentDocumentTagsState[dataItem.TagId].tagState === null
                "
                [checked]="
                  currentDocumentTagsState &&
                  currentDocumentTagsState[dataItem.TagId] &&
                  currentDocumentTagsState[dataItem.TagId]?.tagState
                "
                [disabled]="
                  !dataItem.WritePermission ||
                  (allowToApplyTag$ | async) === false ||
                  (allowToApplyTag$ | async) === null ||
                  (allowToApplyTag$ | async) === undefined
                "
                (click)="
                  onNodeSelected(
                    dataItem.TagId,
                    $event,
                    tagCheckboxRef,
                    dataItem
                  )
                "
                [ngClass]="
                  getClassNameStringFromTagGroup(dataItem.TagGroupName)
                " />
              <kendo-label
                class="t-flex t-w-[87%] t-text-base t-items-center t-grow-0">
                <span
                  *ngIf="dataItem.TagColor"
                  class="t-h-2 t-w-2 t-float-left t-mx-px t-rounded-full t-mr-1.5"
                  [style.background-color]="dataItem.TagColor"></span>
                <div
                  class="t-flex t-items-center t-w-full"
                  [ngClass]="getClassForTagName(dataItem)">
                  <div class="t-flex-1 t-w-[75%] t-flex t-items-center">
                    <span
                      class="t-max-w-[100%] t-overflow-hidden t-text-ellipsis t-whitespace-no-wrap"
                      kendoTooltip
                      [title]="dataItem.TagName"
                      >{{ dataItem.TagName }}</span
                    >
                    @if(!isExternalUser && !reviewSetState.isBatchReview()){
                    <span
                      class="t-relative t-flex t-flex-0 t-gray-400 t-py-0 t-pl-2">
                      <button
                        kendoButton
                        *ngIf="!isBulkDocument"
                        kendoTooltip
                        fillMode="clear"
                        title="Search"
                        [svgIcon]="searchIconSvg"
                        #parentElTag
                        size="none"
                        [attr.data-qa]="dataItem.TagId + '-search-icon'"
                        (click)="searchByTag(dataItem)">
                        <!-- <span
                            [parentElement]="parentElTag.element"
                            venioSvgLoader
                            hoverColor="#FEB202"
                            color="#979797"
                            svgUrl="assets/svg/icon-search.svg"
                            height="1.1rem"
                            width="1.1rem">
                            <kendo-loader size="small"></kendo-loader>
                          </span> -->
                      </button>
                    </span>
                    }
                    <span
                      class="t-relative t-flex t-gray-400 t-py-1 t-pl-2"
                      *ngIf="showTagCommentIcon(dataItem)">
                      <button
                        kendoButton
                        kendoTooltip
                        fillMode="clear"
                        title="Comment"
                        #parentElTag
                        size="none"
                        [attr.data-qa]="dataItem.TagId + '-comment-icon'"
                        (click)="toggleOptionalComment(dataItem)">
                        <span
                          [parentElement]="parentElTag.element"
                          venioSvgLoader
                          hoverColor="#FEB202"
                          color="#979797"
                          svgUrl="assets/svg/icon-comment-universal.svg"
                          height="1.1rem"
                          width="1.1rem">
                          <kendo-loader size="small"></kendo-loader>
                        </span>
                      </button>
                      <span
                        class="t-text-error t-mx-1"
                        *ngIf="isTagCommentRequired(dataItem)"
                        >*</span
                      >
                    </span>
                    <span
                      *ngIf="tagRuleIconState[dataItem.TagId]"
                      id="{{ 'tag_rule_icon' + dataItem.TagId }}"
                      class="t-pl-0.5"
                      (click)="showTagRulePopOver(dataItem)">
                      <kendo-svg-icon
                        class="t-text-[var(--tb-kendo-success-100)] t-w-[1.25rem] t-h-[1.25rem] t-cursor-pointer"
                        [icon]="infoCircleIcon"
                        kendoPopoverAnchor
                        [popover]="tagRuleDescriptionPopover"
                        showOn="click"
                        #anchor="kendoPopoverAnchor"></kendo-svg-icon>
                    </span>
                  </div>
                </div>
              </kendo-label>
            </div>
            <div class="t-flex t-flex-col t-max-w-[100%]">
              <ng-container *ngIf="showTagCommentsComponent(dataItem)">
                <ng-container
                  *ngComponentOutlet="
                    tagCommentsComponentItems.component | async;
                    inputs: getTagCommentInputValue(
                      dataItem,
                      tagCommentsComponentItems.inputs
                    )
                  "></ng-container>
              </ng-container>
            </div>
          </div>
        </ng-template>
      </div>
    </ng-template>
  </kendo-treelist-column>
</kendo-treelist>

<kendo-popover
  #tagRuleDescriptionPopover
  position="bottom"
  [animation]="{ type: 'slide', direction: 'right', duration: 100 }">
  <ng-template kendoPopoverBodyTemplate>
    <ng-container *ngComponentOutlet="tagRuleInfoComp | async"></ng-container>
  </ng-template>
</kendo-popover>
