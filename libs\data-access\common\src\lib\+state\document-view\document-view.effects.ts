import { inject, Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import {
  catchError,
  from,
  map,
  Observable,
  pipe,
  switchMap,
  tap,
  UnaryFunction,
} from 'rxjs'
import * as DocumentViewActions from './document-view.actions'
import { HttpErrorResponse } from '@angular/common/http'
import { DocumentViewService } from '../../services'
import { CaseConvertorService } from '@venio/util/utilities'
import {
  ResponseModel,
  SearchFieldModel,
} from '@venio/shared/models/interfaces'
import { ActivatedRoute } from '@angular/router'
import { Store } from '@ngrx/store'
import { ViewService } from '@venio/data-access/review'

@Injectable()
export class DocumentViewEffects {
  private readonly actions$ = inject(Actions)

  private readonly store = inject(Store)

  private readonly documentViewService = inject(DocumentViewService)

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly viewService = inject(ViewService)

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public fetchDocumentViewList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentViewActions.fetchDocumentViewList),
      fetch({
        run: () => {
          return this.documentViewService
            .fetchDocumentList(this.projectId)
            .pipe(
              map((documentViewListSuccessResponse) =>
                DocumentViewActions.fetchDocumentViewListSuccess({
                  documentViewListSuccessResponse,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const documentViewListErrorResponse = error.error
          return DocumentViewActions.fetchDocumentViewListFailure({
            documentViewListErrorResponse,
          })
        },
      })
    )
  )

  public fetchSearchFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentViewActions.fetchSearchFields),
      fetch({
        run: () => {
          return this.documentViewService
            .fetchSearchFieldList(this.projectId)
            .pipe(
              switchMap((response) => {
                const camelCaseConvertorService = new CaseConvertorService()
                return from(
                  camelCaseConvertorService.convertToCase<SearchFieldModel>(
                    response,
                    'camelCase'
                  )
                )
              }),
              map((searchFields) =>
                DocumentViewActions.fetchSearchFieldsSuccess({ searchFields })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const searchFieldsErrorResponse = error.error
          return DocumentViewActions.fetchSearchFieldsFailure({
            searchFieldsErrorResponse,
          })
        },
      })
    )
  )

  #reloadViewList(): void {
    this.store.dispatch(
      DocumentViewActions.fetchDocumentViewList({
        projectId: this.projectId,
      })
    )
  }

  #handleCommonTaskPipe(
    response?: ResponseModel
  ): UnaryFunction<Observable<ResponseModel>, Observable<any>> {
    return pipe(
      map((addOrUpdateViewSuccessResponse) => {
        // The main action response should be used if it is available
        // Fall back to the response from the selected action
        return DocumentViewActions.addOrUpdateViewSuccess({
          addOrUpdateViewSuccessResponse:
            response || addOrUpdateViewSuccessResponse,
        })
      }),
      tap(() => this.#reloadViewList()),
      catchError((error: unknown) => {
        throw error
      })
    )
  }

  public addOrUpdateView$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentViewActions.addOrUpdateView),
      fetch({
        run: ({ view, shouldLoadAndApply }) => {
          return this.documentViewService
            .addOrUpdateView(view)
            .pipe(
              shouldLoadAndApply
                ? switchMap((response) =>
                    this.viewService
                      .saveUserDefaultView(
                        this.projectId,
                        response.data || view.viewId
                      )
                      .pipe(this.#handleCommonTaskPipe(response))
                  )
                : this.#handleCommonTaskPipe()
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const addOrUpdateViewErrorResponse = error.error
          return DocumentViewActions.addOrUpdateViewFailure({
            addOrUpdateViewErrorResponse,
          })
        },
      })
    )
  )

  public fetchViewByViewId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DocumentViewActions.fetchViewByViewId),
      fetch({
        run: ({ viewId }) => {
          return this.documentViewService.fetchViewByViewId(viewId).pipe(
            map((response) =>
              DocumentViewActions.storeCurrentFormData({
                currentFormData: response?.data,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const viewByViewIdErrorResponse = error.error
          return DocumentViewActions.fetchViewByViewIdFailure({
            viewByViewIdErrorResponse,
          })
        },
      })
    )
  )
}
