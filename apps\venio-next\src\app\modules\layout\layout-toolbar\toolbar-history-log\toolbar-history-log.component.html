<kendo-dropdownbutton
  class="v-custom-dropdownbtn-invert v-custom-btn-group"
  buttonClass="t-m-0 t-max-h-[34px] t-p-1 !t-border-[#BEBEBE] hover:!t-bg-[#1EBADC] hover:!t-border-[#1EBADC] t-rounded"
  [data]="historyDropdownData"
  (itemClick)="onMenuClick($event, dialogActions)"
  #parentElTag
  [popupSettings]="{
    align: 'right',
    popupClass:
      '!t-mt-2.5 !t-p-[0.625rem] !t-border-0  !t-shadow-lg v-custom-theme'
  }">
  <span
    class="t-w-full invert-button"
    [parentElement]="kendoButtonEl"
    venioSvgLoader
    color="#1EBADC"
    hoverColor="#FFFFFF"
    svgUrl="assets/svg/icon-history.svg"
    height="1.5rem"
    width="3rem">
    <kendo-loader size="small"></kendo-loader>
  </span>
</kendo-dropdownbutton>

<ng-template #dialogActions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button kendoButton (click)="close()" fillMode="outline" themeColor="dark">
      CANCEL
    </button>
  </div>
</ng-template>

<div kendoDialogContainer></div>
