import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  inject,
  Injector,
  OnD<PERSON>roy,
  OnInit,
  runInInjectionContext,
  signal,
  Type,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { SVGIcon } from '@progress/kendo-angular-icons'
import {
  arrowRotateCwIcon,
  checkIcon,
  chevronLeftIcon,
  plusIcon,
  paletteIcon,
} from '@progress/kendo-svg-icons'
import {
  TabStripComponent,
  TabStripModule,
} from '@progress/kendo-angular-layout'
import {
  ButtonGroupModule,
  ButtonModule,
} from '@progress/kendo-angular-buttons'
import {
  CheckBoxModule,
  ColorPickerModule,
  RadioButtonModule,
  TextAreaModule,
  TextBoxComponent,
  TextBoxModule,
} from '@progress/kendo-angular-inputs'
import {
  DialogModule,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import {
  ComboBoxModule,
  DropDownListModule,
} from '@progress/kendo-angular-dropdowns'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { GridModule } from '@progress/kendo-angular-grid'
import { LabelModule } from '@progress/kendo-angular-label'
import { UserGroupRightCheckDirective } from '@venio/feature/shared/directives'
import { TagsFacade } from '@venio/data-access/common'
import { ActivatedRoute } from '@angular/router'
import { combineLatest, filter, Subject, take, takeUntil } from 'rxjs'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import {
  LoaderModule,
  SkeletonModule,
} from '@progress/kendo-angular-indicators'
import {
  DuplicateTagPropagation,
  GroupModel,
  NearDuplicateTagPropagation,
  ResponseModel,
  TagGroupModel,
  TagPropagationSettingModel,
  TagsModel,
} from '@venio/shared/models/interfaces'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  NotificationModule,
  NotificationService,
  Type as NotificationType,
} from '@progress/kendo-angular-notification'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { debounceTime, tap } from 'rxjs/operators'
import { cloneDeep, debounce } from 'lodash'
import { ProjectRoleComponent } from '@venio/feature/project-role'
import { ReviewSetStateService, UserRights } from '@venio/data-access/review'
import { DocumentTagFacade } from '@venio/data-access/document-utility'

@Component({
  selector: 'venio-tag-form',
  standalone: true,
  imports: [
    CommonModule,
    ButtonGroupModule,
    ButtonModule,
    CheckBoxModule,
    ColorPickerModule,
    DialogModule,
    DropDownListModule,
    FormsModule,
    GridModule,
    LabelModule,
    RadioButtonModule,
    TabStripModule,
    TextAreaModule,
    TextBoxModule,
    TreeListModule,
    LoaderModule,
    ReactiveFormsModule,
    NotificationModule,
    CheckBoxModule,
    ComboBoxModule,
    SkeletonModule,
    ProjectRoleComponent,
    UserGroupRightCheckDirective,
  ],
  templateUrl: './tag-form.component.html',
  styleUrl: './tag-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagFormComponent implements OnInit, AfterViewInit, OnDestroy {
  public readonly toDestroy$ = new Subject<void>()

  @ViewChild('tagGroupTabStrip')
  public tagGroupTabStrip: TabStripComponent

  public readonly commonActionTypes = CommonActionTypes

  public readonly rights = UserRights

  private formBuilder = inject(FormBuilder)

  private tagsFacade = inject(TagsFacade)

  private documentTagFacade = inject(DocumentTagFacade)

  private activatedRoute = inject(ActivatedRoute)

  private dialogRef = inject(DialogRef, { optional: true })

  private changeDetectorRef = inject(ChangeDetectorRef)

  private dialogService = inject(DialogService)

  private notificationService = inject(NotificationService)

  public reviewSetState = inject(ReviewSetStateService)

  private injector = inject(Injector)

  public isTagAddUpdate = signal<boolean>(false)

  public isCodingAddUpdate = signal<boolean>(false)

  public isCodingFormValid = signal<boolean>(false)

  public refreshSvg: SVGIcon = arrowRotateCwIcon

  public plusSvg: SVGIcon = plusIcon

  public checkSvg: SVGIcon = checkIcon

  public leftSvg: SVGIcon = chevronLeftIcon

  public colorPalette: SVGIcon = paletteIcon

  public tagGroup: TagGroupModel[]

  public projectGroup: GroupModel[]

  private parentTags: TagsModel[]

  public filteredParentTags: TagsModel[]

  @ViewChild('tagGroupInput')
  public tagGroupInput: TextBoxComponent

  public tagGroupFormGroup: FormGroup

  public tagFormGroup: FormGroup

  public get tagGroupNameControl(): AbstractControl {
    return this.tagGroupFormGroup.get('tagGroupName')
  }

  public get tagGroupIdControl(): AbstractControl {
    return this.tagGroupFormGroup.get('tagGroupId')
  }

  public get tagGroupAccessControl(): AbstractControl {
    return this.tagFormGroup.get('groupAccess')
  }

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public tagGroupListLazyComponent: Promise<Type<unknown>>

  public isAddOrUpdateGatLoading$ =
    this.tagsFacade.selectIsAddOrUpdateGatLoading$

  public isTagSecurityPermissionAllowed$ =
    this.tagsFacade.selectIsTagSecurityPermissionAllowed$

  public duplicateTagPropagation = signal([
    {
      value: DuplicateTagPropagation.AllDuplicatesCase,
      label: 'Propagate tags to all duplicates in the case',
    },
    {
      value: DuplicateTagPropagation.NearDuplicateCase,
      label: 'Propagate tags to all duplicates in the selected scope',
    },
    {
      value: DuplicateTagPropagation.NoDuplicates,
      label: 'Do not propagate tags to duplicates',
    },
  ])

  public nearDuplicateTagPropagation = signal([
    {
      value: NearDuplicateTagPropagation.NearDuplicateWholeScope,
      label: 'Propagate tags to near duplicate group in whole case',
    },
    {
      value: NearDuplicateTagPropagation.NearDuplicateSelectedScope,
      label: 'Propagate tags to near duplicate group in selected scope',
    },
    {
      value: NearDuplicateTagPropagation.NoNearDuplicates,
      label: 'Do not propagate tags to near duplicate groups ',
    },
  ])

  private reviewSetId: number

  protected readonly CommonActionTypes = CommonActionTypes

  protected tagPropagationSetting: TagPropagationSettingModel

  public ngOnInit(): void {
    this.#initTagFormGroup()
    this.#initTagGroupFormGroup()
    this.#loadLazyComponents()
    this.#monitorTagFormClosed()
    this.#selectTagPropagationSettingResponses()
    this.#initReviewSetId()
  }

  public ngAfterViewInit(): void {
    this.#tagFormValueChanges()
    this.#selectTagGroupResponse()
    this.#selectTagGroupActionResponses()
    this.#selectSelectedTagGroup()
    this.#selectTagGroupDeletionResponses()
    this.#populateParentTagByGroupId()
    this.#selectSelectedTag()
    this.#selectAddOrUpdateTagNotified()
    this.#selectTagAddOrUpdateResponses()
    //this.#activateInitialTab() //Remove once everything is working
  }

  public ngOnDestroy(): void {
    this.#resetTagTreeFetchState()
    this.#resetTagGroupFetchState()
    this.#resetTagGroupActionState()
    this.#resetTagAddOrUpdateState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #initReviewSetId(): void {
    this.reviewSetId = this.reviewSetState.isBatchReview()
      ? this.reviewSetState.reviewSetId()
      : 0
  }

  #selectAddOrUpdateTagNotified(): void {
    this.tagsFacade.selectIsAddOrUpdateTagFieldNotified$
      .pipe(
        filter((isNotified) => isNotified),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isNotified) => {
        if (isNotified) {
          this.#addOrUpdateTag()
        }
      })
  }

  public setFlagForTagAddUpdate(): void {
    this.changeDetectorRef.markForCheck()
    this.isTagAddUpdate.set(true)
    this.#fetchTagPropagationSettings()
  }

  public tagGroupActionClicked(actionType: CommonActionTypes): void {
    switch (actionType) {
      case CommonActionTypes.ADD:
        this.#addOrUpdateTagGroup()
        break
      case CommonActionTypes.RESET:
        this.tagGroupFormGroup.reset()
        this.#resetTagGroupActionState()
        break
    }
  }

  public parentTagFilter(value: string): void {
    this.filteredParentTags = this.parentTags.filter(
      (s) => s.tagName.toLowerCase().indexOf(value.toLowerCase()) !== -1
    )
  }

  public projectRoleChange(data: GroupModel[]): void {
    this.projectGroup = [...data]
  }

  //Remove once everything is working
  // #activateInitialTab(): void {
  //   setTimeout(() => {
  //     this.changeDetectorRef.markForCheck()
  //     this.mainTabStrip.selectTab(0)
  //   })
  // }

  #resetTagGroupFetchState(): void {
    this.tagsFacade.resetTagsState([
      'tagGroupActionErrorResponse',
      'tagGroupActionSuccessResponse',
    ])
  }

  #resetTagTreeFetchState(): void {
    this.tagsFacade.resetTagsState([
      'isTagTreeLoading',
      'tagTreeSuccessResponse',
      'tagTreeErrorResponse',
    ])
  }

  #resetTagGroupActionState(): void {
    this.tagsFacade.resetTagsState([
      'selectedTagGroup',
      'tagDeleteErrorResponse',
      'tagDeleteSuccessResponse',
    ])
  }

  #resetTagAddOrUpdateState(): void {
    this.tagsFacade.resetTagsState([
      'selectedTag',
      'tagAddOrUpdateErrorResponse',
      'tagAddOrUpdateSuccessResponse',
      'tagFormGroupValid',
      'isAddOrUpdateTagFieldNotified',
    ])
  }

  #fetchTagPropagationSettings(): void {
    this.tagsFacade.fetchTagPropagationSetting(this.projectId)
  }

  #initTagGroupFormGroup(): void {
    this.tagGroupFormGroup = this.formBuilder.group({
      tagGroupId: null,
      tagGroupName: ['', Validators.required],
      isExclusive: false,
      reviewSetId: -1,
    })
  }

  #initTagFormGroup(): void {
    this.tagFormGroup = this.formBuilder.group({
      tagId: [0],
      tagCommentRequirement: ['None'],
      commentLabel: [null],
      tagName: ['', Validators.required],
      tagGroupId: [null, Validators.required],
      parentTagId: [null],
      reviewSetId: [-1],
      description: [null],
      color: [null],
      groupAccess: [[]],
      isSystemTag: [false],
      tagIdLineage: ['1'],
      enforceChildTagRule: [false],
      tagSecurityLevel: ['ALL_ACCESS_GENERAL_TAG'],
      tagPropagationSetting: this.formBuilder.group({
        dupTagOption: [DuplicateTagPropagation.NearDuplicateCase],
        propagatePCSet: [true],
        includeEmailThread: [true],
        nddTagOption: [NearDuplicateTagPropagation.NearDuplicateSelectedScope],
      }),
    })
  }

  #setTagPropagationSetting(setting: TagPropagationSettingModel): void {
    this.changeDetectorRef.markForCheck()
    this.tagFormGroup.patchValue({
      tagPropagationSetting: {
        dupTagOption: setting?.dupTagOption,
        propagatePCSet: setting?.PropagatePCSet,
        includeEmailThread: setting?.IncludeEmailThread,
        nddTagOption: setting?.nddTagOption,
      },
    })
    this.tagFormGroup.updateValueAndValidity()
  }

  #patchTagPropagationSetting(setting: TagPropagationSettingModel): void {
    this.changeDetectorRef.markForCheck()
    this.tagFormGroup.patchValue({
      tagPropagationSetting: {
        dupTagOption: setting?.dupTagOption,
        propagatePCSet: setting?.PropagatePCSet,
        includeEmailThread: setting?.IncludeEmailThread,
        nddTagOption: setting?.nddTagOption,
      },
    })
    this.tagFormGroup.updateValueAndValidity()
  }

  #patchTagFormGroup(tag: TagsModel): void {
    tag = cloneDeep(tag)
    // If the server has returned null explicitly, we need to set the default value.
    if (
      tag.tagPropagationSetting.dupTagOption === null ||
      tag.tagPropagationSetting.dupTagOption === undefined
    ) {
      tag.tagPropagationSetting.dupTagOption =
        this.tagPropagationSetting.dupTagOption
    }
    if (
      tag.tagPropagationSetting.propagatePCSet === null ||
      tag.tagPropagationSetting.propagatePCSet === undefined
    ) {
      tag.tagPropagationSetting.propagatePCSet =
        this.tagPropagationSetting.PropagatePCSet
    }
    if (
      tag.tagPropagationSetting.includeEmailThread === null ||
      tag.tagPropagationSetting.includeEmailThread === undefined
    ) {
      tag.tagPropagationSetting.includeEmailThread =
        this.tagPropagationSetting.IncludeEmailThread
    }
    if (
      tag.tagPropagationSetting.nddTagOption === null ||
      tag.tagPropagationSetting.nddTagOption === undefined
    ) {
      tag.tagPropagationSetting.nddTagOption =
        this.tagPropagationSetting.nddTagOption
    }

    this.changeDetectorRef.markForCheck()

    this.tagFormGroup.patchValue({
      tagId: tag.tagId,
      tagCommentRequirement: tag.tagCommentRequirement,
      commentLabel: tag.commentLabel,
      tagName: tag.tagName,
      tagGroupId: tag.tagGroupId,
      parentTagId: tag.parentTagId,
      description: tag.description,
      color: tag.color,
      isInUse: null,
      groupAccess: tag.groupAccess,
      isSystemTag: tag.isSystemTag,
      tagIdLineage: tag.tagIdLineage,
      enforceChildTagRule: tag.enforceChildTagRule,
      tagSecurityLevel: tag.tagSecurityLevel,
      reviewSetId: tag.reviewSetId,
      tagPropagationSetting: tag.tagPropagationSetting,
    })
    this.tagFormGroup.updateValueAndValidity()
  }

  #resetTagFormWithDefaultValues(): void {
    this.changeDetectorRef.markForCheck()
    this.tagFormGroup.reset({
      tagId: 0,
      tagCommentRequirement: 'None',
      commentLabel: null,
      tagName: '',
      tagGroupId: null,
      parentTagId: null,
      description: null,
      color: null,
      groupAccess: [],
      isSystemTag: false,
      tagIdLineage: '1',
      enforceChildTagRule: false,
      tagSecurityLevel: 'ALL_ACCESS_GENERAL_TAG',
      reviewSetId: -1,
      tagPropagationSetting: {
        dupTagOption: DuplicateTagPropagation.NearDuplicateCase,
        propagatePCSet: true,
        includeEmailThread: true,
        nddTagOption: NearDuplicateTagPropagation.NearDuplicateSelectedScope,
      },
    })
  }

  #tagFormValueChanges(): void {
    this.tagFormGroup.valueChanges
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((value) => {
        this.tagsFacade.notifyTagFormGroupValid(this.tagFormGroup.valid)
      })
  }

  #resetTagGroupFormGroupWithDefaultValues(): void {
    this.tagGroupFormGroup.reset({
      tagGroupId: null,
      tagGroupName: '',
      isExclusive: false,
      reviewSetId: -1,
    })
  }

  #populateParentTagByGroupId(): void {
    const tagGroupIdControl = this.tagFormGroup.get('tagGroupId')
    combineLatest([
      tagGroupIdControl.valueChanges,
      this.tagsFacade.selectTagTreeSuccessResponse$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(([tagGroupId, tagTreeResponse]) => {
        this.changeDetectorRef.markForCheck()
        const tagGroup = this.tagGroup?.find(
          (tg) => tg.tagGroupId === tagGroupId
        )
        if (tagGroup?.isExclusive) {
          this.filteredParentTags = []
          this.tagFormGroup.get('parentTagId').disable()
          return
        }

        this.tagFormGroup.get('parentTagId').enable()
        const tagTree: TagsModel[] = tagTreeResponse?.data || []
        this.changeDetectorRef.markForCheck()
        // reset if already selected value
        this.tagFormGroup.get('parentTagId').reset()
        // filter the tags by group ID
        this.parentTags = tagTree.filter((t) => t.tagGroupId === tagGroupId)
        // since the combobox has filter, we need to assign the primary values initially
        this.filteredParentTags = this.parentTags.slice()
      })

    tagGroupIdControl.updateValueAndValidity()
  }

  #showMessage(content = '', type: NotificationType): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 7000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #addOrUpdateTagGroup(): void {
    const data = this.tagGroupFormGroup.getRawValue() as TagGroupModel
    this.tagsFacade.addOrUpdateTagGroup({
      projectId: this.projectId,
      actionType:
        data.tagGroupId > 0 ? CommonActionTypes.EDIT : CommonActionTypes.ADD,
      data,
    })
  }

  #loadLazyComponents(): void {
    this.tagGroupListLazyComponent = import(
      '../tag-group-list/tag-group-list.component'
    ).then(({ TagGroupListComponent }) => TagGroupListComponent)
  }

  #selectTagGroupResponse(): void {
    this.tagsFacade.fetchTagGroup(this.projectId, this.reviewSetId)
    combineLatest([
      this.tagsFacade.selectTagGroupSuccessResponse$,
      this.tagsFacade.selectTagGroupErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.tagGroup = success.data
      })
  }

  #selectTagGroupActionResponses(): void {
    combineLatest([
      this.tagsFacade.selectTagGroupActionSuccessResponse$,
      this.tagsFacade.selectTagGroupActionErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.changeDetectorRef.markForCheck()
        if (!success && !error) return

        this.#resetTagGroupFetchState()

        const { message, status } =
          success || error || ({ status: '', message: '' } as ResponseModel)

        const match = status.match(/success|error/gi)
        const style = match?.[0].toLowerCase()

        if (!style) return

        this.#showMessage(message, {
          style,
        } as NotificationType)

        if (error) return

        this.tagGroupFormGroup.reset()

        this.#fetchTagGroup()
        this.#reloadTagGroupData()
      })
  }

  #selectTagPropagationSettingResponses(): void {
    combineLatest([
      this.tagsFacade.selectTagPropagationSettingSuccessResponse$,
      this.tagsFacade.selectTagPropagationSettingErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return
        // TODO: if we need to investigate the issue, use `error`
        this.#setTagPropagationSetting(success.data)
        this.tagPropagationSetting = success.data
        this.#patchTagPropagationSetting(success.data)
      })
  }

  #addOrUpdateTag(): void {
    const data = this.#prepareTagData()

    data.groupAccess = this.#createGroupAccess(data)

    this.tagsFacade.addOrUpdateTag({
      projectId: this.projectId,
      actionType: this.#determineActionType(data.tagId),
      data,
    })
  }

  /**
   * Prepares tag data from the form group.
   * @returns {TagsModel} The prepared tag data.
   */
  #prepareTagData(): TagsModel {
    const data = this.tagFormGroup.getRawValue() as TagsModel
    data.parentTagId = this.#normalizeParentTagId(data.parentTagId)
    return data
  }

  /**
   * Normalizes the parentTagId to ensure it's not null or undefined.
   * @param {number | null | undefined} parentTagId - The original parentTagId.
   * @returns {number} The normalized parentTagId.
   */
  #normalizeParentTagId(parentTagId: number | null | undefined): number {
    return parentTagId ?? -1
  }

  /**
   * Creates group access data based on the project group and tag data.
   * @param {TagsModel} data - The tag data.
   * @returns {GroupModel[]} An array of group access objects.
   */
  #createGroupAccess(data: TagsModel): GroupModel[] {
    return (
      this.projectGroup?.map((group) =>
        this.#createBaseGroupAccessObject(group, data.tagId)
      ) || []
    )
  }

  /**
   * Creates a base group access object, combining group details with tag ID if applicable.
   * @param {GroupModel} group - The group data.
   * @param {number} tagId - The tag ID.
   * @returns {GroupModel} The group access object.
   */
  #createBaseGroupAccessObject(group: GroupModel, tagId: number): GroupModel {
    const baseObj = {
      groupId: group.groupId,
      permission: group.permission,
    } as GroupModel
    const tagIdObj = tagId > 0 ? { tagId } : {}
    return { ...baseObj, ...tagIdObj } as GroupModel
  }

  /**
   * Determines the action type (Add or Edit) based on the tag ID.
   * @param {number} tagId - The tag ID.
   * @returns {CommonActionTypes} The determined action type.
   */
  #determineActionType(tagId: number): CommonActionTypes {
    return tagId > 0 ? CommonActionTypes.EDIT : CommonActionTypes.ADD
  }

  #fetchTagGroup(): void {
    this.tagsFacade.fetchTagGroup(this.projectId, this.reviewSetId)
  }

  #reloadTagGroupData(): void {
    this.documentTagFacade.reloadTagGroupData(true)
  }

  #focusTagGroupInput(tagGroupName: string): void {
    if (this.tagGroupInput?.input?.nativeElement) {
      const inputElement = this.tagGroupInput.input.nativeElement
      const length = tagGroupName.length
      inputElement.setSelectionRange(length, length)
      inputElement.focus()
    }
  }

  #selectTagGroupDeletionResponses(): void {
    combineLatest([
      this.tagsFacade.selectDeleteTagSuccessResponse$,
      this.tagsFacade.selectDeleteTagErrorResponse$,
    ])
      .pipe(
        filter(
          ([success, error]) =>
            (!!success || !!error) &&
            this.tagGroupTabStrip?.tabs?.get(1)?.selected
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.changeDetectorRef.markForCheck()

        if (!success && !error) return

        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })

        this.#resetTagGroupActionState()

        this.tagGroupFormGroup.reset()

        if (error) return

        this.#fetchTagGroup()
        this.#reloadTagGroupData()
      })
  }

  #selectTagAddOrUpdateResponses(): void {
    combineLatest([
      this.tagsFacade.selectTagAddOrUpdateSuccessResponse$,
      this.tagsFacade.selectTagAddOrUpdateErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.changeDetectorRef.markForCheck()
        if (!success && !error) return

        this.#resetTagAddOrUpdateState()

        const { message, status } =
          success || error || ({ status: '', message: '' } as ResponseModel)

        const match = status.match(/success|error/gi)
        const style = match?.[0].toLowerCase()

        if (!style) return

        this.#showMessage(message, {
          style,
        } as NotificationType)

        this.tagsFacade.notifyIsAddOrUpdateTagField(false)

        if (error) return

        this.isTagAddUpdate.set(false)

        this.tagsFacade.notifyToSwitchTagView(true)

        this.#resetTagFormWithDefaultValues()

        this.tagsFacade.fetchTagTree(this.projectId, this.reviewSetId)
      })
  }

  #setTagGroupForEdit(data: TagGroupModel): void {
    const { tagGroupName, isExclusive, reviewSetId, tagGroupId } = data
    this.changeDetectorRef.markForCheck()
    this.tagGroupFormGroup.patchValue({
      tagGroupId,
      tagGroupName,
      isExclusive,
      reviewSetId,
    })
    this.#focusTagGroupInput(tagGroupName)
  }

  #confirmTagGroupDeletion(tagGroupId: number): void {
    const dialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
    })

    this.#setDialogInput(dialogRef.content.instance)

    dialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => this.#performTaskAfterConfirmation(tagGroupId))
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Confirm Tag Deletion'
    instance.message = `You will lose all tag(s) even if the file(s) are associated with tags in this tag group.
                        Are you sure you want to delete the selected tag group and all tag in this group?`
  }

  #performTaskAfterConfirmation(tagGroupId: number): void {
    const deleteTagGroup = true
    this.tagsFacade.deleteTag(this.projectId, tagGroupId, deleteTagGroup)
  }

  #selectSelectedTagGroup(): void {
    this.tagsFacade.selectSelectedTagGroup$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(({ selected, actionType }) => {
        switch (actionType) {
          case CommonActionTypes.EDIT:
            this.#setTagGroupForEdit(selected)
            break
          case CommonActionTypes.DELETE:
            this.#confirmTagGroupDeletion(selected.tagGroupId)
            break
        }
      })
  }

  //Remove once everything is working
  // #setDialogTitle(actionType: CommonActionTypes): void {
  //   this.dialogTitle =
  //     actionType === CommonActionTypes.CLONE
  //       ? 'Clone - Tag'
  //       : actionType === CommonActionTypes.EDIT
  //       ? 'Edit - Tag'
  //       : 'Add - Tags'
  // }

  #selectSelectedTag(): void {
    this.tagsFacade.selectSelectedTag$
      .pipe(
        filter((data) => Boolean(data?.selected?.tagName)),
        tap(() => {
          this.setFlagForTagAddUpdate()
        }),
        debounceTime(500),
        takeUntil(this.toDestroy$)
      )
      .subscribe(({ selected, actionType }) => {
        this.changeDetectorRef.markForCheck()
        this.tagGroupTabStrip?.selectTab(0)
        this.#patchTagFormGroup(selected)
        //this.#setDialogTitle(actionType) //Remove once everything is working
      })
  }

  /**
   * Monitors the state of the tag form and resets it when certain conditions are met.
   * This function is wrapped inside `runInInjectionContext` to ensure it has access to the
   * required Angular dependencies.
   * It uses an effect to continuously monitor the `isTagAddUpdate` flag.
   * When this flag is not set (indicating that a tag addition or update operation is not in progress),
   * the function resets the tag form and tag group form to their default values.
   * This is essential
   * to maintain a clean state for the next user interactions with the form.
   *
   * @returns {void}
   */
  #monitorTagFormClosed(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        debounce(() => {
          if (this.isTagAddUpdate()) return
          this.#resetTagFormWithDefaultValues()
          this.#resetTagGroupFormGroupWithDefaultValues()
          // Reach this far, the reset has happened and need to fetch the latest default setting
          // that is derived from server
          this.#fetchTagPropagationSettings()
        }, 1000)
      )
    )
  }
}
