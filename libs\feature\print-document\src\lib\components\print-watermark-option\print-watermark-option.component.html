<form [formGroup]="waterMarkOption">
  <div class="t-p-1">
    <div class="t-flex t-items-center">
      <input
        id="add-water-mark"
        class="custom-checkbox"
        type="checkbox"
        formControlName="addWaterMark"
        kendoCheckBox
        #addWaterMarkCheckbox />
      <kendo-label
        class="k-checkbox-label t-top-[1px]"
        for="add-water-mark"
        text="Add Watermark"
        [ngClass]="{
          't-text-primary t-font-medium':
            waterMarkOption.get('addWaterMark')?.value &&
            !addWaterMarkCheckbox.disabled
        }"></kendo-label>
    </div>
    <div class="t-w-full t-flex t-flex-row t-mt-3">
      <!-- Slipsheet text area div-->
      <div
        class="t-bg-white t-bg-no-repeat t-border-[1px] t-border-solid t-border-[#BEBEBE] t-rounded-[4px] t-opacity-100 t-p-3 t-flex t-flex-col t-w-full"
        [ngClass]="{
          't-pointer-events-none t-cursor-not-allowed t-opacity-50':
            !isAddWatemarkChecked()
        }">
        <div class="t-px-0 t-flex t-flex-row t-gap-x-2">
          <div class="t-flex t-flex-row t-gap-x-2 t-w-[310px]">
            <kendo-dropdownlist
              class="t-w-2/5"
              id="font-text"
              [data]="fontTextList"
              formControlName="fontText"
              [popupSettings]="{ width: 225 }"
              data-qa="slipsheet-font-name-dropdown"></kendo-dropdownlist>

            <kendo-dropdownlist
              class="t-w-1/4"
              id="font-size"
              [data]="fontSizeList"
              data-qa="slipsheet-font-size-dropdown"
              formControlName="fontSize"></kendo-dropdownlist>

            <kendo-buttongroup [selection]="selectionMode">
              <button
                kendoButton
                [svgIcon]="boldSVG"
                title="Bold"
                [value]="'Bold'"
                [ngClass]="{
                  'k-selected k-focus': isSelected('Bold')
                }"
                (click)="onFontStyleChange('Bold')"></button>
              <button
                kendoButton
                [svgIcon]="italicSVG"
                title="Italic"
                [value]="'Italic'"
                [ngClass]="{
                  'k-selected k-focus': isSelected('Italic')
                }"
                (click)="onFontStyleChange('Italic')"></button>
            </kendo-buttongroup>
          </div>

          <kendo-colorpicker
            id="color"
            class="t-w-[60px]"
            [format]="'hex'"
            formControlName="color"></kendo-colorpicker>

          <kendo-dropdownlist
            class="t-w-[120px]"
            id="placeHolderPosition"
            formControlName="orientation"
            [valuePrimitive]="true"
            [textField]="'text'"
            [valueField]="'value'"
            [data]="stampLocationList">
          </kendo-dropdownlist>
        </div>
        <hr class="t-my-2 t-border-[#0000001F]" />

        <div>
          <kendo-textarea
            [rows]="4"
            resizable="vertical"
            class="textarea-min-height t-border-0 t-border-transparent"
            id="watermark-text"
            resizable="none"
            [required]="isAddWatemarkChecked()"
            formControlName="text"></kendo-textarea>
        </div>
      </div>
    </div>

    <kendo-label
      class="t-text-error"
      *ngIf="
        isAddWatemarkChecked() &&
        waterMarkOption.get('text').touched &&
        waterMarkOption.get('text').invalid
      "
      text="Watermark text cannot be empty."></kendo-label>
  </div>
</form>
