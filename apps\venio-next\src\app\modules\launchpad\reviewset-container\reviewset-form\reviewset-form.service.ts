import {
  computed,
  effect,
  inject,
  Injectable,
  Injector,
  signal,
  untracked,
} from '@angular/core'
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms'
import {
  FileConversionCounts,
  NavigationMode,
  PreDefinedSortingFields,
  PropagationRules,
  ResponseModel,
  ReviewSetDialogData,
  ReviewSetDialogType,
  ReviewSetForm,
  ReviewSetModel,
  ReviewSetSourceFolders,
  ReviewSetSourceSavedSearch,
  ReviewSetSourceTags,
  ReviewSetSourceTypes,
  ReviewSetTemplateModel,
  SamplingCountModel,
  SelectedReviewSetActionEvent,
  TagsModel,
  UserGroupTree,
} from '@venio/shared/models/interfaces'
import { HttpClient } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import {
  combineLatest,
  from,
  mergeMap,
  Observable,
  of,
  OperatorFunction,
  startWith,
  switchMap,
  take,
  timer,
} from 'rxjs'
import { CaseConvertorService } from '@venio/util/utilities'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { catchError, map } from 'rxjs/operators'
import { TagsFacade } from '@venio/data-access/common'
import dayjs from 'dayjs'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { ReviewSetPayloadService } from './reviewset-payload.service'

type FilterTermType =
  | 'SOURCE_TAG'
  | 'SOURCE_FOLDER'
  | 'SOURCE_SAVE_SEARCH'
  | 'SORT_FIELD'
  | 'REVIEW_SET'

export interface LoadingStatus {
  isSourceTagLoading: boolean
  isSourceFolderLoading: boolean
  isSourceSaveSearchLoading: boolean
  isProjectUserLoading: boolean
  // All review sets indicator
  isReviewSetsLoading: boolean
  // single review set indicator
  isReviewSetLoading: boolean
  isCustomFieldsLoading: boolean
  isNavigationModeLoading: boolean
  isReviewSetNameChecking: boolean
  isFileConversionCountLoading: boolean
  isReviewSetTemplateLoading: boolean
  isSamplingCountLoading: boolean
}

@Injectable()
export class ReviewsetFormService {
  private readonly httpClient = inject(HttpClient)

  private readonly formBuilder = inject(FormBuilder)

  private readonly injector = inject(Injector)

  private readonly tagsFacade = inject(TagsFacade)

  public readonly reviewSetPayloadBuilder = inject(ReviewSetPayloadService)

  public reviewSetForm: FormGroup<ReviewSetForm> = null

  public readonly loadingStatus = signal<LoadingStatus>({
    isSourceTagLoading: false,
    isSourceFolderLoading: false,
    isSourceSaveSearchLoading: false,
    isProjectUserLoading: false,
    isReviewSetsLoading: false,
    isReviewSetLoading: false,
    isCustomFieldsLoading: false,
    isNavigationModeLoading: false,
    isReviewSetNameChecking: false,
    isFileConversionCountLoading: false,
    isReviewSetTemplateLoading: false,
    isSamplingCountLoading: false,
  })

  public readonly fileConversionCounts = signal<FileConversionCounts>({
    reviewFileCount: 0,
    tiffConversionCount: 0,
    tiffRemainingCount: 0,
    htmlConversionCount: 0,
    htmlRemainingCount: 0,
  })

  public readonly isNearNativeViewerEnabled = signal<boolean>(true)

  public readonly isImageViewerEnabled = signal<boolean>(true)

  public readonly enableNativeAutoPrefetch = signal<boolean>(true)

  public readonly isImageEnabled = signal<boolean>(true)

  public readonly isTagTreeLoading = toSignal(
    this.tagsFacade.selectIsTagTreeLoading$
  )

  public readonly formValues = toSignal(
    toObservable(signal(undefined)).pipe(
      switchMap(() =>
        combineLatest([
          this.reviewSetForm?.valueChanges,
          this.reviewSetForm?.statusChanges,
        ])
      ),
      map(() => this.reviewSetForm?.getRawValue())
    )
  )

  public readonly isEnableAutoCollect = toSignal(
    toObservable(signal(false)).pipe(
      this.#createControlMap<boolean>('enableAutoCollect'),
      map((checked) => checked)
    )
  )

  public readonly isAutoCollectCriteriaMatchSource = toSignal(
    toObservable(signal(false)).pipe(
      this.#createControlMap<boolean>('autoCollectionSelectionCriteria'),
      // We have 2 options which we need to have false as reverse value for 2nd option
      // 1. true - `Collected from matched source`
      // 2. false - `Collected from matched source and only if reviewed in batch`
      map((checked) => !checked)
    )
  )

  public readonly isUseCALProfileForReviewSet = toSignal(
    toObservable(signal(false)).pipe(
      this.#createControlMap<boolean>('useCALProfileForReviewSet'),
      map((checked) => checked)
    )
  )

  public readonly selectedProjectId = toSignal(
    toObservable(signal(0)).pipe(
      this.#createControlMap<number>('projectId'),
      map((projectId) => projectId)
    ),
    {
      initialValue: -1,
    }
  )

  public readonly selectedTemplateId = toSignal(
    toObservable(signal(0)).pipe(
      this.#createControlMap<number>('reviewSetTemplateId'),
      map((projectId) => projectId)
    ),
    {
      initialValue: -1,
    }
  )

  public readonly selectedAutoCollectReviewSetId = toSignal(
    toObservable(signal(undefined)).pipe(
      this.#createControlMap<number>('autoCollectReviewset'),
      map((reviewSet) => reviewSet)
    ),
    {
      initialValue: 0,
    }
  )

  public readonly selectedSavedSearchId = toSignal(
    toObservable(signal(undefined)).pipe(
      this.#createControlMap<number>('savedSearchId'),
      map((id) => id)
    ),
    {
      initialValue: 0,
    }
  )

  public readonly selectedSource = toSignal(
    toObservable(signal(undefined)).pipe(
      this.#createControlMap<ReviewSetSourceTypes>('reviewSource'),
      map((source) => source)
    ),
    {
      initialValue: ReviewSetSourceTypes.TAG,
    }
  )

  private readonly sourceValues = toSignal(
    toObservable(signal(undefined)).pipe(
      switchMap(() => {
        const ops = [
          this.#createControlMap<ReviewSetSourceTypes>('reviewSource'),
          this.#createControlMap<number[]>('tagId'),
          this.#createControlMap<number[]>('folderId'),
          this.#createControlMap<number>('savedSearchId'),
          this.#createControlMap<boolean>('useCALProfileForReviewSet'),
          this.#createControlMap<number>('confidenceLevel'),
          this.#createControlMap<number>('confidenceInterval'),
          this.#createControlMap<number>('percentageOfPopulation'),
        ]

        const streams = ops.map((op) => op(of(null)))

        return combineLatest(streams).pipe(
          map(
            ([reviewSource, tags, folders, savedSearch]) =>
              [reviewSource, tags, folders, savedSearch] as [
                ReviewSetSourceTypes,
                number[],
                number[],
                number
              ]
          )
        )
      })
    )
  )

  public readonly allTags = toSignal(
    this.tagsFacade.selectTagTreeSuccessResponse$.pipe(
      map((response) => this.#mapTreeToIdParentId(response?.data || []))
    )
  )

  public readonly navigationModelInfo = signal<NavigationMode>(undefined)

  public readonly samplingCount = signal<SamplingCountModel>(undefined)

  public readonly allSourceTags = signal<ReviewSetSourceTags[]>([])

  public readonly allSourceFolders = signal<ReviewSetSourceFolders[]>([])

  public readonly allSourceSavedSearch = signal<ReviewSetSourceSavedSearch[]>(
    []
  )

  public readonly allProjectUsers = signal<UserGroupTree[]>([])

  public readonly allCustomFields = signal<string[]>([])

  public readonly allReviewSets = signal<ReviewSetModel[]>([])

  public readonly allReviewSetTemplate = signal<ReviewSetTemplateModel[]>([])

  public readonly sortFieldTerm = signal<string>('')

  public readonly sourceTagTerm = signal<string>('')

  public readonly sourceFolderTerm = signal<string>('')

  public readonly sourceSaveSearchTerm = signal<string>('')

  public readonly reviewSetTerm = signal<string>('')

  private shallWaitForClone = false

  public readonly isAutoQueueForHtmlChecked = computed(
    () => this.formValues()?.autoQueueForHtmlConversion || false
  )

  public readonly isAutoQueueForTiffChecked = computed(
    () => this.formValues()?.autoQueueForTiff || false
  )

  /**
   * Filters the list of source folders based on the current search term.
   *
   * Returns all source folders when no search term is provided, otherwise
   * returns only those folders whose names or file counts include
   * the search term (case-insensitive matching).
   *
   * @returns {ReviewSetSourceFolders[]} - Filtered source folders list
   */
  public readonly filteredSourceTags = computed<ReviewSetSourceTags[]>(() => {
    const term = this.sourceTagTerm().trim().toLowerCase()
    const tags = this.allSourceTags()
    return term
      ? tags.filter(
          (tag) =>
            tag.name.toLowerCase().includes(term) ||
            tag.parentId.toLowerCase().includes(term) ||
            String(tag.totalTagCount).toLowerCase().includes(term)
        )
      : tags
  })

  /**
   * Filters the list of source folders based on the current search term.
   *
   * Returns all source folders when no search term is provided, otherwise
   * returns only those folders whose names or file counts include
   * the search term (case-insensitive matching).
   *
   * @returns {ReviewSetSourceFolders[]} - Filtered source folders list
   */
  public readonly filteredSourceFolders = computed<ReviewSetSourceFolders[]>(
    () => {
      const term = this.sourceFolderTerm().trim().toLowerCase()
      const folders = this.allSourceFolders()
      return term
        ? folders.filter(
            (tag) =>
              tag.folderName.toLowerCase().includes(term) ||
              String(tag.totalFileCount).toLowerCase().includes(term)
          )
        : folders
    }
  )

  /**
   * Filters the list of saved searches based on the current search term.
   *
   * Returns all saved searches when no search term is provided, otherwise
   * returns only those saved searches whose names or hit counts include
   * the search term (case-insensitive matching).
   *
   * @returns {ReviewSetSourceSavedSearch[]} - Filtered saved searches list
   */
  public readonly filteredSourceSavedSearch = computed<
    ReviewSetSourceSavedSearch[]
  >(() => {
    const term = this.sourceSaveSearchTerm().trim().toLowerCase()
    const savedSearches = this.allSourceSavedSearch()
    return term
      ? savedSearches.filter(
          (tag) =>
            tag.searchName.toLowerCase().includes(term) ||
            String(tag.totalHitCount).toLowerCase().includes(term)
        )
      : savedSearches
  })

  /**
   * Filters the list of sorting fields based on the current search term.
   *
   * Returns all custom fields when no search term is provided, otherwise
   * returns only those fields whose names include the search term
   * (case-insensitive matching).
   *
   * @returns {string[]} - Filtered sorting fields list
   */
  public readonly filteredSortFields = computed<string[]>(() => {
    const term = this.sortFieldTerm().trim().toLowerCase()
    const fields = this.allCustomFields()
    return term
      ? fields.filter((tag) => tag.toLowerCase().includes(term))
      : fields
  })

  /**
   * Filters the list of review sets based on the current search term.
   *
   * Returns all review sets when no search term is provided, otherwise
   * returns only those review sets whose names include the search term
   * (case-insensitive matching).
   *
   * @returns {ReviewSetModel[]} - Filtered review sets list
   */
  public readonly filteredReviewSets = computed<ReviewSetModel[]>(() => {
    const term = this.reviewSetTerm().trim().toLowerCase()
    const reviewSets = this.allReviewSets()
    return term
      ? reviewSets.filter((tag) => tag.name.toLowerCase().includes(term))
      : reviewSets
  })

  /**
   * Computes whether source selection (tags or folders) is in an indeterminate state.
   *
   * Determines if some but not all available sources are selected by comparing:
   * - For tags: the selected tag IDs against filtered available tags
   * - For folders: the selected folder IDs against filtered available folders
   *
   * Returns 'indeterminate' if some items are selected but not all,
   * returns true if all items are selected, and undefined for other cases.
   *
   * @returns {boolean | 'indeterminate' | undefined} - Selection state
   */
  public readonly isIntermediateSourceTag = computed(() => {
    const getIsIndeterminate = (
      sourceIds: number[],
      filteredSources: unknown[]
    ): boolean | 'indeterminate' => {
      return filteredSources.length > 0 &&
        sourceIds.length > 0 &&
        sourceIds.length < filteredSources.length
        ? 'indeterminate'
        : sourceIds.length === filteredSources.length
    }

    const formValues = this.formValues()
    if (!formValues) return

    if (formValues.reviewSource === ReviewSetSourceTypes.TAG) {
      const tagId = formValues.tagId || []
      const filteredSourceTags = this.filteredSourceTags()
      return getIsIndeterminate(tagId, filteredSourceTags)
    }

    if (formValues.reviewSource === ReviewSetSourceTypes.FOLDER) {
      const folderId = formValues.folderId || []
      const filteredSourceFolders = this.filteredSourceFolders()
      return getIsIndeterminate(folderId, filteredSourceFolders)
    }
  })

  /**
   * Initializes the review set form with default values and validation rules.
   *
   * Creates a FormGroup with controls for all review set properties including:
   * - Basic information (name, batch settings, purpose)
   * - Source configuration (tags, folders, saved searches)
   * - Display and user access settings
   * - Document inclusion/exclusion rules
   * - Tag and review propagation settings
   * - CAL (Continuous Active Learning) configuration
   * - Document processing options
   *
   * Applies validators including required fields, minimum values, array length requirements,
   * async validation for name uniqueness, and cross-field validation.
   * @returns {void}
   */
  public initReviewSetForm(): void {
    this.reviewSetForm = this.formBuilder.group<ReviewSetForm>(
      {
        projectId: this.formBuilder.control(0, {
          validators: [Validators.required, Validators.min(1)],
          nonNullable: true,
        }),
        reviewSetId: this.formBuilder.control(0, { nonNullable: true }),
        reviewSetTemplateId: this.formBuilder.control(0, { nonNullable: true }),
        name: this.formBuilder.control('', {
          validators: [Validators.required],
          nonNullable: true,
          asyncValidators: [this.#reviewSetNameAsyncValidator.bind(this)],
        }),
        batchPrefix: this.formBuilder.control('Default', {
          validators: [Validators.required],
          nonNullable: true,
        }),
        batchStartNumber: this.formBuilder.control(1, {
          validators: [Validators.required, Validators.min(1)],
          nonNullable: true,
        }),
        batchPaddingLength: this.formBuilder.control(8, {
          validators: [Validators.required, Validators.min(1)],
          nonNullable: true,
        }),
        batchSize: this.formBuilder.control(100, {
          validators: [Validators.required, Validators.min(1)],
          nonNullable: true,
        }),
        purpose: this.formBuilder.control('', { nonNullable: true }),
        reviewSource: this.formBuilder.control(ReviewSetSourceTypes.TAG, {
          validators: [Validators.required],
          nonNullable: true,
        }),
        tagId: this.formBuilder.control(null),
        tagSelectionOption: this.formBuilder.control('OR', {
          nonNullable: true,
        }),
        folderId: this.formBuilder.control(null),
        folderSelectionOption: this.formBuilder.control('OR', {
          nonNullable: true,
        }),
        savedSearchId: this.formBuilder.control(null),
        orderByField: this.formBuilder.control('INGESTION_ORDER', {
          nonNullable: true,
        }),
        sortOrder: this.formBuilder.control('asc', { nonNullable: true }),
        sortByCustodian: this.formBuilder.control(true, { nonNullable: true }),
        custodianSortOrder: this.formBuilder.control('asc', {
          nonNullable: true,
        }),
        displayTag: this.formBuilder.control([], {
          validators: [Validators.required],
          nonNullable: true,
        }),
        enableAutoCollect: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        autoCollectFrequency: this.formBuilder.control(24, {
          nonNullable: true,
        }),
        autoCollectExpiresOn: this.formBuilder.control('', {
          nonNullable: true,
        }),
        autoCollectMinThresholdValue: this.formBuilder.control(500, {
          nonNullable: true,
        }),
        autoCollectionSelectionCriteria: this.formBuilder.control(true, {
          nonNullable: true,
        }),
        autoCollectReviewset: this.formBuilder.control(null),
        selectedUserGroups: this.formBuilder.control([], {
          validators: [Validators.required, this.#arrayMinLengthValidator(1)],
          nonNullable: true,
        }),
        layout: this.formBuilder.control(1, {
          nonNullable: true,
        }),
        highlightGroup: this.formBuilder.control(1, {
          nonNullable: true,
        }),
        parentChildIncluded: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        msgThreadIncluded: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        excludePrevReviewSetDoc: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        excludeNonInclusiveEmails: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        propagateTagPCSet: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        tagPropagationRule: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        propagateTagEmailThread: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        propagateReviewPCSet: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        reviewDuplicatePropagationRule: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        propagateReviewEmailThread: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        useCALProfileForReviewSet: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        trainingRecallThreshold: this.formBuilder.control(70, {
          nonNullable: true,
        }),
        categoryTrainingThreshold: this.formBuilder.control(70, {
          nonNullable: true,
        }),
        predictionAccuracyThreshold: this.formBuilder.control(70, {
          nonNullable: true,
        }),
        batchRichnessThreshold: this.formBuilder.control(10, {
          nonNullable: true,
        }),
        reviewRelevanceThreshold: this.formBuilder.control(80, {
          nonNullable: true,
        }),
        allowReviewAfterCALThreshold: this.formBuilder.control(true, {
          nonNullable: true,
        }),
        controlSetSizeDerivedBy: this.formBuilder.control(0, {
          nonNullable: true,
        }),
        percentageOfPopulation: this.formBuilder.control(10, {
          nonNullable: true,
        }),
        numberOfDocuments: this.formBuilder.control(1000, {
          nonNullable: true,
        }),
        isDynamicControlSet: this.formBuilder.control(true, {
          nonNullable: true,
        }),
        calControlSetDocCount: this.formBuilder.control(0, {
          nonNullable: true,
        }),
        confidenceLevel: this.formBuilder.control(95, { nonNullable: true }),
        confidenceInterval: this.formBuilder.control(5, { nonNullable: true }),
        controlSetPercentFromTrainingBatch: this.formBuilder.control(5, {
          nonNullable: true,
        }),
        calControlSetMinDocCount: this.formBuilder.control(5, {
          nonNullable: true,
        }),
        calControlSetMaxDocCount: this.formBuilder.control(0, {
          nonNullable: true,
        }),
        calTrainingSetMinDocCount: this.formBuilder.control(5, {
          nonNullable: true,
        }),
        calTrainingSetMaxDocCount: this.formBuilder.control(3000, {
          nonNullable: true,
        }),
        // Default is true and when the form is edit mode, the derived value.
        autoQueueForHtmlConversion: this.formBuilder.control(true, {
          nonNullable: true,
        }),
        autoQueueForTiff: this.formBuilder.control(false, {
          nonNullable: true,
        }),
        markTaggedDocsAsReviewed: this.formBuilder.control(false, {
          nonNullable: true,
        }),
      },
      {
        validators: [this.#crossFieldValidator.bind(this)],
      }
    )
  }

  /**
   * Sets up automatic data reloading when the selected project changes.
   *
   * Creates an effect that watches for project ID changes and:
   * - Clears all previously loaded data
   * - Resets tag state
   * - Triggers loading of navigation data, custom fields, tags, review sets, and user groups
   *
   * Uses untracked to prevent the effect from reacting to changes in the data being loaded.
   * @returns {void}
   */
  public reloadWhenCaseChanges(): void {
    effect(
      () => {
        const projectId = this.selectedProjectId()
        if (!(projectId > 0)) return
        untracked(() => {
          //clear existing data
          this.allSourceTags.set([])
          this.allSourceFolders.set([])
          this.allSourceSavedSearch.set([])
          this.allProjectUsers.set([])
          this.allReviewSets.set([])
          this.allCustomFields.set([])
          this.navigationModelInfo.set(undefined)
          this.tagsFacade.resetTagsState('tagTreeSuccessResponse')

          this.#loadProjectSettings()
          this.#loadNavigationModeData()
          this.#loadCustomFields()
          this.tagsFacade.fetchTagTree(projectId, 0)
          this.#loadReviewSets()
          this.#loadProjectUserGroups()

          // We need to check if the review set template is already loaded,
          // if not we need to load it. Once loaded, we don't need to load it again
          // as it is independent of the project.

          if (!this.allReviewSetTemplate()?.[0]) {
            this.#loadReviewSetTemplate()
          }

          this.#resetDefaultFormState()
        })
      },
      { injector: this.injector }
    )
  }

  /**
   * Only reloads the sources when the source changes.
   * Track the selected source and reload the sources when the source changes.
   *
   * @returns {void}
   */
  public reloadWhenSourceChanges(): void {
    effect(
      () => {
        const { projectId } = this.reviewSetForm.getRawValue()
        const source = this.selectedSource()
        const navigationModel = this.navigationModelInfo()
        if (!(projectId > 0) || !navigationModel) return

        if (typeof source === 'undefined' || source === null) return

        // When source is changed, we need to reset the control values
        // Only if the form is not in edit mode;
        const isEdit = this.reviewSetForm.getRawValue().reviewSetId > 0

        // When there is process of copying the values from existing review set,
        // it becomes clone status, and we need to ensure to avoid resetting the cloned data.
        // So in both case i.e. edit mode and clone mode, we need to avoid resetting the values.
        // If once copied, user can then change the values.
        // But, for edit mode, it cannot be changed.
        if (!isEdit && !this.shallWaitForClone) {
          switch (source) {
            case ReviewSetSourceTypes.TAG:
              this.reviewSetForm.controls.tagId.reset()
              this.reviewSetForm.controls.tagSelectionOption.reset()
              break
            case ReviewSetSourceTypes.FOLDER:
              this.reviewSetForm.controls.folderId.reset()
              this.reviewSetForm.controls.folderSelectionOption.reset()
              break
            case ReviewSetSourceTypes.SAVED_SEARCH:
              this.reviewSetForm.controls.savedSearchId.reset()
              break
          }
        }

        untracked(() => this.#loadSources())
      },
      { injector: this.injector }
    )
  }

  public reloadSamplingCountWhenSourceChanges(): void {
    effect(
      () => {
        const value = this.sourceValues()
        const { projectId, useCALProfileForReviewSet } =
          this.reviewSetForm.getRawValue()

        if (!(projectId > 0) || !useCALProfileForReviewSet || !value) return

        const isTagSelected =
          value[0] === ReviewSetSourceTypes.TAG && value[1]?.length > 0
        const isFolderSelected =
          value[0] === ReviewSetSourceTypes.FOLDER && value[2]?.length > 0
        const isSavedSearchSelected =
          value[0] === ReviewSetSourceTypes.SAVED_SEARCH && value[3] > 0

        if (!isTagSelected && !isFolderSelected && !isSavedSearchSelected) {
          untracked(() => this.samplingCount.set(undefined))
          return
        }

        untracked(() => this.#loadSamplingCount())
      },
      { injector: this.injector }
    )
  }

  /**
   * Manages the enabled/disabled state of auto-collect related form controls.
   *
   * Sets up an effect that reacts to changes in auto-collect settings:
   * - When auto-collect is enabled, enables frequency, expiration, threshold, and criteria controls
   * - When auto-collect is disabled, disables and resets all related controls
   * - Separately handles the review set selection control based on criteria type
   *
   * Updates validity of all affected controls after state changes.
   * @returns {void}
   */
  public autoCollectControlEnableToggle(): void {
    effect(
      () => {
        const isEnableAutoCollect = this.isEnableAutoCollect()
        const isAutoCollectCriteriaMatchSource =
          this.isAutoCollectCriteriaMatchSource()

        // controls
        const autoCollectFrequency =
          this.reviewSetForm.controls.autoCollectFrequency
        const autoCollectExpiresOn =
          this.reviewSetForm.controls.autoCollectExpiresOn
        const autoCollectMinThresholdValue =
          this.reviewSetForm.controls.autoCollectMinThresholdValue
        const autoCollectionSelectionCriteria =
          this.reviewSetForm.controls.autoCollectionSelectionCriteria
        const autoCollectReviewset =
          this.reviewSetForm.controls.autoCollectReviewset

        if (isAutoCollectCriteriaMatchSource) {
          autoCollectReviewset.enable()
        } else {
          // Set default value before disabling
          autoCollectReviewset.reset()
          autoCollectReviewset.disable()
        }

        if (isEnableAutoCollect) {
          autoCollectFrequency.enable()
          autoCollectExpiresOn.enable()
          autoCollectMinThresholdValue.enable()
          autoCollectionSelectionCriteria.enable()
        } else {
          autoCollectFrequency.disable()
          autoCollectExpiresOn.disable()
          autoCollectMinThresholdValue.disable()

          // Set default value before disabling
          autoCollectionSelectionCriteria.reset()
          autoCollectionSelectionCriteria.disable()

          // Set default value before disabling
          autoCollectReviewset.reset()
          autoCollectReviewset.disable()
        }

        autoCollectFrequency.updateValueAndValidity()
        autoCollectExpiresOn.updateValueAndValidity()
        autoCollectMinThresholdValue.updateValueAndValidity()
        autoCollectionSelectionCriteria.updateValueAndValidity()
        autoCollectReviewset.updateValueAndValidity()
      },
      { injector: this.injector }
    )
  }

  /**
   * Sets a filter term for a specific filter type.
   *
   * Updates the appropriate signal based on the filter type provided.
   *
   * @param {string} term - The search/filter term to set
   * @param {FilterTermType} type - The type of filter to configure
   * @returns {void}
   */
  public configureFilterTerm(term: string, type: FilterTermType): void {
    switch (type) {
      case 'SOURCE_TAG':
        this.sourceTagTerm.set(term)
        break
      case 'SOURCE_FOLDER':
        this.sourceFolderTerm.set(term)
        break
      case 'SOURCE_SAVE_SEARCH':
        this.sourceSaveSearchTerm.set(term)
        break
      case 'SORT_FIELD':
        this.sortFieldTerm.set(term)
        break
      case 'REVIEW_SET':
        this.reviewSetTerm.set(term)
        break
    }
  }

  /**
   * Submits a review set to the API.
   *
   * Creates a new review set by sending the provided payload to the API endpoint.
   *
   * @param {number} projectId - The ID of the project to create the review set in
   * @param {ReviewSetModel} payload - The review set data to submit
   * @returns {Observable<ResponseModel>} - API response observable
   */
  public submitReviewSet(): Observable<ResponseModel> {
    const reviewSetInfo = this.#prepareReviewSetData()
    const projectId = this.selectedProjectId()
    const { reviewSetId } = reviewSetInfo
    return reviewSetId > 0
      ? this.httpClient.put<ResponseModel>(
          `${environment.apiUrl}project/${projectId}/reviewSet/${reviewSetId}`,
          reviewSetInfo
        )
      : this.httpClient.post<ResponseModel>(
          `${environment.apiUrl}project/${projectId}/ReviewSet`,
          reviewSetInfo
        )
  }

  /**
   * Loads review set data from the API and prepares it for editing or cloning.
   *
   * Process:
   * 1. Sets the project ID in the form to trigger dependent data loading
   * 2. Updates loading status
   * 3. Fetches review set data from API and converts to camelCase
   * 4. For cloning, modifies the name and resets ID
   * 5. Loads the data into the form
   *
   * Handles errors by clearing loading state and returning an empty object.
   *
   * @param {SelectedReviewSetActionEvent} eventPayload - Contains review set info and action type
   * @returns {void}
   */
  public loadReviewSet(eventPayload: SelectedReviewSetActionEvent): void {
    const {
      selectedReviewSet: { projectId, reviewSetId },
      actionType,
    } = eventPayload

    const isClone = actionType === CommonActionTypes.REVIEW_SET_CLONE
    this.shallWaitForClone = isClone

    // Early patching of the project id to ensure the form loads other
    // data based on the project id.
    this.reviewSetForm.controls.projectId.patchValue(projectId)
    this.reviewSetForm.controls.projectId.updateValueAndValidity()

    this.#updateLoadingStatus('isReviewSetLoading', true)

    this.httpClient
      .get<ResponseModel>(
        `${environment.apiUrl}project/${projectId}/reviewSet/${reviewSetId}`
      )
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          return from(
            camelCaseConvertorService.convertToCase<ReviewSetModel>(
              response.data,
              'camelCase'
            )
          )
        }),
        catchError(() => {
          this.#updateLoadingStatus('isReviewSetLoading', false)
          return of({} as ReviewSetModel)
        }),
        take(1)
      )
      .subscribe((reviewSet) => {
        this.#updateLoadingStatus('isReviewSetLoading', false)

        // When it is clone, we need to reset the review set id to 0
        if (isClone) {
          reviewSet.name = `${reviewSet.name} - Clone`
          reviewSet.reviewSetId = 0
        }
        this.#handleSourceDataLoaded(reviewSet)
      })
  }

  /**
   * Fetches document conversion statistics for the current review set configuration.
   *
   * Sends the review set form values to the API to calculate how many documents
   * would be queued for HTML and TIFF conversion.
   *
   * @returns {Observable<boolean>} - Success indicator
   */
  public fetchFileConversionCounts(): Observable<boolean> {
    const { projectId } = this.reviewSetForm.getRawValue()
    const reviewSetInfo = this.#prepareReviewSetData()

    this.#updateLoadingStatus('isFileConversionCountLoading', true)

    return this.httpClient
      .post<ResponseModel>(
        `${environment.apiUrl}project/${projectId}/ReviewSetFileConversionCount`,
        reviewSetInfo
      )
      .pipe(
        map((response) => {
          this.#updateLoadingStatus('isFileConversionCountLoading', false)
          if (response.data) {
            this.fileConversionCounts.set(response.data)
            return true
          }
          return false
        }),
        catchError(() => {
          this.#updateLoadingStatus('isFileConversionCountLoading', false)
          return of(false)
        }),
        take(1)
      )
  }

  /**
   * Loads layout details and updates capability signals.
   *
   * Fetches the selected layout's panel configuration and determines if it
   * includes NearNativeViewer and ImageViewer panels.
   *
   * @returns {void}
   */
  public loadLayoutDetails(): void {
    this.httpClient
      .get<ResponseModel>(`${environment.apiUrl}client/1/reviewlayout`)
      .pipe(
        catchError(() => of(undefined)),
        take(1)
      )
      .subscribe((response) => {
        if (response?.data) {
          // Default one has been selected.
          const layout = response.data?.[0]

          // Check for NearNativeViewer panel
          const hasNearNativeViewer = layout.reviewLayoutPanels?.some(
            (panel) => panel.name === 'NearNativeViewer'
          )

          // Check for ImageViewer panel
          const hasImageViewer = layout.reviewLayoutPanels?.some(
            (panel) => panel.name === 'ImageViewer'
          )

          this.isNearNativeViewerEnabled.set(hasNearNativeViewer)
          this.isImageViewerEnabled.set(hasImageViewer)

          // Automatically update form controls if not in edit mode
          const isEdit = this.reviewSetForm.controls.reviewSetId.value > 0
          if (
            !isEdit &&
            this.enableNativeAutoPrefetch() &&
            hasNearNativeViewer
          ) {
            this.reviewSetForm.controls.autoQueueForHtmlConversion.setValue(
              true
            )
          }
        }
      })
  }

  /**
   * Disables the review set template control if no templates are available.
   * When the project is selected, this effect checks if any review set templates are available.
   * If templates are found, the control is enabled. If no templates are available, the control is disabled.
   * This effect is destroyed on templates loads.
   *
   * @returns {void}
   */
  public reviewSetTemplateControlValidation(): void {
    const templateControlEffectRef = effect(
      () => {
        const templateCtrl = this.reviewSetForm.controls.reviewSetTemplateId
        const hasAnyTemplate = this.allReviewSetTemplate()?.[0]

        templateCtrl.disable()

        const projectId = this.selectedProjectId()
        if (!(projectId > 0)) return

        templateCtrl.enable()

        // As we have templates loaded, we no longer need this effect
        if (!hasAnyTemplate) {
          templateControlEffectRef.destroy()
        }
      },
      { injector: this.injector }
    )
  }

  public validateDocumentSortControlWhenCalChange(): void {
    effect(
      () => {
        const isCalEnabled = this.isUseCALProfileForReviewSet()
        const orderByField = this.reviewSetForm.controls.orderByField
        const sortOrder = this.reviewSetForm.controls.sortOrder
        const sortByCustodian = this.reviewSetForm.controls.sortByCustodian
        const custodianSortOrder =
          this.reviewSetForm.controls.custodianSortOrder

        if (isCalEnabled) {
          orderByField.setValue('CAL')
          orderByField.disable()
          sortOrder.disable()
          sortByCustodian.disable()
          custodianSortOrder.disable()
        } else {
          orderByField.reset()
          orderByField.enable()
          sortOrder.enable()
          sortByCustodian.enable()
          custodianSortOrder.enable()
        }
      },
      { injector: this.injector }
    )
  }

  /**
   * Loads project settings including conversion capabilities.
   *
   * Fetches whether the project supports native auto-prefetch and
   * image (TIFF) processing.
   *
   * @returns {void}
   */
  #loadProjectSettings(): void {
    const projectId = this.selectedProjectId()
    this.httpClient
      .get<ResponseModel>(
        `${environment.apiUrl}cases/project/${projectId}/case-info`
      )
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          const convertedData =
            camelCaseConvertorService.convertToCase<ResponseModel>(
              response,
              'camelCase'
            )
          return from(convertedData)
        }),
        catchError(() => of(undefined)),
        take(1)
      )
      .subscribe((response) => {
        if (response?.data) {
          this.enableNativeAutoPrefetch.set(
            response.data.enableNativeAutoPrefetch || false
          )
          this.isImageEnabled.set(response.data.allowTiff || false)
        }
      })
  }

  /**
   * Determines which dialog should be shown before form submission.
   *
   * Analyzes current form state and system capabilities to decide if:
   * - HTML conversion should be recommended
   * - Conversion counts need to be confirmed
   * - Settings are not supported and need warnings
   *
   * @returns {Observable<ReviewSetDialogData>} - Dialog information if needed
   */
  #determineRequiredDialog(): Observable<ReviewSetDialogData> {
    const formValues = this.reviewSetForm.getRawValue()
    const isEdit = formValues.reviewSetId > 0

    // Check if auto-queue is enabled for either format
    const isAutoQueueHtml = formValues.autoQueueForHtmlConversion
    const isAutoQueueTiff = formValues.autoQueueForTiff

    // If HTML or TIFF conversion is enabled, load counts
    if (
      (isAutoQueueHtml || isAutoQueueTiff) &&
      this.enableNativeAutoPrefetch()
    ) {
      return this.fetchFileConversionCounts().pipe(
        map(() => this.#calculateDialogType(isEdit))
      )
    }

    // Otherwise determine dialog without loading counts
    return of(this.#calculateDialogType(isEdit))
  }

  /**
   * Helper method to calculate which dialog type to show.
   *
   * @param {boolean} isEdit - Whether we're editing an existing review set
   * @returns {ReviewSetDialogData} - Dialog information
   */
  #calculateDialogType(isEdit: boolean): ReviewSetDialogData {
    const formValues = this.reviewSetForm.getRawValue()
    const counts = this.fileConversionCounts()

    // Case 1: Native prefetch not enabled but trying to use auto-queue
    if (
      !this.enableNativeAutoPrefetch() &&
      (formValues.autoQueueForHtmlConversion || formValues.autoQueueForTiff)
    ) {
      return {
        type: ReviewSetDialogType.NOT_SUPPORTED,
        title: 'Feature Not Supported',
        message:
          'Auto-queue for conversion is not supported because native auto-prefetch is not enabled for this project.',
      }
    }

    // Case 2: TIFF auto-queue selected but images not enabled
    if (formValues.autoQueueForTiff && !this.isImageEnabled()) {
      return {
        type: ReviewSetDialogType.NOT_SUPPORTED,
        title: 'Feature Not Supported',
        message:
          'Auto-queue for TIFF is not supported because image processing is not enabled for this project.',
      }
    }

    // Case 3: HTML conversion should be recommended but isn't enabled
    if (
      !isEdit &&
      this.enableNativeAutoPrefetch() &&
      this.isNearNativeViewerEnabled() &&
      !formValues.autoQueueForHtmlConversion
    ) {
      return {
        type: ReviewSetDialogType.HTML_RECOMMENDATION,
        title: 'Enable HTML Conversion',
        message:
          'The native to html conversion option is not selected. As a result the reviewers may experience slow loading of the native viewer while switching between the documents.\n' +
          'Do you still want to continue?',
      }
    }

    // Case 4: Auto-queue is enabled and we have documents to convert
    if (formValues.autoQueueForHtmlConversion || formValues.autoQueueForTiff) {
      const isNativeToHtmlChecked = formValues.autoQueueForHtmlConversion
      const isNativeToImageChecked = formValues.autoQueueForTiff
      const isHtmlConversionDisabled =
        this.reviewSetForm.controls.autoQueueForHtmlConversion.disabled
      const isImageConversionDisabled =
        this.reviewSetForm.controls.autoQueueForTiff.disabled

      const type =
        (!isHtmlConversionDisabled && isNativeToHtmlChecked) ||
        (!isImageConversionDisabled && isNativeToImageChecked)
          ? ReviewSetDialogType.IMAGE_HTML_CONFIRMATION
          : ReviewSetDialogType.NONE
      const title =
        'Document Conversion Type -  ' +
        (isNativeToHtmlChecked && isNativeToImageChecked
          ? 'HTML & Images'
          : !isNativeToHtmlChecked && isNativeToImageChecked
          ? 'Images'
          : '')
      return {
        type,
        title,
        message: 'The following documents will be queued for conversion:',
        counts,
        isNativeToHtmlChecked:
          !isHtmlConversionDisabled && isNativeToHtmlChecked,
        isNativeToImageChecked:
          !isImageConversionDisabled && isNativeToImageChecked,
      }
    }

    // Default: No dialog needed
    return { type: ReviewSetDialogType.NONE }
  }

  /**
   * Prepares review set data for submission, applying capability-based overrides.
   *
   * @returns {ReviewSetModel} - Prepared review set data
   */
  #prepareReviewSetData(): ReviewSetModel {
    const formValues = this.reviewSetForm.getRawValue()

    const payload = this.reviewSetPayloadBuilder.transform<ReviewSetModel>(
      formValues as any,
      {
        enableNativeAutoPrefetch: this.enableNativeAutoPrefetch(),
        isImageEnabled: this.isImageEnabled(),
        allViewableTags: this.allTags(),
      }
    )

    // Enforce capability limitations
    if (!this.enableNativeAutoPrefetch()) {
      payload.autoQueueForHtmlConversion = false
      payload.autoQueueForTiff = false
    }

    if (!this.isImageEnabled()) {
      payload.autoQueueForTiff = false
    }

    // Apply other transformations required for the API

    return payload
  }

  /**
   * Processes form for submission, including dialog determination.
   *
   * @returns {Observable<{shouldProceed: boolean, dialogData?: ReviewSetDialogData, reviewSetData?: ReviewSetModel}>}
   */
  public processFormForSubmission(): Observable<{
    shouldProceed: boolean
    dialogData?: ReviewSetDialogData
    reviewSetData?: ReviewSetModel
  }> {
    return this.#determineRequiredDialog().pipe(
      map((dialogData) => {
        // If no dialog needed, prepare data and proceed
        if (dialogData.type === ReviewSetDialogType.NONE) {
          const reviewSetData = this.#prepareReviewSetData()
          return { shouldProceed: true, reviewSetData }
        }

        // Otherwise, client needs to show dialog
        return { shouldProceed: false, dialogData }
      })
    )
  }

  /**
   * Handles user response to a dialog.
   *
   * Updates form values based on dialog confirmation and determines
   * if another dialog is needed or if submission should proceed.
   *
   * @param {boolean} confirmed - Whether user confirmed the dialog
   * @param {ReviewSetDialogType} dialogType - Type of dialog that was shown
   * @returns {Observable<{shouldProceed: boolean, dialogData?: ReviewSetDialogData, reviewSetData?: ReviewSetModel}>}
   */
  public handleDialogConfirmation(
    confirmed: boolean,
    dialogType: ReviewSetDialogType
  ): Observable<{
    shouldProceed: boolean
    dialogData?: ReviewSetDialogData
    reviewSetData?: ReviewSetModel
  }> {
    // If user cancelled, don't proceed
    if (!confirmed) {
      return of({ shouldProceed: false })
    }

    // Handle different dialog confirmations
    switch (dialogType) {
      case ReviewSetDialogType.HTML_RECOMMENDATION:
        // User confirmed they want HTML conversion
        this.reviewSetForm.controls.autoQueueForHtmlConversion.setValue(true)

        // Check if we need another dialog
        return this.#determineRequiredDialog().pipe(
          map((dialogData) => {
            if (dialogData.type !== ReviewSetDialogType.NONE) {
              return { shouldProceed: false, dialogData }
            }
            const reviewSetData = this.#prepareReviewSetData()
            return { shouldProceed: true, reviewSetData }
          })
        )

      case ReviewSetDialogType.NOT_SUPPORTED:
        // User acknowledged limitations, apply necessary changes
        if (!this.enableNativeAutoPrefetch()) {
          this.reviewSetForm.controls.autoQueueForHtmlConversion.setValue(false)
          this.reviewSetForm.controls.autoQueueForTiff.setValue(false)
        } else if (!this.isImageEnabled()) {
          this.reviewSetForm.controls.autoQueueForTiff.setValue(false)
        }
        break
    }

    // Prepare final data and proceed
    const reviewSetData = this.#prepareReviewSetData()
    return of({ shouldProceed: true, reviewSetData })
  }

  /**
   * Processes and loads review set data into the form.
   *
   * Transforms API data to match form structure by:
   * - Converting string IDs to number arrays
   * - Formatting dates properly for Kendo UI
   * - Normalizing boolean/number values
   * - Handling casing differences
   * - Setting default values for required fields
   *
   * After patching the main form values, maps complex structures like
   * user groups and display tags, then disables appropriate controls
   * if editing an existing review set.
   *
   * @param {any} input - Review set data from API
   * @returns {void}
   */
  #handleSourceDataLoaded(input: any): void {
    const transformed = {
      ...input,
      reviewSetId: input.reviewSetId,
      name: input.name,
      batchPrefix: input.batchPrefix,
      batchStartNumber: input.batchStartNumber,
      batchPaddingLength: input.batchPaddingLength,
      batchSize: input.batchSize,
      purpose: input.purpose,
      // Initially, the kendo expects to have some value so initializing with empty array.
      // Once the real data source are loaded, they are mapped to the displayTag control.
      displayTag: [],
      selectedUserGroups: [],

      // The form control expects an array of numbers, but the API returns comma separated string
      tagId: this.#parseIdList(input.tagId),
      folderId: this.#parseIdList(input.folderId),

      savedSearchId: input.savedSearchId || null,
      orderByField: input.orderByField,
      reviewSource: input.reviewSource,
      sortOrder: input.sortOrder,
      sortByCustodian: input.sortByCustodian,
      custodianSortOrder: input.custodianSortOrder,
      enableAutoCollect: input.enableAutoCollect,
      autoCollectFrequency: input.autoCollectFrequency,
      // Kendo date picker will throw an error if the date is not a Date object
      autoCollectExpiresOn: input.autoCollectExpiresOn
        ? dayjs(input.autoCollectExpiresOn).toDate()
        : '',
      autoCollectMinThresholdValue: input.autoCollectMinThresholdValue,
      autoCollectReviewset: input.autoCollectReviewset,
      // We have 2 options which we need to have false as reverse value for 2nd option
      autoCollectionSelectionCriteria: !(input.autoCollectReviewset > 0),
      layout: input.layout || 1,
      highlightGroup: input.highlightGroup || 1,
      parentChildIncluded: input.parentChildIncluded,
      msgThreadIncluded: input.msgThreadIncluded,
      excludePrevReviewSetDoc: input.excludePrevReviewSetDoc,
      excludeNonInclusiveEmails: input.excludeNonInclusiveEmails,
      // Property casing is different in the form and the API
      propagateTagPCSet: input.propagateTagPcSet,
      // In the UI it is boolean, but in the API it is a number
      tagPropagationRule:
        input.tagPropagationRule === PropagationRules.PROPAGATE_TO_DUPLICATES,
      propagateTagEmailThread: input.propagateTagEmailThread,
      // Property casing is different in the form and the API
      propagateReviewPCSet: input.propagateReviewPcSet,
      // In the UI it is boolean, but in the API it is a number
      reviewDuplicatePropagationRule:
        input.reviewDuplicatePropagationRule ===
        PropagationRules.PROPAGATE_TO_DUPLICATES,
      propagateReviewEmailThread: input.propagateReviewEmailThread,
      useCALProfileForReviewSet: input.isCalReviewSet,
      autoQueueForHtmlConversion: input.autoQueueForHtmlConversion,
      autoQueueForTiff: input.autoQueueForTiff,
      markTaggedDocsAsReviewed: input.markTaggedDocsAsReviewed,
    }

    // When values are patched, trigger event on all ancestors and descendants
    this.reviewSetForm.patchValue(transformed, { onlySelf: false })

    // We have other types of data structure when it binds to the form
    // Any value that we need to custom structure, we need to ensure patch with initial values first
    // and then map the values to the form control to reflect the updated changes and trigger the event
    this.#mapUserGroups(input.selectedUserGroups)
    this.#mapDisplayTags(input.viewableTags)

    // once patched, we disable controls based on review set id
    this.#nonEditableControls(input.reviewSetId)
  }

  /**
   * Async validator that checks if a review set name already exists in the project.
   *
   * Validates the review set name by checking against the server after a 500ms debounce.
   * Skips validation if the control is pristine, empty, has no parent, or project ID is missing.
   *
   * @param {AbstractControl} control - The form control to validate
   * @returns {Observable<ValidationErrors | null>} - Returns null if valid or an error object if the name exists
   */
  #reviewSetNameAsyncValidator(
    control: AbstractControl
  ): Observable<ValidationErrors | null> {
    const reviewSetName = control.value?.trim().toLowerCase()
    if (control.pristine || !reviewSetName) {
      return of(null)
    }
    if (!control.parent) {
      return of(null)
    }
    const projectId = control.parent.get('projectId').value
    const reviewSetId = control.parent.get('reviewSetId').value

    if (!reviewSetName || !projectId) {
      return of(null)
    }

    this.#updateLoadingStatus('isReviewSetNameChecking', true)

    return timer(500).pipe(
      switchMap(() =>
        this.httpClient.get<ResponseModel>(
          `${
            environment.apiUrl
          }project/${projectId}/reviewSetName/${encodeURIComponent(
            reviewSetName
          )}/lookup`
        )
      ),
      map((r) => {
        this.#updateLoadingStatus('isReviewSetNameChecking', false)

        if (reviewSetId > 0) {
          const areSameName = this.allReviewSets().some(
            (rs) =>
              rs.name.trim().toLowerCase() === reviewSetName &&
              rs.reviewSetId === reviewSetId
          )
          return !areSameName && r.data ? { nameExists: r.data } : null
        }
        return r.data ? { nameExists: r.data } : null
      }),
      catchError(() => {
        this.#updateLoadingStatus('isReviewSetNameChecking', false)
        return of(null)
      }),
      take(1)
    )
  }

  /**
   * Creates an operator that maps form control changes to observable values.
   *
   * Returns an operator function that combines the latest value and status
   * of the specified form control, starting with its current value, and
   * maps the result to just the value cast to the specified type.
   *
   * @param {ReviewSetForm} controlName - Name of the form control to observe
   * @returns {OperatorFunction<unknown, T>} - An operator that transforms inputs into control values
   * @template T - Type to cast the control value to
   * @returns {OperatorFunction<unknown, T>} - An operator that transforms inputs into control values
   */
  #createControlMap<T>(
    controlName: keyof ReviewSetForm
  ): OperatorFunction<unknown, T> {
    return mergeMap(() =>
      combineLatest([
        this.reviewSetForm
          .get(controlName)
          .valueChanges.pipe(
            startWith(this.reviewSetForm.get(controlName).value)
          ),
        this.reviewSetForm.get(controlName).statusChanges,
      ]).pipe(map(([value, _]) => value as T))
    )
  }

  /**
   * Loads review set templates data.
   *
   * Fetches review set template data from the API,
   * converts it to camelCase, and updates the allReviewSetTemplate signal.
   *
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadReviewSetTemplate(): void {
    this.#updateLoadingStatus('isReviewSetTemplateLoading', true)
    this.httpClient
      .get<ResponseModel>(`${environment.apiUrl}ReviewSetTemplate`)
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          return from(
            camelCaseConvertorService.convertToCase<ReviewSetTemplateModel[]>(
              response.data,
              'camelCase'
            )
          )
        }),
        catchError(() => {
          this.#updateLoadingStatus('isReviewSetTemplateLoading', false)
          return of([] as ReviewSetTemplateModel[])
        }),
        take(1)
      )
      .subscribe((reviewSet) => {
        this.#updateLoadingStatus('isReviewSetTemplateLoading', false)
        this.allReviewSetTemplate.set(reviewSet)
      })
  }

  /**
   * Loads navigation mode data and triggers source loading.
   *
   * Fetches navigation mode data from the API for the current project,
   * converts it to camelCase, and updates the navigationModelInfo signal.
   * Copies mediaList to navigationList in the stored data.
   *
   * After loading navigation data, automatically triggers loading of
   * appropriate sources based on the current review source type.
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadNavigationModeData(): void {
    this.#updateLoadingStatus('isNavigationModeLoading', true)
    const { projectId } = this.reviewSetForm.getRawValue()
    this.httpClient
      .get<ResponseModel>(
        `${environment.apiUrl}cases/project/${projectId}/navigation-mode`
      )
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          return from(
            camelCaseConvertorService.convertToCase<NavigationMode>(
              response.data,
              'camelCase'
            )
          )
        }),
        take(1),
        catchError(() => {
          this.#updateLoadingStatus('isNavigationModeLoading', false)
          return of({} as NavigationMode)
        })
      )
      .subscribe((source) => {
        this.#updateLoadingStatus('isNavigationModeLoading', false)
        this.navigationModelInfo.set({
          ...source,
          // which seems to be the same values required for the navigation model
          navigationList: source.mediaList,
        })
      })
  }

  #loadSamplingCount(): void {
    this.#updateLoadingStatus('isSamplingCountLoading', true)
    const {
      projectId,
      confidenceLevel,
      confidenceInterval,
      reviewSource,
      percentageOfPopulation,
      numberOfDocuments,
      tagId,
      savedSearchId,
      folderId,
    } = this.reviewSetForm.getRawValue()
    const samplePopulation =
      reviewSource === ReviewSetSourceTypes.TAG
        ? '0'
        : reviewSource === ReviewSetSourceTypes.FOLDER
        ? '2'
        : '1'
    const tagTotalCount = this.allTags().filter((x) => x.id).length
    const tagSelectedCount = tagId?.length || 0
    let sampleTagCondition = 'Non_Selected_Tags'
    if (tagSelectedCount === 0) {
      sampleTagCondition = 'Non_Selected_Tags'
    } else if (tagSelectedCount < tagTotalCount) {
      sampleTagCondition = 'Any_Selected_Tags'
    } else if (tagSelectedCount === tagTotalCount) {
      sampleTagCondition = 'All_Selected_Tags'
    }
    const payload = {
      sampleAction: reviewSource,
      confidenceLevel,
      confidenceInterval,
      samplePercent: percentageOfPopulation,
      sampleNumber: numberOfDocuments,
      applyTagId: tagId || [],
      searchId: savedSearchId,
      excludeNoText: false,
      excludeTags: false,
      tagIds: tagId || [],
      excludeTagIds: [],
      samplePopulation,
      sampleTagCondition,
      folderIds: folderId,
      module: 'ADVANCE_ADMIN',
    }
    this.httpClient
      .post<ResponseModel>(
        `${environment.apiUrl}Project/${projectId}/Sampling/GetSamplingCount`,
        payload
      )
      .pipe(
        catchError(() => {
          this.#updateLoadingStatus('isSamplingCountLoading', false)
          return of({} as ResponseModel)
        }),
        take(1)
      )
      .subscribe((response) => {
        const data = response.data as SamplingCountModel
        this.#updateLoadingStatus('isSamplingCountLoading', false)
        this.samplingCount.set(data)

        const { useCALProfileForReviewSet } = this.formValues()
        if (useCALProfileForReviewSet) {
          this.reviewSetForm.controls.calControlSetMaxDocCount.patchValue(
            // when there is negative value, the validation will fail
            // so we need to get the absolute value and set it.
            // e.g. -1 will be 1
            Math.abs(data?.sampleSize) || 0,
            {
              emitEvent: false,
            }
          )
        }
      })
  }

  /**
   * Loads custom fields and combines them with predefined sorting fields.
   *
   * Fetches custom field data from the API for the current project,
   * extracts field names, combines them with predefined sorting fields,
   * and updates the allCustomFields signal with the merged list.
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadCustomFields(): void {
    this.#updateLoadingStatus('isCustomFieldsLoading', true)
    const { projectId } = this.reviewSetForm.getRawValue()
    this.httpClient
      .get<ResponseModel>(
        `${environment.apiUrl}project/${projectId}/customfield-predefinedfield`
      )
      .pipe(
        catchError(() => {
          this.#updateLoadingStatus('isCustomFieldsLoading', false)
          return of({ data: [] })
        }),
        take(1)
      )
      .subscribe((r) => {
        this.#updateLoadingStatus('isCustomFieldsLoading', false)
        this.allCustomFields.set(
          PreDefinedSortingFields.concat(r.data.map((f) => f.fieldName))
        )
      })
  }

  /**
   * Loads all review sets for the current project.
   *
   * Fetches review set data from the API, converts the response to camelCase,
   * and updates the allReviewSets signal with the results.
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadReviewSets(): void {
    this.#updateLoadingStatus('isReviewSetsLoading', true)
    const { projectId } = this.reviewSetForm.getRawValue()

    // Since the review sets are required to validate the existance name duriing edit, we need to disable the anme control until it loads
    this.reviewSetForm.controls.name.disable()

    this.httpClient
      .get<ResponseModel>(`${environment.apiUrl}project/${projectId}/reviewset`)
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          const convertedData = camelCaseConvertorService.convertToCase<
            ReviewSetModel[]
          >(response.data, 'camelCase')
          return from(convertedData)
        }),
        catchError(() => {
          this.#updateLoadingStatus('isReviewSetsLoading', false)

          // Enable the name control after the review sets are loaded
          this.reviewSetForm.controls.name.enable()

          return of([] as ReviewSetModel[])
        }),
        take(1)
      )
      .subscribe((reviewSets) => {
        this.#updateLoadingStatus('isReviewSetsLoading', false)
        this.allReviewSets.set(reviewSets)

        // Enable the name control after the review sets are loaded
        this.reviewSetForm.controls.name.enable()
      })
  }

  /**
   * Loads tags for use as review set source.
   *
   * Makes a POST request to fetch source tags for the current project,
   * converts the response to camelCase, and updates the allSourceTags
   * signal with filtered results (excluding tags without parent IDs).
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadSourceTags(): void {
    this.#updateLoadingStatus('isSourceTagLoading', true)
    const { projectId } = this.reviewSetForm.getRawValue()
    this.httpClient
      .post(
        `${environment.apiUrl}production/project/${projectId}/source-tags`,
        this.navigationModelInfo()
      )
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          const convertedData = camelCaseConvertorService.convertToCase<
            ReviewSetSourceTags[]
          >(response, 'camelCase')
          return from(convertedData)
        }),
        catchError(() => {
          this.#updateLoadingStatus('isSourceTagLoading', false)
          return of([] as ReviewSetSourceTags[])
        }),
        take(1)
      )
      .subscribe((source) => {
        this.#updateLoadingStatus('isSourceTagLoading', false)
        this.allSourceTags.set(source.filter((t) => t.parentId?.trim()))

        // once the patching is done, we no longer need to retain the local state
        this.shallWaitForClone = false
      })
  }

  /**
   * Loads folders for use as review set source.
   *
   * Makes a POST request to fetch source folders for the current project,
   * converts the response to camelCase, and updates the allSourceFolders
   * signal with the results.
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadSourceFolders(): void {
    this.#updateLoadingStatus('isSourceFolderLoading', true)
    const { projectId } = this.reviewSetForm.getRawValue()
    this.httpClient
      .post(
        `${environment.apiUrl}production/project/${projectId}/source-folders`,
        this.navigationModelInfo()
      )
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          return from(
            camelCaseConvertorService.convertToCase<ReviewSetSourceFolders[]>(
              response,
              'camelCase'
            )
          )
        }),
        catchError(() => {
          this.#updateLoadingStatus('isSourceFolderLoading', false)
          return of([] as ReviewSetSourceFolders[])
        }),
        take(1)
      )
      .subscribe((source) => {
        this.#updateLoadingStatus('isSourceFolderLoading', false)
        this.allSourceFolders.set(source)

        // once the patching is done, we no longer need to retain the local state
        this.shallWaitForClone = false
      })
  }

  /**
   * Loads saved searches for use as review set source.
   *
   * Makes a POST request to fetch saved searches for the current project,
   * converts the response to camelCase, and updates the allSourceSavedSearch
   * signal with the results.
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadSourceSavedSearch(): void {
    this.#updateLoadingStatus('isSourceSaveSearchLoading', true)
    const { projectId } = this.reviewSetForm.getRawValue()
    this.httpClient
      .post(
        `${environment.apiUrl}production/project/${projectId}/saved-searches`,
        this.navigationModelInfo()
      )
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          return from(
            camelCaseConvertorService.convertToCase<
              ReviewSetSourceSavedSearch[]
            >(response, 'camelCase')
          )
        }),
        catchError(() => {
          this.#updateLoadingStatus('isSourceSaveSearchLoading', false)
          return of([] as ReviewSetSourceSavedSearch[])
        }),
        take(1)
      )
      .subscribe((source) => {
        this.#updateLoadingStatus('isSourceSaveSearchLoading', false)
        this.allSourceSavedSearch.set(source)

        // once the patching is done, we no longer need to retain the local state
        this.shallWaitForClone = false
      })
  }

  /**
   * Loads user groups for the current project.
   *
   * Fetches user group data from the API for the selected project,
   * converts response to camelCase, transforms parentId values to match
   * Kendo UI requirements (null instead of negative values), and updates
   * the allProjectUsers signal with the result.
   *
   * Updates loading status before and after the HTTP request.
   * @returns {void}
   */
  #loadProjectUserGroups(): void {
    this.#updateLoadingStatus('isProjectUserLoading', true)
    const { projectId } = this.reviewSetForm.getRawValue()
    this.httpClient
      .get<ResponseModel>(`${environment.apiUrl}user/project/${projectId}`)
      .pipe(
        switchMap((response) => {
          const camelCaseConvertorService = new CaseConvertorService()
          const convertedData = camelCaseConvertorService.convertToCase<
            UserGroupTree[]
          >(response.data, 'camelCase')
          return from(convertedData)
        }),
        catchError(() => {
          this.#updateLoadingStatus('isProjectUserLoading', false)
          return of([] as UserGroupTree[])
        }),
        take(1)
      )
      .subscribe((source) => {
        this.#updateLoadingStatus('isProjectUserLoading', false)
        this.allProjectUsers.set(
          source.map((u) => ({
            ...u,
            // Kendo doesn't like to have non-numeric values for the parentId as it expects null or a positive number
            parentId: u.parentId <= 0 ? null : u.parentId,
          }))
        )
      })
  }

  /**
   * Loads source data based on the selected review source type.
   *
   * Delegates to the appropriate loading method depending on whether
   * the current review source is a tag, folder, or saved search.
   * @returns {void}
   */
  #loadSources(): void {
    const { reviewSource } = this.reviewSetForm.getRawValue()
    switch (reviewSource) {
      case ReviewSetSourceTypes.TAG:
        this.#loadSourceTags()
        break
      case ReviewSetSourceTypes.FOLDER:
        this.#loadSourceFolders()
        break
      case ReviewSetSourceTypes.SAVED_SEARCH:
        this.#loadSourceSavedSearch()
        break
    }
  }

  /**
   * Validator that makes the control required if a specified condition is true.
   *
   * @param {FormGroup<ReviewSetForm>} group - The key of the control to check in the parent form.
   * @returns {ValidationErrors | null} - A validator function.
   */
  #crossFieldValidator(
    group: FormGroup<ReviewSetForm>
  ): ValidationErrors | null {
    const reviewSource = group.controls.reviewSource?.value
    const tagIdControl = group.controls.tagId
    const folderIdControl = group.controls.folderId
    const savedSearchIdControl = group.controls.savedSearchId
    switch (reviewSource) {
      case ReviewSetSourceTypes.TAG:
        return tagIdControl?.value?.length > 0 ? null : { required: true }
      case ReviewSetSourceTypes.FOLDER:
        return folderIdControl?.value?.length > 0 ? null : { required: true }
      case ReviewSetSourceTypes.SAVED_SEARCH:
        return +savedSearchIdControl?.value > 0 ? null : { required: true }
      default:
        return null
    }
  }

  /**
   * Creates a validator function that checks if an array has at least the specified minimum length.
   *
   * @param {number} min - The minimum required length of the array
   * @returns {ValidatorFn} - Validator function that returns null if valid or an error object if invalid
   */
  #arrayMinLengthValidator(min: number): ValidatorFn {
    return (control: AbstractControl): { [key: string]: unknown } | null => {
      const value = control.value

      if (Array.isArray(value) && value.length >= min) {
        return null
      }
      return { arrayMinLength: true }
    }
  }

  /**
   * Transforms tag tree data with string IDs into a hierarchical structure with numeric IDs.
   *
   * The process:
   * 1. Builds an adjacency list and node map from the input data
   * 2. Identifies root nodes (where treeParentId is '-1' or empty)
   * 3. Performs breadth-first traversal to:
   *    - Detect any cycles in the tree
   *    - Assign sequential numeric IDs to each node
   * 4. Maps the original data to include numeric 'id' and 'parentId' properties
   *
   * @param {TagsModel[]} data - Array of tag models with string-based tree identifiers
   * @returns {TagsModel[]} - Transformed array with numeric id/parentId relationships
   * @throws {Error} - If a cycle is detected in the tree structure
   */
  #mapTreeToIdParentId(data: TagsModel[]): TagsModel[] {
    if (!data.length) return []

    // 1) Build adjacency list and node map
    const adjacency = new Map<string, string[]>()
    const itemByKeyId = new Map<string, TagsModel>()

    for (const item of data) {
      const key = item.treeKeyId
      itemByKeyId.set(key, item)
      const parentKey = item.treeParentId

      if (!adjacency.has(parentKey)) {
        adjacency.set(parentKey, [])
      }
      adjacency.get(parentKey).push(key)
    }

    // 2) Identify roots (treeParentId === '-1' OR empty string)
    const rootKeys = data
      .filter((x) => x.treeParentId === '-1' || x.treeParentId === '')
      .map((x) => x.treeKeyId)

    // 3) Detect cycles and assign numeric IDs via BFS
    const visited = new Set<string>()
    const queue = [...rootKeys]
    const keyToNumericId = new Map<string, number>()
    let currentId = 1

    while (queue.length > 0) {
      const currentKey = queue.shift()

      if (visited.has(currentKey)) {
        throw new Error(`Cycle detected at node ${currentKey}`)
      }

      visited.add(currentKey)
      keyToNumericId.set(currentKey, currentId++)

      const children = adjacency.get(currentKey) || []
      for (const childKey of children) {
        if (!visited.has(childKey) && !queue.includes(childKey)) {
          queue.push(childKey)
        }
      }
    }

    // 4) Map to numeric IDs
    return data.map((item) => {
      const numericId = keyToNumericId.get(item.treeKeyId) ?? null
      const parentId =
        item.treeParentId === '-1' || item.treeParentId === ''
          ? null
          : keyToNumericId.get(item.treeParentId) ?? null

      return { ...item, id: numericId, parentId }
    })
  }

  /**
   * Updates a specific loading status property.
   *
   * Modifies the specified property in the loading status signal while preserving other values.
   *
   * @param {LoadingStatus} prop - The loading status property to update
   * @param {boolean} value - The new boolean value to set
   * @returns {void}
   */
  #updateLoadingStatus(prop: keyof LoadingStatus, value: boolean): void {
    this.loadingStatus.update((prev) => ({
      ...prev,
      [prop]: value,
    }))
  }

  /**
   * Converts a string or number input into an array of numbers.
   *
   * If input is a number, returns a single-item array.
   * If input is a string, splits by commas, converts to numbers, and filters out NaN values.
   *
   * @param {string | number} value - Input value to parse into number array
   * @returns {number[]} - Array of valid numbers parsed from the input
   */
  #parseIdList(value: string | number): number[] {
    if (typeof value === 'number') return [value]
    return (value?.toString() || '')
      .split(',')
      .map(Number)
      .filter((n) => !isNaN(n))
  }

  /**
   * Maps viewable tags to display tags in the form, including their parent tags.
   *
   * Creates an effect that waits for tag data to load, then filters all tags to match
   * the provided viewable tags by tagId. Also includes parent tags of the matched tags.
   * Updates the form control with the combined tag list and cleans up the effect.
   *
   * @param {TagsModel[]} viewableTags - Array of tag models to be displayed
   * @returns {void} - Returns early if tag tree is loading or no tags exist
   */
  #mapDisplayTags(viewableTags: TagsModel[]): void {
    if (!viewableTags) return

    const displayTagMapEffectRef = effect(
      () => {
        const isTagTreeLoading = this.isTagTreeLoading()
        if (isTagTreeLoading) return

        const all = this.allTags()
        if (!all.length) return

        const tags = all.filter((t) =>
          viewableTags.some((v) => v.tagId === t.tagId)
        )
        const withParents = all.filter((c) =>
          tags.some((t) => c.id === t.parentId)
        )
        this.reviewSetForm.controls.displayTag.patchValue(
          tags.concat(withParents)
        )

        // Once we are done patching display tags, we no longer need this effect
        displayTagMapEffectRef.destroy()
      },
      { injector: this.injector }
    )
  }

  /**
   * Maps selected user groups from an external format to internal user group objects.
   *
   * This method filters the project's user groups based on matching names with the provided
   * selected user groups array. Once matching groups are found, it updates the form control
   * with these mapped user group objects and destroys the effect.
   *
   * @param {unknown[]} selectedUserGroups - Array of user group objects from external source
   *                                         that contain a 'userGroupName' property
   * @returns {void} - Returns early if selectedUserGroups is empty or falsy
   */
  #mapUserGroups(selectedUserGroups: unknown[]): void {
    if (!selectedUserGroups?.length) return

    const userGroupEffectRef = effect(
      () => {
        const userGroups = this.allProjectUsers()
        if (!userGroups.length) return

        const mappedGroups = userGroups.filter((u) =>
          selectedUserGroups.some(
            (s) => s['userGroupName'].toLowerCase() === u.name.toLowerCase()
          )
        )

        this.reviewSetForm.controls.selectedUserGroups.patchValue(
          mappedGroups,
          {
            onlySelf: false,
            emitEvent: true,
          }
        )
        // Once we are done patching user groups, we no longer need this effect
        userGroupEffectRef.destroy()
      },
      { injector: this.injector }
    )
  }

  /**
   * Disables form controls that should not be editable when editing an existing review set.
   * This method ensures that critical configuration settings cannot be changed after a review set is created.
   *
   * @param {number} reviewSetId - The ID of the review set being edited. A value > 0 indicates edit mode.
   * @returns {void}
   */
  #nonEditableControls(reviewSetId: number): void {
    const controls = this.reviewSetForm.controls
    const isEdit = reviewSetId > 0
    const source = controls.reviewSource.getRawValue()
    if (!isEdit) return

    // Changing the project is not allowed in edit mode
    controls.projectId.disable()

    // disable batch controls
    controls.batchPrefix.disable()
    controls.batchStartNumber.disable()
    controls.batchPaddingLength.disable()
    controls.batchSize.disable()
    controls.purpose.disable()

    // disable source controls based on the selected source type
    switch (source) {
      case ReviewSetSourceTypes.TAG:
        controls.tagId.disable()
        controls.tagSelectionOption.disable()
        break
      case ReviewSetSourceTypes.FOLDER:
        controls.folderId.disable()
        controls.folderSelectionOption.disable()
        break
      case ReviewSetSourceTypes.SAVED_SEARCH:
        controls.savedSearchId.disable()
        break
    }
    controls.reviewSource.disable()

    // doc sort are disabled
    controls.orderByField.disable()
    controls.sortOrder.disable()
    controls.sortByCustodian.disable()
    controls.custodianSortOrder.disable()

    // include/exclude controls are disabled
    controls.parentChildIncluded.disable()
    controls.msgThreadIncluded.disable()
    controls.excludePrevReviewSetDoc.disable()
    controls.excludeNonInclusiveEmails.disable()

    // If checked then it should be disabled for edit
    if (controls.autoQueueForHtmlConversion.value) {
      controls.autoQueueForHtmlConversion.disable()
    }
    if (controls.autoQueueForTiff.value) {
      controls.autoQueueForTiff.disable()
    }

    // CAL and convert native to html controls are disabled
    controls.useCALProfileForReviewSet.disable()
    controls.calControlSetMinDocCount.disable()
    controls.calControlSetMaxDocCount.disable()
    controls.controlSetSizeDerivedBy.disable()
    controls.controlSetPercentFromTrainingBatch.disable()
    controls.confidenceLevel.disable()
    controls.confidenceInterval.disable()
  }

  #resetDefaultFormState(): void {
    const isEdit = this.reviewSetForm.controls.reviewSetId.value > 0
    const projectId = this.reviewSetForm.controls.projectId.value
    if (isEdit) return
    this.reviewSetForm.reset({
      projectId,
    })
  }

  /**
   * Loads a review set template into the form.
   *
   * @returns {void}
   */
  public handleReviewSetTemplateChange(): void {
    const templateId = this.selectedTemplateId()
    const isEdit = this.reviewSetForm.controls.reviewSetId.value > 0
    if (isEdit) return

    const template = this.allReviewSetTemplate().find(
      (t) => t.templateId === templateId
    )

    if (!template?.reviewSetInformation) {
      return
    }

    // Extract and prepare the review set information
    const reviewSetInfo = { ...template.reviewSetInformation }

    // Save original user groups for special handling
    const originalUserGroups = reviewSetInfo.selectedUserGroups

    // Create a new review set (not editing)
    reviewSetInfo.reviewSetId = 0

    // Set name to template name
    reviewSetInfo.name = template.templateName

    // Apply proper defaults for sorting fields
    // For CAL review sets, the default orderByField should be 'CAL'
    reviewSetInfo.orderByField = reviewSetInfo.isCalReviewSet
      ? 'CAL'
      : reviewSetInfo.orderByField || 'INGESTION_ORDER'

    // Default sort orders
    reviewSetInfo.sortOrder = reviewSetInfo.sortOrder || 'asc'
    reviewSetInfo.custodianSortOrder = reviewSetInfo.custodianSortOrder || 'asc'

    // Default for sortByCustodian should be true
    reviewSetInfo.sortByCustodian = reviewSetInfo.sortByCustodian !== false

    // Map selection options with defaults
    reviewSetInfo.tagSelectionOption = reviewSetInfo.tagSelectionOption || 'OR'
    reviewSetInfo.folderSelectionOption =
      reviewSetInfo.folderSelectionOption || 'OR'

    // Map boolean property name difference
    reviewSetInfo.useCALProfileForReviewSet = reviewSetInfo.isCalReviewSet

    // Ensure autoCollectionSelectionCriteria is correctly derived
    reviewSetInfo.autoCollectionSelectionCriteria = !(
      reviewSetInfo.autoCollectReviewset > 0
    )

    // Map CAL profile properties to top level with proper defaults
    if (reviewSetInfo.isCalReviewSet && reviewSetInfo.calProfileInfo) {
      const cal = reviewSetInfo.calProfileInfo

      // Note the property name difference (CAL vs Cal in the property names)
      reviewSetInfo.trainingRecallThreshold = cal.trainingRecallThreshold || 70
      reviewSetInfo.categoryTrainingThreshold =
        cal.categoryTrainingThreshold || 70
      reviewSetInfo.predictionAccuracyThreshold =
        cal.predictionAccuracyThreshold || 70
      reviewSetInfo.batchRichnessThreshold = cal.batchRichnessThreshold || 10
      reviewSetInfo.reviewRelevanceThreshold =
        cal.reviewRelevanceThreshold || 80
      reviewSetInfo.allowReviewAfterCALThreshold =
        cal.allowReviewAfterCalThreshold !== false
      reviewSetInfo.controlSetSizeDerivedBy = cal.controlSetSizeDerivedBy || 0
      reviewSetInfo.percentageOfPopulation = cal.percentageOfPopulation || 10
      reviewSetInfo.numberOfDocuments = cal.numberOfDocuments || 1000
      reviewSetInfo.isDynamicControlSet = cal.isDynamicControlSet !== false
      reviewSetInfo.calControlSetDocCount = cal.calControlSetDocCount || 0
      reviewSetInfo.confidenceLevel = cal.confidenceLevel || 95
      reviewSetInfo.confidenceInterval = cal.confidenceInterval || 5
      reviewSetInfo.controlSetPercentFromTrainingBatch =
        cal.controlSetPercentFromTrainingBatch || 5
      reviewSetInfo.calControlSetMinDocCount = cal.calControlSetMinDocCount || 5
      reviewSetInfo.calControlSetMaxDocCount = cal.calControlSetMaxDocCount || 0
      reviewSetInfo.calTrainingSetMinDocCount =
        cal.calTrainingSetMinDocCount || 5
      reviewSetInfo.calTrainingSetMaxDocCount =
        cal.calTrainingSetMaxDocCount || 3000
    }

    // Make sure tag and folder IDs are properly handled
    reviewSetInfo.tagId = reviewSetInfo.tagId || null
    reviewSetInfo.folderId = reviewSetInfo.folderId || null

    // Only set date if it's a valid, non-default date
    const dateStr = reviewSetInfo.autoCollectExpiresOn
    const isDefaultDate =
      dateStr === '0001-01-01T00:00:00' || dateStr === '1900-01-01T00:00:00'

    reviewSetInfo.autoCollectExpiresOn = !isDefaultDate
      ? dayjs(dateStr).format('MM/DD/YYYY')
      : ''

    // Empty the user groups array to be filled later with proper mapping
    reviewSetInfo.selectedUserGroups = []

    // Process the data with the existing handler
    this.#handleSourceDataLoaded(reviewSetInfo)

    // Special handling for template user groups format
    if (originalUserGroups?.length) {
      this.#mapTemplateUserGroups(originalUserGroups)
    }

    // For CAL, the sorting options are disabled
    const controls = this.reviewSetForm.controls
    // doc sort are disabled
    reviewSetInfo.isCalReviewSet
      ? controls.orderByField.disable()
      : controls.orderByField.enable()
    reviewSetInfo.isCalReviewSet
      ? controls.sortOrder.disable()
      : controls.sortOrder.enable()
    reviewSetInfo.isCalReviewSet
      ? controls.sortByCustodian.disable()
      : controls.sortByCustodian.enable()
    reviewSetInfo.isCalReviewSet
      ? controls.custodianSortOrder.disable()
      : controls.custodianSortOrder.enable()
  }

  /**
   * Maps template-specific user group format to form user group objects.
   *
   * @param {any[]} templateUserGroups - User groups from template
   * @returns {void}
   */
  #mapTemplateUserGroups(templateUserGroups: any[]): void {
    if (!templateUserGroups?.length) return

    const userGroupEffectRef = effect(
      () => {
        const userGroups = this.allProjectUsers()
        if (!userGroups.length) return

        const mappedGroups = []

        for (const tGroup of templateUserGroups) {
          // Template uses numeric type (0=USER, 1=GROUP)
          const userType = tGroup.type === 0 ? 'USER' : 'GROUP'
          const id = tGroup.userGroupId

          // Find matching user/group by ID and type
          const match = userGroups.find((u) => {
            if (userType === 'USER' && u.type === 'USER') {
              return u.userId === id
            }
            if (userType === 'GROUP' && u.type === 'GROUP') {
              return u.groupId === id
            }
            return false
          })

          if (match) {
            mappedGroups.push(match)
          }
        }

        if (mappedGroups.length) {
          this.reviewSetForm.controls.selectedUserGroups.patchValue(
            mappedGroups,
            {
              onlySelf: false,
              emitEvent: true,
            }
          )
        }

        userGroupEffectRef.destroy()
      },
      { injector: this.injector }
    )
  }
}
