<form [formGroup]="outputOption">
  <div>
    <div>
      <kendo-label class="t-font-semibold" text="Output Option"></kendo-label>
    </div>
    <div class="t-flex t-flex-col t-p-1">
      <div class="t-pt-2 t-flex t-items-center">
        <input
          id="individual-document-option"
          size="small"
          type="radio"
          formControlName="outputOptionButton"
          value="true"
          kendoRadioButton
          #individualDocumentRadio />
        <kendo-label
          class="t-k-radio-label t-px-[5px] t-relative"
          for="individual-document-option"
          [ngClass]="{
            't-text-primary t-font-medium':
              outputOption.get('outputOptionButton')?.value === 'true' &&
              !individualDocumentRadio.disabled
          }"
          text="Create Individual PDF for each selected document"></kendo-label>
      </div>
      <div class="t-pt-2 t-w-1/2">
        <kendo-dropdownlist
          id="individual-pdf-name"
          [data]="fieldValueList"
          [valuePrimitive]="true"
          [textField]="'text'"
          [valueField]="'value'"
          data-qa="individual-pdf-name-dropdown"
          formControlName="fieldValue"></kendo-dropdownlist>
      </div>
      <div class="t-p-1">
        <div class="t-pt-2 t-flex t-items-center">
          <input
            id="multiple-document-option"
            size="small"
            type="radio"
            formControlName="outputOptionButton"
            value="false"
            kendoRadioButton
            #multipleDocumentRadio />
          <kendo-label
            class="t-k-radio-label t-px-[5px] t-relative"
            for="multiple-document-option"
            [ngClass]="{
              't-text-primary t-font-medium':
                outputOption.get('outputOptionButton')?.value === 'false' &&
                !multipleDocumentRadio.disabled
            }"
            text="Create one PDF for this job that include all selected documents"></kendo-label>
        </div>
        <div class="t-pt-1 t-ml-4 t-flex t-flex-col">
          <div class="t-px-2 t-py-1 t-flex t-items-center">
            <input
              id="multiple-dcument-option-with-bookmarks"
              size="small"
              type="radio"
              formControlName="outputSettings"
              value="PRINT_COMBINED_PDF_WITH_BOOKMARK"
              kendoRadioButton
              #multipleDocumentWithBookmarksRadio />
            <kendo-label
              class="t-k-radio-label t-px-[5px] t-relative"
              for="multiple-dcument-option-with-bookmarks"
              [ngClass]="{
                't-text-primary t-font-medium':
                  outputOption.get('outputSettings')?.value ===
                    'PRINT_COMBINED_PDF_WITH_BOOKMARK' &&
                  !multipleDocumentWithBookmarksRadio.disabled
              }"
              text="Add bookmark for each document in PDF"></kendo-label>
          </div>
          <div class="t-px-2 t-py-1 t-flex t-items-center">
            <input
              id="multiple-document-option-without-bookmarks"
              size="small"
              type="radio"
              formControlName="outputSettings"
              value="PRINT_COMBINED_PDF_WITHOUT_BOOKMARK"
              kendoRadioButton
              #multipleDocumentWithoutBookmarksRadio />
            <kendo-label
              class="t-k-radio-label t-px-[5px] t-relative"
              for="multiple-document-option-without-bookmarks"
              [ngClass]="{
                't-text-primary t-font-medium':
                  outputOption.get('outputSettings')?.value ===
                    'PRINT_COMBINED_PDF_WITHOUT_BOOKMARK' &&
                  !multipleDocumentWithoutBookmarksRadio.disabled
              }"
              text="Do not add bookmark"></kendo-label>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
