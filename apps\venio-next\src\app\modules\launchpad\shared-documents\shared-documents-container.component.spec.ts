import { TestBed } from '@angular/core/testing'
import { SharedDocumentsContainerComponent } from './shared-documents-container.component'
import { CommonModule } from '@angular/common'
import { SharedDocumentToolbarComponent } from './shared-document-toolbar/shared-document-toolbar.component'
import { SharedDocumentsComponent } from './shared-documents-grid/shared-documents.component'
import { SharedDocumentGridActionsComponent } from './shared-document-grid-actions/shared-document-grid-actions.component'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import {
  DialogContainerDirective,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentShareFacade,
  SharedDocRequestType,
} from '@venio/data-access/review'
import { IframeMessengerService } from '@venio/data-access/iframe-messenger'
import { of } from 'rxjs'
import { provideHttpClient } from '@angular/common/http'

describe('SharedDocumentsContainerComponent', () => {
  let component: SharedDocumentsContainerComponent
  let fixture: any
  let mockDialogService: Partial<DialogService>
  let mockSharedDocFacade: Partial<DocumentShareFacade>

  beforeEach(async () => {
    mockDialogService = {
      open: jest.fn(),
    }

    mockSharedDocFacade = {
      selectIsSharedDocumentsLoading$: of(false),
      selectSharedDocDetailPagingInfo$: of({ pageNumber: 1, pageSize: 10 }),
      selectSharedDocumentList$: of([]),
      selectSharedDocumentRequestInfo$: of({
        requestType: SharedDocRequestType.All,
        pageNumber: 1,
        pageSize: 10,
        searchText: '',
      }),
      fetchSharedDocuments: jest.fn(),
      fetchSharedDocumentCountDetail: jest.fn(),
      updateSharedDocumentRequestInfo: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        SharedDocumentsContainerComponent,
        SharedDocumentToolbarComponent,
        SharedDocumentsComponent,
        SharedDocumentGridActionsComponent,
        DropDownListModule,
        DialogContainerDirective,
      ],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideMockStore({}), // Can also remove if unused
        { provide: DialogService, useValue: mockDialogService },
        { provide: DocumentShareFacade, useValue: mockSharedDocFacade },
        { provide: IframeMessengerService, useValue: jest.fn() },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SharedDocumentsContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
