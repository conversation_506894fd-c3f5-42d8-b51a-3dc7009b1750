import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnInit,
  runInInjectionContext,
  signal,
  ViewChild,
  OnDestroy,
  ChangeDetectorRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownListComponent,
  DropDownsModule,
  MultiSelectComponent,
  RemoveTagEvent,
} from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { distinctUntilChanged, filter, Subject, takeUntil } from 'rxjs'
import { LegalHoldReportFacade } from '@venio/data-access/common'
import {
  ActiveTerminatedLegalHolds,
  LegalHoldsModel,
} from '@venio/shared/models/interfaces'
import { CheckBoxModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ReportsFacade } from '@venio/data-access/reports'
import { ReportTypes } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-activate-deactivate-custodian-report',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    IconsModule,
    CheckBoxModule,
    LabelModule,
  ],
  templateUrl: './activate-deactivate-custodian-report.component.html',
  styleUrl: './activate-deactivate-custodian-report.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivateDeactivateCustodianReportComponent
  implements OnInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  private readonly reportFacade = inject(ReportsFacade)

  private readonly legalHoldReportFacade = inject(LegalHoldReportFacade)

  private cdr = inject(ChangeDetectorRef)

  private injector = inject(Injector)

  @ViewChild(MultiSelectComponent)
  public multiSelect: MultiSelectComponent

  public readonly filteredLegalHold = signal<any[]>([])

  public readonly legalHold = signal<LegalHoldsModel[]>([])

  public readonly isAllLegalHoldChecked = signal<boolean>(true)

  public readonly selectedLegalHold = signal<number[]>([])

  public readonly selectedReportType = signal<ReportTypes>(
    ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT
  )

  public readonly chevronDownIcon = chevronDownIcon

  public readonly filteredStatuses = signal<any[]>([])

  public readonly statuses = signal<any[]>([])

  public readonly isAllStatusesChecked = signal<boolean>(true)

  public readonly selectedStatus = signal<boolean | null>(null)

  public legalHoldSelectionChange(holdIds: number[]): void {
    this.selectedLegalHold.set(holdIds)
    if (this.selectedLegalHold().length) {
      this.isAllLegalHoldChecked.set(false)
    }
    this.#storeSelectedLegalHold()
  }

  public statusSelectionChange(statusValue: boolean | null): void {
    this.selectedStatus.set(statusValue)
    this.#storeSelectedStatuses()
  }

  public tagMapper = (items: any[]): any[] | any[][] => {
    if (this.isAllLegalHoldChecked()) {
      // if there is a default value or all users, which in this case, should be shown as well.
      return [
        {
          holdId: null,
          clientMatterName: 'All Legal Holds',
        } as ActiveTerminatedLegalHolds,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public ngOnInit(): void {
    this.#selectSelectedReportType()
    this.#fetchLegalHoldList()
    this.#fetchStatusList()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public filterUsers(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredLegalHold.set(
      this.legalHold().filter((item) =>
        item.clientMatterName.toLowerCase().includes(filterValue)
      )
    )
  }

  public chevDownIconClick(
    userSelection: MultiSelectComponent | DropDownListComponent
  ): void {
    userSelection.toggle()
    userSelection.focus()
  }

  public allLegalHoldSelectionChange(checked: boolean): void {
    this.isAllLegalHoldChecked.set(checked)
    if (checked) {
      this.selectedLegalHold.set(this.legalHold().map((item) => item.holdId))
    } else {
      this.selectedLegalHold.set([])
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.holdId !== null
      )
    }
  }

  public handleAllLegalHoldClick(
    event: MouseEvent,
    input: HTMLInputElement
  ): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allLegalHoldSelectionChange(input.checked)
  }

  public removeTag(event: RemoveTagEvent): void {
    if (event.dataItem.holdId === null) {
      this.isAllLegalHoldChecked.set(false)
      this.selectedLegalHold.set([])
      this.cdr.detectChanges()
    }
    if (event.dataItem.statusID === null) {
      this.isAllStatusesChecked.set(false)
    }
  }

  #fetchLegalHoldList(status?: boolean | null): void {
    this.legalHoldReportFacade.fetchActiveTerminatedLegalHoldList(
      status ?? null
    )
    this.legalHoldReportFacade.selectActiveTerminatedLegalHoldListSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response) => {
        this.legalHold.set(response?.data)
        this.filteredLegalHold.set(this.legalHold())
      })
  }

  #fetchStatusList(): void {
    this.legalHoldReportFacade.fetchActiveTerminatedStatus()
    this.legalHoldReportFacade.selectActiveTerminatedStatusSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response) => {
        this.statuses.set(response?.data)
        this.filteredStatuses.set(this.statuses())
      })
  }

  #storeSelectedLegalHold(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          this.reportFacade.storeSelectedActiveTerminatedLegalHold(
            this.selectedLegalHold()
          )
        },
        { allowSignalWrites: true }
      )
    )
  }

  #storeSelectedStatuses(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          this.reportFacade.storeSelectedActiveTerminatedStatuses(
            this.selectedStatus()
          )
        },
        { allowSignalWrites: true }
      )
    )
  }

  #selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(
        distinctUntilChanged(),
        filter((type) => Boolean(type)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((type) => {
        this.selectedReportType.set(type)
      })
  }
}
