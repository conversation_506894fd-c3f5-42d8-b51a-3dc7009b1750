import {
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { SharedDocumentToolbarComponent } from './shared-document-toolbar/shared-document-toolbar.component'
import { SharedDocumentsComponent } from './shared-documents-grid/shared-documents.component'
import { SharedDocumentGridActionsComponent } from './shared-document-grid-actions/shared-document-grid-actions.component'
import { LaunchpadAction } from '@venio/shared/models/interfaces'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import {
  DocumentShareFacade,
  SharedDocRequestType,
  SharedDocumentDetailModel,
} from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'
import { cloneDeep } from 'lodash'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  DialogContainerDirective,
  DialogRef,
  DialogService,
} from '@progress/kendo-angular-dialog'
import { PageArgs } from '@venio/ui/pagination'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-shared-documents-container',
  standalone: true,
  imports: [
    CommonModule,
    SharedDocumentToolbarComponent,
    SharedDocumentsComponent,
    SharedDocumentGridActionsComponent,
    DropDownListModule,
    DialogContainerDirective,
    TooltipsModule,
  ],
  templateUrl: './shared-documents-container.component.html',
  styleUrl: './shared-documents-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharedDocumentsContainerComponent implements OnInit, OnDestroy {
  private readonly sharedDocFacade = inject(DocumentShareFacade)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly dialogService = inject(DialogService)

  private selectedDocumentType: SharedDocRequestType = SharedDocRequestType.All

  public readonly isSharedDocsLoading = toSignal(
    this.sharedDocFacade.selectIsSharedDocumentsLoading$
  )

  public listItems: Array<{ text: string; value: SharedDocRequestType }> = [
    { text: 'All Document', value: SharedDocRequestType.All },
    { text: 'Shared By Me', value: SharedDocRequestType.SharedByMe },
    { text: 'Shared With Me', value: SharedDocRequestType.SharedToMe },
  ]

  public placeholderItem: { text: string; value: SharedDocRequestType } = {
    text: 'All Shared Document',
    value: SharedDocRequestType.All,
  }

  public ngOnInit(): void {
    this.#fetchShareDocumentList()
  }

  public sharedDocGridActionClick(event: LaunchpadAction): void {
    const { actionType, content } = event
    const selectedCase = cloneDeep(content)
    const payload = {
      actionType,
      selectedCase,
      isCaseActionClick: true,
    }
    switch (event.actionType) {
      case CommonActionTypes.EDIT:
        this.#openShareDocEditDialog(event?.content)
        break

      case CommonActionTypes.VIEW:
        this.#openSharedDocViewDialog(event?.content)
        break
      default:
        this.#notifyParentApp(payload)
    }
  }

  public pagingChanged(arg: PageArgs): void {
    this.#updatePagingInfo(arg)
    // this.#fetchShareDocumentList()
  }

  /**
   * Handles the search control change.
   * @param {string} value - The search term.
   * @param {ToolbarChangeType} type - The type of search. It can be a case search, review set search, or document share search.
   * @returns {void}
   */
  public searchControlChanged(value: string): void {
    // As soon as the search value changes, we need to update the search value in the shared document facade.
    this.#updateSearchValue(value)
    // this.#fetchShareDocumentList()
  }

  #updateSearchValue(searchText: string): void {
    this.sharedDocFacade.updateSharedDocumentRequestInfo({
      searchText,
    })
  }

  #updatePagingInfo(arg: PageArgs): void {
    const { pageNumber, pageSize } = arg
    this.sharedDocFacade.updateSharedDocumentRequestInfo({
      pageNumber,
      pageSize,
    })
  }

  #fetchShareDocumentList(): void {
    const caseId = -1
    const documentType = this.selectedDocumentType || SharedDocRequestType.All

    this.sharedDocFacade.fetchSharedDocuments(caseId, documentType)
    this.sharedDocFacade.fetchSharedDocumentCountDetail()
  }

  public onSharedDocFilterChange(value: SharedDocRequestType): void {
    this.selectedDocumentType = value
    this.sharedDocFacade.updateSharedDocumentRequestInfo({
      sharedDocRequestType: value,
    })

    // this.#fetchShareDocumentList()
  }

  #openShareDocEditDialog(data: SharedDocumentDetailModel | any): void {
    this.sharedDocFacade.isEditMode = true
    this.sharedDocFacade.fetchSharedDocumentDetail(
      data?.projectId,
      data?.documentShareId
    )
    import('@venio/feature/document-share').then((td) => {
      const dialog: DialogRef = this.dialogService.open({
        content: td.DocumentShareComponent,
        maxWidth: '1400px',
        maxHeight: '750px',
        width: '95%',
        height: '90vh',
      })

      const dialogContent = dialog.content.instance
      dialogContent.documentShareDialogData = {
        isDocShareEdit: true,
        projectId: data?.projectId,
      }
    })
  }

  #openSharedDocViewDialog(data: SharedDocumentDetailModel | any): void {
    this.sharedDocFacade.fetchSharedDocumentDetail(
      data?.projectId,
      data?.documentShareId
    )
    import(
      '../shared-documents/shared-documents-view/shared-document-view.component'
    ).then((td) => {
      this.dialogService.open({
        content: td.SharedDocumentViewComponent,
        maxWidth: '1200px',
        maxHeight: '500px',
        width: '90%',
        height: '60vh',
      })
    })
  }

  #notifyParentApp(content: unknown): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content,
      } as MessageContent,
    })
  }

  public ngOnDestroy(): void {
    // reset page size and page number stored on store when component is destroyed
    this.sharedDocFacade.updateSharedDocumentRequestInfo({
      searchText: '',
      pageNumber: 1,
      pageSize: 25,
    })
  }
}
