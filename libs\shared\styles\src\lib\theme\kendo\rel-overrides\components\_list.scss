@import 'variables';

// the following is to override the default kendo dropdown list styles
// to make the menu items look like the ones in the design
@layer {
  .k-dropdownlist {
    border-color: $border-grey-color !important;
    .k-button {
      color: $border-grey-color !important;
    }
  }

  .k-list-ul {
    padding: 0.625rem;
  }

  .k-list-item,
  .k-item {
    &:hover,
    &.k-selected {
      background: $hover-bg-color;
      color: #ffffff !important;
    }
  }

  .k-animation-container-shown {
    .k-menu-popup,
    .k-dropdownlist-popup,
    .v-document-menu-popup-container {
      @apply t-rounded-sm t-p-2.5 t-border t-border-[#ececeb] #{!important};
      .k-list-item,
      .k-menu-item {
        @apply t-text-sm t-capitalize #{!important};
        .k-menu-link-text {
          @apply t-overflow-visible #{!important};
        }
      }
    }
  }

  // Kendo group/header border overrides for dropdowns
  .k-list-group,
  .k-list-header,
  .k-group-header ,
  .v-dropdown-divider {
    @apply t-border-b-[1px] t-border-[#ececeb] t-border-dashed #{!important};
  }
}
