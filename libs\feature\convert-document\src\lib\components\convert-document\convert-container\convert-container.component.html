<div class="convert-container-size">
  <kendo-dialog-titlebar (close)="onCancelAction()">
    <div class="t-flex t-flex-row t-gap-2">
      <div
        class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-flex t-items-center t-justify-center t-rounded-full">
        <img
          src="assets/svg/convert.svg"
          alt="Convert Icon"
          style="width: 20px; height: 20px" />
      </div>
      <div
        class="t-flex t-text-[#2F3080DE] t-text-[16px] t-font-medium t-relative t-top-[10px] t-ml-2">
        Convert
      </div>
    </div>
  </kendo-dialog-titlebar>
  <div
    class="t-flex t-flex-col t-border t-border-t-[#ebebeb] t-border-x-0 t-border-b-0 t-h-full">
    <div class="t-flex t-gap-5 t-h-full">
      <div class="t-flex t-flex-1 t-flex-col t-w-full t-h-full">
        <kendo-tabstrip
          class="t-h-full"
          (tabSelect)="tabSelected($event)"
          #convertDocTabStrip>
          <ng-container *ngFor="let tab of getEnabledTabs(); let i = index">
            <kendo-tabstrip-tab
              [title]="tab.title"
              *venioHasUserGroupRights="tab.right"
              [disabled]="tab.disabled">
              <ng-template kendoTabContent>
                <ng-container *ngComponentOutlet="tab.componentRef">
                </ng-container>
              </ng-template>
            </kendo-tabstrip-tab>
          </ng-container>
        </kendo-tabstrip>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-justify-between">
      <div class="t-flex-col t-w-2/3">
        <div class="t-mt-2">
          <span *ngIf="launchVisible" class="t-m-1">
            <input
              type="checkbox"
              (change)="launchRelaunchChanged()"
              [(ngModel)]="launchChecked"
              [disabled]="!launchEnabled"
              id="launch-convert"
              kendoCheckBox />
            <label
              for="launch-convert"
              class="k-checkbox-label t-align-middle t-relative t-top-[1px]"
              [ngClass]="{
                't-text-primary t-font-medium': launchChecked && launchEnabled,
                't-text-disabled': !launchEnabled
              }"
              >Launch</label
            >
          </span>
          <span *ngIf="relaunchVisible" class="t-m-1">
            <input
              type="checkbox"
              (change)="launchRelaunchChanged()"
              [(ngModel)]="relaunchChecked"
              [disabled]="!relaunchEnabled"
              id="relaunch-convert"
              kendoCheckBox />
            <label
              for="relaunch-convert"
              class="k-checkbox-label t-align-middle t-relative t-top-[1px]"
              [ngClass]="{
                't-text-primary t-font-medium':
                  relaunchChecked && relaunchEnabled,
                't-text-disabled': !relaunchEnabled
              }"
              >Relaunch</label
            >
          </span>
          <span
            *ngIf="isOcrTabSelected || isAudioTranscribeSelected"
            class="t-m-1 t-ml-[10px] t-font-medium t-text-[#ED7425] t-relative t-top-[1px] t-text-[11px] t-uppercase t-leading-[15px]">
            NOTE:
            <span class="t-font-normal t-text-[#000000] t-normal-case">
              Password Protected, Corrupted, Zero Byte, System and Denist files
              will not be queued.</span
            >
          </span>
        </div>
      </div>
      <div class="t-flex t-flex-row t-justify-end t-w-auto">
        <div class="flex justify-end gap-4">
          <button
            kendoButton
            (click)="convertClicked()"
            [disabled]="disableConvertButton()"
            class="t-m-1 v-custom-secondary-button"
            fillMode="outline"
            themeColor="secondary">
            START
            <kendo-loader
              *ngIf="showSpinner"
              size="small"
              type="pulsing"
              class="-t-ml-[0.5rem] -t-mr-[1rem] t-pl-[0.5rem]"></kendo-loader>
          </button>
          <button
            kendoButton
            class="t-m-1 ml-2"
            (click)="onCancelAction()"
            themeColor="dark"
            fillMode="outline">
            CANCEL
          </button>
        </div>
      </div>
    </div>
  </kendo-dialog-actions>
</div>
