import 'fake-indexeddb/auto'

import { TestBed } from '@angular/core/testing'
import { Http<PERSON><PERSON><PERSON>, HttpRequest, HttpResponse } from '@angular/common/http'
import {
  LRUCache,
  SvgCacheEntry,
  SvgRequestInterceptor,
} from './svg-loader.interceptor'
import { lastValueFrom, of, throwError } from 'rxjs'
// optional if you want to simulate network latency

describe('SvgRequestInterceptor', () => {
  let interceptor: SvgRequestInterceptor
  let mockHandler: HttpHandler

  /**
   * Clear Dexie and in-memory caches before each run, ensuring a fresh test state.
   */
  beforeEach(async () => {
    TestBed.configureTestingModule({
      providers: [SvgRequestInterceptor],
    })
    interceptor = TestBed.inject(SvgRequestInterceptor)
    mockHandler = { handle: jest.fn() }

    // Clear in-memory LRU
    interceptor['memoryCache'] = new LRUCache<string, SvgCacheEntry>(2)
    interceptor['SVG_CACHE_EXPIRATION_MS'] = 10

    // Clear Dexie
    await interceptor.VenioSVGContentCache.clear()
  })

  /**
   * After each test, we clear mock calls and revert to real timers
   */
  afterEach(() => {
    jest.clearAllMocks()
    jest.clearAllTimers()
  })

  it('should create the interceptor instance', () => {
    // GIVEN the testing environment with SvgRequestInterceptor in the providers
    // WHEN retrieving the interceptor from TestBed
    const i = TestBed.inject(SvgRequestInterceptor)
    // THEN it should be defined
    expect(i).toBeTruthy()
  })

  it('should pass through non-GET requests without caching', async () => {
    // GIVEN a POST request to a .svg URL
    const postUrl = 'https://example.com/upload.svg'
    const postRequest = new HttpRequest('POST', postUrl, { foo: 'bar' })
    const mockResponse = new HttpResponse({ body: 'post-response' })

    jest.spyOn(mockHandler, 'handle').mockReturnValue(of(mockResponse))

    // WHEN the interceptor sees this non-GET request
    await lastValueFrom(interceptor.intercept(postRequest, mockHandler))

    // THEN it should pass through and not cache anything
    expect(mockHandler.handle).toHaveBeenCalledTimes(1)
    expect(interceptor['memoryCache'].get(postUrl)).toBeUndefined()

    const dbEntries = await interceptor.VenioSVGContentCache.toArray()
    expect(dbEntries).toHaveLength(0)
  })

  it('should pass through GET requests that do not end in .svg', async () => {
    // GIVEN a GET request for a PNG
    const nonSvgUrl = 'https://example.com/image.png'
    const getPngRequest = new HttpRequest('GET', nonSvgUrl)
    const mockResponse = new HttpResponse({ body: 'png-data' })

    jest.spyOn(mockHandler, 'handle').mockReturnValue(of(mockResponse))

    // WHEN the interceptor sees this request
    await lastValueFrom(interceptor.intercept(getPngRequest, mockHandler))

    // THEN it is passed through with no caching
    expect(mockHandler.handle).toHaveBeenCalledTimes(1)
    expect(interceptor['memoryCache'].get(nonSvgUrl)).toBeUndefined()

    const dbEntries = await interceptor.VenioSVGContentCache.toArray()
    expect(dbEntries).toHaveLength(0)
  })

  it('should handle GET .svg requests by caching them when not in memory or Dexie', async () => {
    // GIVEN a new .svg GET request
    const svgUrl = 'https://example.com/assets/image.svg'
    const mockRequest = new HttpRequest('GET', svgUrl)
    const svgBody = '<svg onclick="alert(1)">test</svg>'
    const mockResponse = new HttpResponse({ body: svgBody })

    jest.spyOn(mockHandler, 'handle').mockReturnValue(of(mockResponse))

    // WHEN we intercept for the first time
    await lastValueFrom(interceptor.intercept(mockRequest, mockHandler))

    // THEN it should be sanitized, hashed, and stored in both memory & Dexie
    expect(mockHandler.handle).toHaveBeenCalledTimes(1)

    const memEntry = interceptor['memoryCache'].get(svgUrl)
    expect(memEntry).toBeDefined()
    expect(memEntry?.sanitizedSvg).not.toContain('onclick') // sanitized

    const dbEntry = await interceptor.VenioSVGContentCache.get(svgUrl)
    expect(dbEntry).toBeDefined()
    expect(dbEntry?.sanitizedSvg).not.toContain('onclick')
  })

  it('should return cached response from memory if present and fresh', async () => {
    // GIVEN a .svg URL already in memory
    const svgUrl = 'https://example.com/assets/memory.svg'
    const sanitizedSvg = '<svg>cached</svg>'
    interceptor['memoryCache'].set(svgUrl, {
      url: svgUrl,
      sanitizedSvg,
      hash: interceptor['computeHash'](sanitizedSvg),
      timestamp: Date.now(),
      cacheVersion: interceptor['CACHE_VERSION'],
    })

    // WHEN the interceptor sees the same request
    const mockRequest = new HttpRequest('GET', svgUrl)
    await lastValueFrom(interceptor.intercept(mockRequest, mockHandler))

    // THEN we do not call the network again
    expect(mockHandler.handle).toHaveBeenCalledTimes(0)
  })

  it('should retrieve a fresh entry from Dexie when not in memory, then store in memory', async () => {
    // GIVEN a .svg entry only in Dexie
    const svgUrl = 'https://example.com/assets/db.svg'
    const dbContent = '<svg>fromDexie</svg>'

    await interceptor.VenioSVGContentCache.put({
      url: svgUrl,
      sanitizedSvg: dbContent,
      hash: interceptor['computeHash'](dbContent),
      timestamp: Date.now(),
      cacheVersion: interceptor['CACHE_VERSION'],
    })

    // WHEN we intercept the same GET request
    const mockRequest = new HttpRequest('GET', svgUrl)
    await lastValueFrom(interceptor.intercept(mockRequest, mockHandler))

    // THEN no network call, and memory is populated
    expect(mockHandler.handle).toHaveBeenCalledTimes(0)
    const memEntry = interceptor['memoryCache'].get(svgUrl)
    expect(memEntry?.sanitizedSvg).toBe(dbContent)
  })

  it('should remove tampered Dexie entry and fetch a fresh copy from network', async () => {
    // GIVEN a tampered entry in Dexie
    const svgUrl = 'https://example.com/assets/tampered.svg'
    await interceptor.VenioSVGContentCache.put({
      url: svgUrl,
      sanitizedSvg: '<svg>tampered</svg>',
      hash: 'invalid-hash',
      timestamp: Date.now(),
      cacheVersion: interceptor['CACHE_VERSION'],
    })

    // Mock network response
    const mockResponse = new HttpResponse({ body: '<svg>fresh</svg>' })
    jest.spyOn(mockHandler, 'handle').mockReturnValue(of(mockResponse))

    // WHEN the request is intercepted
    const mockRequest = new HttpRequest('GET', svgUrl)
    await lastValueFrom(interceptor.intercept(mockRequest, mockHandler))

    // THEN the old Dexie entry is removed, and network provides a new one
    expect(mockHandler.handle).toHaveBeenCalledTimes(1)

    const dbEntryAfter = await interceptor.VenioSVGContentCache.get(svgUrl)
    expect(dbEntryAfter?.sanitizedSvg).toContain('fresh')
  })

  it('should handle request errors by not caching the failing request', async () => {
    // GIVEN a failing .svg request
    const svgUrl = 'https://example.com/assets/error.svg'
    const errorReq = new HttpRequest('GET', svgUrl)

    jest
      .spyOn(mockHandler, 'handle')
      .mockReturnValue(throwError(() => new Error('Network error')))

    // WHEN we intercept and it fails
    await expect(
      lastValueFrom(interceptor.intercept(errorReq, mockHandler))
    ).rejects.toThrow('Network error')

    // THEN no memory or Dexie entry is created
    expect(interceptor['memoryCache'].get(svgUrl)).toBeUndefined()
    const dbEntry = await interceptor.VenioSVGContentCache.get(svgUrl)
    expect(dbEntry).toBeUndefined()
  })

  it('should expire cache entries past the configured time', async () => {
    // GIVEN an expired entry in Dexie
    const svgUrl = 'https://example.com/old.svg'
    const oldTimestamp =
      Date.now() - interceptor['SVG_CACHE_EXPIRATION_MS'] - 1000
    await interceptor.VenioSVGContentCache.put({
      url: svgUrl,
      sanitizedSvg: '<svg>expired</svg>',
      hash: interceptor['computeHash']('<svg>expired</svg>'),
      timestamp: oldTimestamp,
      cacheVersion: interceptor['CACHE_VERSION'],
    })

    // Mock a fresh network response
    const freshResponse = new HttpResponse({ body: '<svg>fresh</svg>' })
    jest.spyOn(mockHandler, 'handle').mockReturnValue(of(freshResponse))

    // WHEN we intercept
    const request = new HttpRequest('GET', svgUrl)
    const response$ = interceptor.intercept(request, mockHandler)

    await lastValueFrom(response$)

    // THEN network is called again because the old entry was stale
    expect(mockHandler.handle).toHaveBeenCalledTimes(1)
  })

  it('should evict least recently used entries in memory once capacity is reached', async () => {
    // GIVEN a smaller LRU capacity
    interceptor['memoryCache'] = new LRUCache<string, SvgCacheEntry>(2)

    // Insert 2 entries
    interceptor['memoryCache'].set('url1/assets', {
      url: 'url1/assets',
      sanitizedSvg: '<svg>1</svg>',
      hash: 'hash1',
      timestamp: Date.now(),
      cacheVersion: interceptor['CACHE_VERSION'],
    })
    interceptor['memoryCache'].set('url2/assets', {
      url: 'url2/assets',
      sanitizedSvg: '<svg>2</svg>',
      hash: 'hash2',
      timestamp: Date.now(),
      cacheVersion: interceptor['CACHE_VERSION'],
    })

    // WHEN adding a third
    interceptor['memoryCache'].set('url3/assets', {
      url: 'url3/assets',
      sanitizedSvg: '<svg>3</svg>',
      hash: 'hash3',
      timestamp: Date.now(),
      cacheVersion: interceptor['CACHE_VERSION'],
    })

    // THEN 'url1' should be evicted (LRU)
    expect(interceptor['memoryCache'].get('url1/assets')).toBeUndefined()
    expect(interceptor['memoryCache'].get('url2/assets')).toBeDefined()
    expect(interceptor['memoryCache'].get('url3/assets')).toBeDefined()
  })
})
