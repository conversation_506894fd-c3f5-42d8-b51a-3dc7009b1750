<div class="t-flex t-flex-col t-gap-4">
  <form [formGroup]="imageResizeOption">
    <div class="t-flex t-items-center">
      <input
        data-qa="resize-image-input"
        id="resize-image"
        class="custom-checkbox"
        formControlName="resizeImage"
        type="checkbox"
        kendoCheckBox
        #resizeImageCheckbox />
      <kendo-label
        class="k-checkbox-label t-top-[1px]"
        for="resize-image"
        text="Resize"
        [ngClass]="{
          't-text-primary t-font-medium':
            imageResizeOption.get('resizeImage')?.value &&
            !resizeImageCheckbox.disabled
        }" />
    </div>
    <div class="t-flex t-flex-col t-mt-1 t-ml-0">
      <div class="t-mt-2 t-w-48">
        <kendo-dropdownlist
          id="paper-size"
          formControlName="paperSize"
          [data]="tiffPaperSizesList"
          [valuePrimitive]="true"
          [textField]="'text'"
          [valueField]="'value'"
          data-qa="paper-size-dropdown"></kendo-dropdownlist>
      </div>
      <div class="t-mt-3 t-flex t-flex-row t-gap-4">
        <div class="t-w-48">
          <kendo-dropdownlist
            id="dimension-options"
            formControlName="dimension"
            [data]="dimensionOptionsList"
            [valuePrimitive]="true"
            [textField]="'text'"
            [valueField]="'value'"
            data-qa="dimension-options-dropdown"></kendo-dropdownlist>
        </div>
        <div>
          <div class="t-flex t-flex-col t-gap-0">
            <kendo-numerictextbox
              formControlName="width"
              [value]="8.5"
              [min]="0"
              class="t-w-[106px]"></kendo-numerictextbox>
            <div
              *ngIf="imageResizeOption.get('width')?.errors?.['required']"
              class="t-text-error t-text-xs">
              Width is required
            </div>
            <div
              *ngIf="imageResizeOption.get('width')?.errors?.['min']"
              class="t-text-error t-text-xs">
              Width must be greater than 0
            </div>
          </div>
        </div>
        <div>
          <div class="t-flex t-flex-col t-gap-0">
            <kendo-numerictextbox
              formControlName="height"
              [value]="11"
              [min]="0"
              class="t-w-[106px]"></kendo-numerictextbox>
            <div
              *ngIf="imageResizeOption.get('height')?.errors?.['required']"
              class="t-text-error t-text-xs">
              Height is required
            </div>
            <div
              *ngIf="imageResizeOption.get('height')?.errors?.['min']"
              class="t-text-error t-text-xs">
              Height must be greater than 0
            </div>
          </div>
        </div>
      </div>

      <div class="t-mt-3 t-flex t-flex-row t-items-center t-gap-4">
        <div class="t-w-48">
          <kendo-dropdownlist
            id="size-unit"
            formControlName="sizeUnit"
            [data]="sizeUnitOptions"
            [valuePrimitive]="true"
            [textField]="'text'"
            [valueField]="'value'"
            data-qa="size-unit-dropdown"></kendo-dropdownlist>
        </div>
        <div>
          <!-- Maintain Aspect Ratio -->
          <div class="t-flex t-items-center">
            <input
              data-qa="maintain-aspect-ratio-input"
              id="maintain-aspect-ratio"
              class="custom-checkbox"
              formControlName="maintainAspectRatio"
              type="checkbox"
              kendoCheckBox
              #maintainAspectRatioCheckbox />
            <kendo-label
              class="k-checkbox-label t-top-[1px] t-relative"
              for="maintain-aspect-ratio"
              [ngClass]="{
                't-text-primary t-font-medium': imageResizeOption.get(
                  'maintainAspectRatio'
                )?.value
              }"
              text="Maintain aspect ratio"></kendo-label>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
