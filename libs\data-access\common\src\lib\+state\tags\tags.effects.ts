import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import { catchError, map } from 'rxjs'
import * as TagsActions from './tags.actions'
import { TagsService } from '../../services/tags.service'
import { HttpErrorResponse } from '@angular/common/http'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { ResponseModel, TagsModel } from '@venio/shared/models/interfaces'

@Injectable()
export class TagsEffects {
  public fetchTagTree$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.fetchTagTree),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.tagsService.fetchTagTree(projectId, reviewSetId).pipe(
            map((tagTreeSuccessResponse) =>
              TagsActions.fetchTagTreeSuccess({ tagTreeSuccessResponse })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagTreeErrorResponse = error.error
          return TagsActions.fetchTagTreeFailure({ tagTreeErrorResponse })
        },
      })
    )
  )

  public fetchTagGroup$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.fetchTagGroup),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.tagsService.fetchTagGroup(projectId, reviewSetId).pipe(
            map((tagGroupSuccessResponse) =>
              TagsActions.fetchTagGroupSuccess({ tagGroupSuccessResponse })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagGroupErrorResponse = error.error
          return TagsActions.fetchTagGroupFailure({ tagGroupErrorResponse })
        },
      })
    )
  )

  public fetchTagTreeWithCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.fetchTagWithCount),
      fetch({
        run: ({ projectId }) => {
          return this.tagsService.fetchTagTreeWithCount(projectId).pipe(
            map((tagWithCountSuccessResponse) =>
              TagsActions.fetchTagWithCountSuccess({
                tagWithCountSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagWithCountErrorResponse = error.error
          return TagsActions.fetchTagWithCountFailure({
            tagWithCountErrorResponse,
          })
        },
      })
    )
  )

  public tagGroupAction$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.tagGroupActions),
      fetch({
        run: ({ payload: { projectId, data } }) => {
          const payloadWithoutNullValueProps = JSON.stringify(
            data,
            (key: never, value: unknown) => (value === null ? undefined : value)
          )
          const finalizedPayload = JSON.parse(payloadWithoutNullValueProps)

          return this.tagsService
            .addOrUpdateTagGroup(projectId, finalizedPayload)
            .pipe(
              map((tagGroupActionSuccessResponse) =>
                TagsActions.tagGroupActionSuccess({
                  tagGroupActionSuccessResponse,
                })
              ),
              catchError((error: unknown) => {
                throw error
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagGroupActionErrorResponse = error.error
          return TagsActions.tagGroupActionFailure({
            tagGroupActionErrorResponse,
          })
        },
      })
    )
  )

  public deleteTag$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.deleteTag),
      fetch({
        id: ({ projectId, tagId }) => tagId + projectId,
        run: ({ projectId, tagId, isTagGroup }) => {
          return this.tagsService
            .deleteTag(projectId, tagId, isTagGroup)
            .pipe(
              map((tagDeleteSuccessResponse) =>
                TagsActions.deleteTagSuccess({ tagDeleteSuccessResponse })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagDeleteErrorResponse = error.error
          return TagsActions.deleteTagFailure({ tagDeleteErrorResponse })
        },
      })
    )
  )

  /**
   * Maps the tag service response to a Redux action.
   * - Resets `isInUse` to null as it's not needed in the cloned data.
   * - Sets `tagIdLineage` statically to '1'.
   * This may need further clarification or dynamic setting based on use case.
   * - If the action type is 'CLONE', `reviewSetId` is set to -1 and `tagId` is reset to 0 to indicate a new tag.
   *   Otherwise, they retain their original values.
   * - Appends '-clone' to the tag name if the action type is 'CLONE'.
   * @param {ResponseModel}response - The response from the tag service.
   * @param {TagsModel}selected - The selected tag details.
   * @param {CommonActionTypes}actionType - The type of action being performed (e.g., CLONE).
   * @returns {TagsModel} A Redux action to set the selected tag.
   * @note
   * There is still some mutation are done from the place where the
   * tag form is used.
   * It is due to some needs to be updated before passing into form
   * and some are after the form data is returned.
   */
  #mergeResponseObjectAndExtend(
    response: ResponseModel,
    selected: TagsModel,
    actionType: CommonActionTypes
  ): TagsModel {
    return {
      ...response.data,
      isInUse: null, // Resetting as it's not needed in this context of edit,clone,create
      tagIdLineage: '1', // Static assignment may need dynamic setting
      reviewSetId:
        actionType === CommonActionTypes.CLONE ? -1 : selected.parentTagId, // -1 for clones, else use parentTagId
      tagId: actionType === CommonActionTypes.CLONE ? 0 : selected.tagId, // 0 for new clone, else use existing tagId
      tagName: `${selected.tagName}${
        actionType === CommonActionTypes.CLONE ? '-clone' : ''
      }`, // Append '-clone' for clones
    } as TagsModel
  }

  public fetchTagBySelectedId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.fetchSelectedTag),
      fetch({
        run: ({ selectedTag: { selected, actionType, projectId } }) => {
          return this.tagsService.fetchTag(projectId, selected.tagId).pipe(
            map((response) =>
              TagsActions.setSelectedTag({
                selectedTag: {
                  selected: this.#mergeResponseObjectAndExtend(
                    response,
                    selected,
                    actionType
                  ),
                  projectId,
                  actionType,
                },
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          return TagsActions.setSelectedTag({ selectedTag: undefined })
        },
      })
    )
  )

  public addOrUpdateTag$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.tagAddOrUpdate),
      fetch({
        run: ({ payload: { projectId, data } }) => {
          // const payloadWithoutNullValueProps = JSON.stringify(
          //   data,
          //   (key: never, value: unknown) => (value === null ? undefined : value)
          // )
          // const finalizedPayload = JSON.parse(payloadWithoutNullValueProps)

          return this.tagsService.addOrUpdateTag(projectId, data).pipe(
            map((tagAddOrUpdateSuccessResponse) =>
              TagsActions.tagAddOrUpdateSuccess({
                tagAddOrUpdateSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagAddOrUpdateErrorResponse = error.error
          return TagsActions.tagAddOrUpdateFailure({
            tagAddOrUpdateErrorResponse,
          })
        },
      })
    )
  )

  public fetchTagPropagationSetting$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.fetchTagPropagationSetting),
      fetch({
        run: ({ projectId }) => {
          return this.tagsService.fetchTagPropagationSetting(projectId).pipe(
            map((settingData) =>
              // since the API returning the data in an object,
              // we then format it here to consist with the other API response model type
              TagsActions.fetchTagPropagationSettingSuccess({
                tagPropagationSettingSuccessResponse: {
                  data: settingData,
                  status: 'success',
                  message: 'Fetch tag propagation settings successfully',
                },
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagPropagationSettingErrorResponse = error.error
          return TagsActions.fetchTagPropagationSettingFailure({
            tagPropagationSettingErrorResponse,
          })
        },
      })
    )
  )

  public saveTagReorder$ = createEffect(() =>
    this.actions$.pipe(
      ofType(TagsActions.saveTagReorder),
      fetch({
        run: ({ projectId, tagReorder }) => {
          return this.tagsService.saveTagOrder(projectId, tagReorder).pipe(
            map((tagReorderSuccessResponse) =>
              TagsActions.fetchTagReorderSuccess({
                tagReorderSuccessResponse,
              })
            ),
            catchError((error: unknown) => {
              throw error
            })
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const tagReorderErrorResponse = error.error
          return TagsActions.fetchTagReorderFailure({
            tagReorderErrorResponse,
          })
        },
      })
    )
  )

  constructor(
    private readonly actions$: Actions,
    private tagsService: TagsService
  ) {}
}
