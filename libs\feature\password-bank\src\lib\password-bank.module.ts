import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogModule } from '@progress/kendo-angular-dialog'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { PasswordBankContainerComponent } from './components/password-bank-container/password-bank-container.component'
import { PasswordManagementComponent } from './components/password-management/password-management.component'
import { LotusNotesPasswordComponent } from './components/lotus-notes-password-management/lotus-notes-password.component'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { InputsModule, TextBoxComponent } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { ImportPasswordBankComponent } from './components/import-password-bank/import-password-bank.component'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@NgModule({
  imports: [
    CommonModule,
    DialogModule,
    LayoutModule,
    FormsModule,
    ReactiveFormsModule,
    IndicatorsModule,
    TextBoxComponent,
    InputsModule,
    LabelModule,
    FormsModule,
    GridModule,
    TooltipModule,
    SVGIconComponent,
    ButtonModule,
    SvgLoaderDirective,
  ],
  declarations: [
    PasswordBankContainerComponent,
    PasswordManagementComponent,
    LotusNotesPasswordComponent,
    ImportPasswordBankComponent,
  ],
  exports: [PasswordBankContainerComponent],
})
export class PasswordBankModule {}
