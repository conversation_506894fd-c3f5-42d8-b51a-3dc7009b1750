import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  TemplateRef,
  ViewChild,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import {
  ButtonsModule,
  DropDownButtonModule,
} from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { LoaderModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-toolbar-history-log',
  standalone: true,
  imports: [
    CommonModule,
    DropDownButtonModule,
    NgOptimizedImage,
    SvgLoaderDirective,
    DropDownsModule,
    ButtonsModule,
    LoaderModule,
  ],
  templateUrl: './toolbar-history-log.component.html',
  styleUrls: ['./toolbar-history-log.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolbarHistoryLogComponent implements AfterViewInit {
  @ViewChild('parentElTag', { read: ElementRef })
  public parentElTag: ElementRef

  public kendoButtonEl: HTMLElement

  public historyDropdownData: Array<{ id: number; text: string }> = [
    {
      text: 'Search Log History',
      id: 1,
    },
    {
      text: 'Saved Search',
      id: 2,
    },
    {
      text: 'Distributed Search Status',
      id: 3,
    },
  ]

  private searchLogHistoryDialogRef: DialogRef

  private dsStatusDialogRef: DialogRef

  constructor(public dialogService: DialogService) {}

  public ngAfterViewInit(): void {
    const native = this.parentElTag?.nativeElement
    this.kendoButtonEl = native?.querySelector('button') as HTMLElement
  }

  #openSearchLogHistory(
    actionTemplate: TemplateRef<unknown>,
    titleString: string,
    isSavedSearch: boolean
  ): void {
    import(
      '../../../history-log/search-log-history/search-log-history.component'
    ).then((comp) => {
      const SearchLogHistoryComponent = comp.SearchLogHistoryComponent
      this.searchLogHistoryDialogRef = this.dialogService.open({
        title: titleString,
        content: SearchLogHistoryComponent,
        actions: actionTemplate,
        maxHeight: '90vh',
        maxWidth: '90vw',
        minHeight: '90vh',
        minWidth: '90vw',
      })

      this.searchLogHistoryDialogRef.content.instance.isSavedSearch =
        isSavedSearch
    })
  }

  #openDSstatus(
    actionTemplate: TemplateRef<unknown>,
    titleString: string
  ): void {
    import(
      '../../../history-log/distributed-search-status/distributed-search-status.component'
    ).then((comp) => {
      const SearchLogDsStatusComponent = comp.DistributedSearchStatusComponent // Assuming your component is the SearchLogDsStatusComponent export
      this.dsStatusDialogRef = this.dialogService.open({
        title: titleString,
        content: SearchLogDsStatusComponent,
        actions: actionTemplate,
        maxHeight: '90vh',
        maxWidth: '90vw',
        minHeight: '90vh',
        minWidth: '90vw',
      })
    })
  }

  public onMenuClick(e: any, dialogActions: TemplateRef<unknown>): void {
    if (e.id === 1) {
      this.#openSearchLogHistory(dialogActions, 'Search Log History', false)
    } else if (e.id === 2) {
      this.#openSearchLogHistory(dialogActions, 'Saved Search', true)
    } else if (e.id === 3) {
      this.#openDSstatus(dialogActions, 'Distributed Search Status')
    }
  }

  public close(): void {
    this.searchLogHistoryDialogRef?.close()
    this.dsStatusDialogRef?.close()
  }
}
