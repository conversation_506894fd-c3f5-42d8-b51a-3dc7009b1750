import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridConfig } from './grid-config.model'
import {
  ExcelModule,
  GridComponent,
  GridModule,
  PDFModule,
} from '@progress/kendo-angular-grid'
import { DynamicHeightDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-reusable-grid',
  standalone: true,
  imports: [
    CommonModule,
    GridModule,
    PDFModule,
    ExcelModule,
    DynamicHeightDirective,
  ],
  templateUrl: './reusable-grid.component.html',
  styleUrl: './reusable-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReusableGridComponent {
  @ViewChild(GridComponent, { static: true })
  public grid: GridComponent

  @Input()
  public container: HTMLDivElement

  @Input({ required: true })
  public config: GridConfig

  @Input({ required: true })
  public data: any[]
}
