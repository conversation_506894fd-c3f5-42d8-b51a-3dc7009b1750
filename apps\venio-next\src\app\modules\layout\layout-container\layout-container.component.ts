import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnD<PERSON>roy,
  OnInit,
  signal,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { LayoutDrawerComponent } from '../layout-drawer/layout-drawer.component'
import { LayoutToolbarComponent } from '../layout-toolbar/layout-toolbar.component'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  EMPTY,
  filter,
  interval,
  map,
  merge,
  of,
  startWith,
  Subject,
  switchMap,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs'
import {
  BatchModel,
  CaseInfoFacade,
  CompositeLayoutFacade,
  CompositeLayoutState,
  FieldFacade,
  ReviewBreadCrumb,
  ReviewsetFacade,
  ReviewSetStateService,
  SearchFacade,
  SearchInputParams,
  SearchService,
  StartupsFacade,
  ViewFacade,
  ViewModel,
  ViewTagRuleConflictModel,
} from '@venio/data-access/review'
import {
  AppIdentitiesTypes,
  IframeMessengerFacade,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { ActivatedRoute, Router, UrlSegment } from '@angular/router'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import {
  ConditionElement,
  ConditionGroup,
  ConditionType,
  GroupStackType,
  ResponseModel,
  UserModel,
} from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'
import {
  ModuleLoginFacade,
  ModuleLoginStateService,
  UserFacade,
  ValidatorService,
} from '@venio/data-access/common'
import {
  DocumentTagService,
  ReviewPanelViewState,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'
import { ConfirmationDialogService } from '../../../services/confirmation-dialog-service'
import { HttpErrorResponse } from '@angular/common/http'
import { VenioNotificationService } from '@venio/feature/notification'

// Only scope to `LayoutContainerComponent` component. The enum is used to determine the source of the search trigger
// from the parent window (micro frontend) which then determines the breadcrumb stack type.
enum SearchTriggerSourceTypes {
  // The search is triggered from the analyze page.
  ANALYZE = 'Analyze Search',
  // The search is triggered from the file upload -> history panel.
  UPLOAD_FILE_HISTORY = 'File Upload History Search',
  // The search is triggered from the production page.
  PRODUCTION = 'Production Search',
  // The search is triggered from the document share page.
  DOCUMENT_SHARE = 'Document Share Search',
  // The search is triggered from the document review page.
  LAUNCHPAD = 'Launchpad Search',
  // The search is triggered from the admin page.
  ADMIN = 'Admin Search',
  DYNAMIC_FOLDER = 'Dynamic Folder',
}

@Component({
  selector: 'venio-layout-container',
  standalone: true,
  imports: [CommonModule, LayoutDrawerComponent, LayoutToolbarComponent],
  templateUrl: './layout-container.component.html',
  styleUrls: ['./layout-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutContainerComponent
  implements OnDestroy, OnInit, AfterViewInit
{
  private readonly toDestroy$ = new Subject<void>()

  private fieldFacade = inject(FieldFacade)

  private searchFacade = inject(SearchFacade)

  private searchService = inject(SearchService)

  private caseFacade = inject(CaseInfoFacade)

  private userFacade = inject(UserFacade)

  private reviewPanelViewState = inject(ReviewPanelViewState)

  private reviewsetFacade = inject(ReviewsetFacade)

  private validatorService = inject(ValidatorService)

  private confirmationDialogService = inject(ConfirmationDialogService)

  private iframeMessengerFacade = inject(IframeMessengerFacade)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private viewFacade = inject(ViewFacade)

  private startupFacade = inject(StartupsFacade)

  private activatedRoute = inject(ActivatedRoute)

  private documentTagService = inject(DocumentTagService)

  private breadcrumbFacade = inject(BreadcrumbFacade)

  private utilityPanelFacade = inject(UtilityPanelFacade)

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  private reviewsetState = inject(ReviewSetStateService)

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  private moduleLoginState = inject(ModuleLoginStateService)

  private moduleLoginFacade = inject(ModuleLoginFacade)

  private refreshInterval$ = new Subject<void>()
  private readonly viewContainerRef = inject(ViewContainerRef)
  private router = inject(Router)

  /**
   * Listens for layout change messages from the iframe messenger.
   * Upon receiving a message,
   * it triggers a change detection cycle and sets the toolbar visibility to false.
   *
   * This method is used to adapt the UI in response to layout changes communicated from the
   * parent window, specifically to hide the toolbar when such a message is received.
   */
  public readonly hideToolbar = toSignal(
    this.iframeMessengerFacade
      .selectIframeMessengerContent$(MessageType.UI_STATE_CHANGE)
      .pipe(
        filter(
          (mc) =>
            (Array.isArray(mc) && mc?.[0]?.content['toolbar']) ||
            (mc as MessageContent)?.content['toolbar']
        ),
        map((mc) =>
          Array.isArray(mc)
            ? mc?.[0]?.content['toolbar'] === 'hide'
            : mc?.content['toolbar'] === 'hide'
        )
      ),
    {
      initialValue: false,
    }
  )

  /**
   * Only necessary until the legacy app depends on the micro app.
   * Once everything is migrated to the micro app, this and `hideToolbar` can be removed.
   */
  public readonly isToolbarVisible = computed(() => !this.hideToolbar())

  private currentSearchFormData: SearchInputParams

  private searchDebounceTimeMs = 200

  private isSwitchingProject = false

  private currentUser = signal<UserModel>({} as UserModel)

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private get docShareToken(): string {
    return (this.activatedRoute.snapshot.queryParams['docShareToken'] ||
      '') as string
  }

  private get reviewSetId(): number {
    return +this.reviewsetState.reviewSetId()
  }

  private get isReviewPage(): boolean {
    const urlSegments = this.#getUrlSegments()
    return (
      urlSegments[0].path.toLowerCase() === 'documents' ||
      urlSegments[0].path.toLowerCase() === 'document-detail'
    )
  }

  private breadcrumbStack = toSignal(
    this.breadcrumbFacade.selectBreadcrumbStack$
  )

  private completeBreadcrumbSyntax = toSignal(
    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
  )

  private readonly isUserDefaultViewLoading = toSignal(
    this.viewFacade.selectIsUserDefaultViewLoading$.pipe(
      filter((loading) => typeof loading !== 'undefined')
    )
  )

  private readonly userDefaultView = toSignal(
    this.viewFacade.selectUserDefaultView$.pipe(
      filter(
        (loading) =>
          typeof loading !== 'undefined' && !this.isUserDefaultViewLoading()
      )
    )
  )

  public ngOnInit(): void {
    this.#notifyUiLayoutReady()
    this.#selectCurrentUser()
    this.#initialActions()
    this.#handleBreadcrumbChange()
    this.#selectSearchFormValues()
    this.#handleProjectIdQueryParamChange()
    this.#handleBatchCheckoutComplete()
    this.moduleLoginState.updateProjectId(this.projectId)
    this.#selectSearchTrigger()
  }

  public ngAfterViewInit(): void {
    this.#handleEventsForSearch()
    this.#handleQueryParams()
    this.setupModuleLoginPeriodicUpdates()
  }

  #getUrlSegments(): UrlSegment[] {
    return this.router.parseUrl(this.router.url)?.root?.children['primary']
      ?.segments
  }

  #handleQueryParams(): void {
    this.activatedRoute.queryParams
      .pipe(
        switchMap((params) => {
          if (params.reviewSetId) {
            this.refreshInterval$.next()
            this.reviewsetState.reviewSetId.set(params.reviewSetId)
            const moduleLoginAction$ =
              this.moduleLoginState.moduleLoginId() &&
              params.reviewSetId ===
                this.moduleLoginState.previousReviewSetId()?.toString()
                ? this.moduleLoginFacade.updateExistingModuleLogin(
                    params.projectId,
                    params.reviewSetId
                  )
                : this.moduleLoginFacade.createNewModuleLogin(
                    params.projectId,
                    params.reviewSetId
                  )

            return moduleLoginAction$.pipe(
              switchMap(() => {
                this.moduleLoginState.updatePreviousReviewSetId(
                  params.reviewSetId
                )
                return this.reviewsetFacade.fetchReviewSetBasicInfo$(
                  params.projectId,
                  params.reviewSetId
                )
              })
            )
          } else {
            this.reviewsetState.reset()
            return of({
              message: '',
              status: 'Success',
              data: { projectId: params.projectId, reviewSetId: -1 },
            } as ResponseModel)
          }
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        if (response?.data?.reviewSetId > 0) {
          this.reviewsetState.reviewSetBasicInfo.set(response.data)
        }
        this.viewFacade.fetchUserDefaultView(this.projectId)
      })
  }

  private setupModuleLoginPeriodicUpdates(): void {
    interval(1 * 60 * 1000)
      .pipe(
        startWith(0),
        filter(
          () =>
            this.reviewsetState.isBatchReview() &&
            Boolean(this.moduleLoginState.moduleLoginId()) &&
            this.moduleLoginState.moduleLoginId() > 0
        ),
        switchMap(() =>
          this.moduleLoginFacade.updateExistingModuleLogin(
            this.projectId,
            this.reviewsetState.reviewSetId()
          )
        ),
        catchError((err: unknown) => {
          console.error('Periodic update failed', err)
          return of(null)
        }),
        takeUntil(merge(this.toDestroy$, this.refreshInterval$))
      )
      .subscribe()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    // When we navigate away from the main app to parent app, we reset the search form values.
    // This is to ensure that when we come back to the main app, the search form values are reset.
    // There might be a scenario where form values are reset within the main app which may change in future.
    this.#resetSearchFormValues()
  }

  #handleProjectIdQueryParamChange(): void {
    this.activatedRoute.queryParams
      .pipe(
        filter((q) => Boolean(q['projectId'])),
        distinctUntilChanged((a, b) => a['projectId'] === b['projectId']),
        takeUntil(this.toDestroy$)
      )
      .subscribe((queryParams) => {
        this.isSwitchingProject = true

        // When the project ID changes, we reset the default view state and fetch another project data.
        this.#resetViewState()
        this.#resetFieldState()
        this.#resetDocuemntTagSelection()

        // using setTimeout because of debounce timer on handleEventsForSearch().
        setTimeout(() => {
          this.#resetLayoutDetails()
          this.#initialProjectActions()
          this.#fetchReviewPanelDetails()
        }, this.searchDebounceTimeMs)
      })
  }

  private notificationFacade = inject(VenioNotificationService)

  #handleBatchCheckoutComplete(): void {
    merge(
      this.searchFacade.getReviewSetBatchId$,
      this.reviewsetFacade.checkBatchReviewCompletedAction$
    )
      .pipe(
        filter((batchId) => this.isReviewPage && batchId > 0),
        map((batchId) => batchId),
        switchMap((batchId: number) => {
          this.reviewsetState.batchId.set(batchId)
          return this.reviewsetFacade.fetchReviewSetBatchInfo$(
            this.projectId,
            this.reviewSetId,
            batchId
          )
        }),
        switchMap((response: ResponseModel) => {
          const batchInfo: BatchModel = response?.data
          this.reviewsetState.reviewsetBatchInfo.set(batchInfo)
          if (batchInfo.remainingFiles === 0) {
            const message =
              'Review of all documents in batch is completed. Do you want to check in and open new batch?'
            return this.confirmationDialogService
              .showConfirmationDialog(
                'Check In',
                message,
                this.viewContainerRef
              )
              .pipe(
                filter((confirmed) => confirmed),
                switchMap(() => {
                  return this.reviewsetFacade
                    .checkInReviewBatch$(
                      this.projectId,
                      this.reviewSetId,
                      batchInfo.batchId
                    )
                    .pipe(
                      filter((response) => Boolean(response)),
                      switchMap((response: ResponseModel) => {
                        this.notificationFacade.showSuccess(response.message)
                        this.reviewsetFacade.checkoutBatchReviewSetAction.next(
                          this.reviewSetId
                        )
                        return of(this.reviewSetId)
                      })
                    )
                }),
                catchError((err: unknown) => {
                  const httpError = err as HttpErrorResponse
                  this.notificationFacade.showError(httpError.error.message)
                  return EMPTY
                })
              )
          }
        }),

        takeUntil(this.toDestroy$)
      )
      .subscribe()
  }

  /**
   * Reset user default view state and default expression.
   * @returns {void}
   */
  #resetViewState(): void {
    this.viewFacade.resetView([
      'userDefaultView',
      'selectedViewDefaultExpression',
    ])
  }

  #resetDocuemntTagSelection(): void {
    this.reviewPanelViewState.resetDocuemntTagSelection()
  }

  #resetSearchFormValues(): void {
    this.searchFacade.resetSearchState([
      'searchFormValues',
      'shouldResetSearchInputControls',
    ])
  }

  #resetFieldState(): void {
    this.fieldFacade.resetField([
      'allCustomFields',
      'fieldPanelMap',
      'venioFieldsPanelMap',
      'customFieldsPanelMap',
    ])
  }

  /**
   * Performs initial data fetching actions when the component or a specific process is initialized.
   * This method is responsible for fetching all Venio fields, user rights related to the project,
   * and the user's default view for the given project.
   * It's typically invoked during the
   * initialization phase of a component or a service.
   *
   * The method does not return any values but triggers asynchronous operations that
   * likely update the application's state or UI once the data is fetched.
   * @returns {void}
   */
  #initialActions(): void {
    if (!this.isReviewPage) return
    this.fieldFacade.fetchAllVenioFields()
    this.fieldFacade.fetchReviewPanelDefaltFields()
    this.#initialProjectActions()
  }

  #initialProjectActions(): void {
    if (!this.projectId && !this.isReviewPage) return

    this.#fetchDefaultUserLayout()
    this.fieldFacade.fetchAllPermittedFields(this.projectId)
    this.fieldFacade.fetchAllCustomWithPredefinedFields(this.projectId)
    this.fieldFacade.fetchAllCustomFields(this.projectId, true)
    this.startupFacade.fetchUserRights(this.projectId, this.docShareToken)
    this.startupFacade.fetchDefaultGroups(this.projectId)
  }

  #fetchReviewPanelDetails(): void {
    if (this.currentUser()?.userId)
      this.fieldFacade.fetchUserLayoutId(this.currentUser()?.userId)
  }

  #fetchDefaultUserLayout(): void {
    this.layoutFacade
      .fetchDefaultLayout$(this.projectId)
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((response: ResponseModel) => {
        if (!this.layoutState.userSelectedLayout()) {
          this.layoutState.userSelectedLayout.set(response?.data)
        }
      })
  }

  /**
   * Constructs and performs a search query based on different sets of breadcrumbs: main, conditional, and filter.
   * Each set of breadcrumbs is transformed into a query segment, and these segments are combined
   * to form a final search query.
   * The search is then executed if the final query is not empty.
   *
   * @param {ReviewBreadCrumb[]} mainBreadcrumbs - The main set of breadcrumbs for the search.
   * @param {ReviewBreadCrumb[]} conditionalBreadcrumbs - The conditional breadcrumbs for additional query criteria.
   * @param {ReviewBreadCrumb[]} filterBreadcrumbs - The filter breadcrumbs for further refining the search.
   * @returns {void}
   */
  #performBreadCrumbSearch(
    mainBreadcrumbs: ReviewBreadCrumb[],
    conditionalBreadcrumbs: ReviewBreadCrumb[],
    filterBreadcrumbs: ReviewBreadCrumb[]
  ): void {
    const mainQuery =
      this.searchService.generateQueryFromBreadcrumbs(mainBreadcrumbs)
    const conditionQuery = this.searchService.generateQueryFromBreadcrumbs(
      conditionalBreadcrumbs
    )
    const filterQuery =
      this.searchService.generateQueryFromBreadcrumbs(filterBreadcrumbs)
    const finalQuery = [mainQuery, conditionQuery, filterQuery]
      .filter((q) => q.length > 0)
      .join(' AND ')
    if (finalQuery.length > 0) {
      this.searchFacade.search({
        searchExpression: finalQuery,
        isResetBaseGuid: true,
        searchDuplicateOption:
          this.currentSearchFormData?.searchDuplicateOption,
        includePC: this.currentSearchFormData?.includePC,
      })
    }
  }

  #resetLayoutDetails(): void {
    this.layoutState.userLayouts.set([])
    this.layoutState.userSelectedLayout.set(null)
    this.utilityPanelFacade.resetUtilityPanelState([
      'expandedStatus',
      'visibilityStatus',
      'panelSortOrder',
    ])
  }

  /**
   * Subscribes to changes in main, conditional, and filter search breadcrumbs.
   * Upon any changes in these breadcrumbs, it triggers the breadcrumb search process by calling
   * `#performBreadCrumbSearch` method with the updated breadcrumbs.
   *
   * This method is responsible for reacting to breadcrumb changes and ensuring that
   * the search queries are updated and executed accordingly.
   * @returns {void}
   */
  #handleBreadcrumbChange(): void {
    combineLatest([
      this.searchFacade.getMainSearchBreadcrumbs$,
      this.searchFacade.getConditionSearchBreadcrumbs$,
      this.searchFacade.getFilterSearchBreadcrumbs$,
    ])
      .pipe(
        filter(() => this.isReviewPage),
        takeUntil(this.toDestroy$)
      )
      .subscribe(
        ([mainBreadcrumbs, conditionalBreadcrumbs, filterBreadcrumbs]) => {
          this.#performBreadCrumbSearch(
            mainBreadcrumbs,
            conditionalBreadcrumbs,
            filterBreadcrumbs
          )
        }
      )
  }

  /**
   * Performs a search operation using a given search expression.
   * @param {string} searchExpression - The expression used for the search.
   * @returns {void}
   */
  #performSearch(searchExpression: string, reviewSetId = -1): void {
    const viewTagConflictData: ViewTagRuleConflictModel =
      this.documentTagService.getTagRuleData()

    this.searchFacade.search({
      searchExpression,
      isResetBaseGuid: true,
      searchDuplicateOption: this.currentSearchFormData?.searchDuplicateOption,
      includePC: this.currentSearchFormData?.includePC,
      viewTagRuleConflictFiles: viewTagConflictData,
      reviewSetId: reviewSetId,
    })
  }

  /**
   * Handles search-related events by responding to changes in search input and user view settings.
   * This method combines multiple observables to track changes in the search input (from an iframe messenger),
   * the user's default view settings, and the loading state of these settings.
   *
   * It follows a reactive programming approach using RxJS operators to process these streams.
   * The method ensures efficient and optimized handling of search operations by debouncing input events,
   * filtering irrelevant updates, and maintaining distinctness in the observable sequence.
   *
   * Once the conditions are met (valid search term and view settings are not loading),
   * it initiates a search operation by calling `this.#performSearch` with the updated view expression.
   *
   * The use of `takeUntil` with `this.toDestroy$` ensures proper subscription management,
   * preventing potential memory leaks and ensuring resource cleanup on component destruction.
   * @returns {void}
   */
  #handleEventsForSearch(): void {
    let isSearchPerformed = false
    let pendingOutsourceEmission: MessageContent | null = null
    let hasViewBeenProcessed = false // New flag to track if view has been processed

    merge(
      this.iframeMessengerFacade
        .selectIframeMessengerContent$(MessageType.SEARCH_CHANGE)
        .pipe(
          filter((mc) => this.isReviewPage && Boolean(mc)),
          map((mc) => ({ src: 'outsource', value: mc as MessageContent })),
          tap((emission) => {
            pendingOutsourceEmission = emission.value
          })
        ),
      this.viewFacade.selectUserDefaultView$.pipe(
        filter((vm) => this.isReviewPage && Boolean(vm)),
        map((vm) => ({ src: 'view', value: vm })),
        distinctUntilChanged(),
        debounceTime(this.searchDebounceTimeMs)
      ),
      this.reviewsetFacade.checkoutBatchReviewSetAction.pipe(
        filter(() => this.isReviewPage),
        map((reviewSetId) => ({ src: 'reviewset', value: reviewSetId })),
        distinctUntilChanged()
      )
    )
      .pipe(
        filter((emission: any) => {
          // Skip outsource emission if view has not been fetched
          if (emission.src === 'outsource' && !hasViewBeenProcessed) {
            return false
          }

          return (
            emission.src === 'outsource' ||
            emission.src === 'reviewset' ||
            (emission.src === 'view' &&
              !isSearchPerformed &&
              !hasViewBeenProcessed) ||
            this.viewFacade.isViewManuallyChanged() ||
            this.caseFacade.isProjectManuallyChanged()
          )
        }),
        tap(() => {
          isSearchPerformed = true
          this.viewFacade.isViewManuallyChanged.set(false)
          this.caseFacade.isProjectManuallyChanged.set(false)
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((emission) => {
        this.isSwitchingProject = false

        if (emission.value) {
          if (emission.src === 'outsource') {
            //if outsource emission occurs first then wait for certain time and handle outsource emission and perform search
            setTimeout(() => {
              if (this.userDefaultView()) {
                this.handleOutsourceEmission(emission.value as MessageContent)
                this.#performSearch(this.completeBreadcrumbSyntax())
                pendingOutsourceEmission = null
              }
            }, 100)
          } else if (emission.src === 'view' && !pendingOutsourceEmission) {
            if (this.reviewsetState.isBatchReview())
              this.#performSearch('', this.reviewsetState.reviewSetId())
            else {
              this.handleViewEmission(emission.value as ViewModel)
              this.#performSearch(this.completeBreadcrumbSyntax())
            }
            hasViewBeenProcessed = true
          }
          //if emission is from view and there is outsource emission already then perform search with outsource expression
          else if (emission.src === 'view' && pendingOutsourceEmission) {
            if (this.reviewsetState.isBatchReview())
              this.#performSearch('', this.reviewsetState.reviewSetId())
            else {
              this.handleOutsourceEmission(pendingOutsourceEmission)
              this.#performSearch(this.completeBreadcrumbSyntax())
            }
            pendingOutsourceEmission = null
            hasViewBeenProcessed = true
          } else if (emission.src === 'reviewset') {
            this.#performSearch('', emission.value as number)
          }
        }
      })
  }

  private handleOutsourceEmission(mc: MessageContent): void {
    let searchTriggerSource = mc.content?.[
      'searchTriggerSource'
    ] as SearchTriggerSourceTypes
    const isAdvancedSearch =
      mc.content?.['isAdvancedSearch'] && mc.content?.['searchTerm']?.trim()

    const isDynamicFolderEdit =
      mc?.content?.['selectedCommandEvent']?.['selectedFolder']

    // Extract searchDuplicateOption and includePC from message content
    const searchDuplicateOption = mc.content?.['searchDuplicateOption']
    const includePC = mc.content?.['includeFamily']

    // Update search facade with these values if they exist
    if (searchDuplicateOption !== undefined) {
      this.searchFacade.setSearchDupOption(searchDuplicateOption)
    }

    if (includePC !== undefined) {
      this.searchFacade.setIncludePC(includePC)
    }

    if (isDynamicFolderEdit)
      searchTriggerSource = SearchTriggerSourceTypes.DYNAMIC_FOLDER
    const groupStackType = this.determineGroupStackType(
      searchTriggerSource,
      isAdvancedSearch && !isDynamicFolderEdit
    )

    if (isAdvancedSearch)
      this.breadcrumbFacade.resetBreadcrumbState('breadcrumbStacks')

    const payload = this.createBreadcrumbPayload(
      mc.content['searchTerm'],
      groupStackType
    )
    const viewPayload = this.createBreadcrumbPayload(
      this.userDefaultView().viewExpression,
      GroupStackType.VIEW_SEARCH
    )

    this.updateOrStoreBreadcrumb(viewPayload)
    this.breadcrumbFacade.storeBreadcrumbs([payload])
  }

  private handleViewEmission(viewModel: ViewModel): void {
    const payload = this.createBreadcrumbPayload(
      viewModel.viewExpression,
      GroupStackType.VIEW_SEARCH
    )
    this.updateOrStoreBreadcrumb(payload)
  }

  private determineGroupStackType(
    searchTriggerSource: SearchTriggerSourceTypes,
    isAdvancedSearch: boolean
  ): GroupStackType {
    if (isAdvancedSearch) return GroupStackType.ADVANCED_SEARCH
    switch (searchTriggerSource) {
      case SearchTriggerSourceTypes.ANALYZE:
        return GroupStackType.ANALYZE_SEARCH
      case SearchTriggerSourceTypes.UPLOAD_FILE_HISTORY:
        return GroupStackType.FILE_UPLOAD_HISTORY_SEARCH
      case SearchTriggerSourceTypes.PRODUCTION:
        return GroupStackType.PRODUCTION_SEARCH
      case SearchTriggerSourceTypes.DYNAMIC_FOLDER:
        return GroupStackType.DYNAMIC_FOLDER
      case SearchTriggerSourceTypes.ADMIN:
        return GroupStackType.ADMIN
      case SearchTriggerSourceTypes.DOCUMENT_SHARE:
        return GroupStackType.DOCUMENT_SHARE_SEARCH
      default:
        return GroupStackType.VIEW_SEARCH
    }
  }

  private createBreadcrumbPayload(
    conditionSyntax: string,
    groupStackType: GroupStackType
  ): ConditionGroup {
    return {
      id: UuidGenerator.uuid,
      groupStackType,
      checked: true,
      conditionType: ConditionType.Group,
      conditions: [{ conditionSyntax }] as ConditionElement[],
    }
  }

  private updateOrStoreBreadcrumb(payload: ConditionGroup): void {
    if (this.breadcrumbStack()?.length > 0) {
      this.breadcrumbFacade.updateBreadcrumb(payload)
    } else {
      this.breadcrumbFacade.storeBreadcrumbs([payload])
    }
  }

  #selectSearchFormValues(): void {
    this.searchFacade.selectSearchFormValues$
      .pipe(
        filter(
          (searchFormData) => this.isReviewPage && Boolean(searchFormData)
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((searchFormData) => {
        this.currentSearchFormData = searchFormData
      })
  }

  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserSuccessResponse$
      .pipe(
        filter((success) => Boolean(success)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        this.currentUser.set(success?.data || {})
        this.#fetchReviewPanelDetails()
      })
  }

  /**
   * Notify the parent app which loads this app as a micro frontend that the layout is ready.
   * The parent app then triggers events for operations to be performed on the layout.
   * @returns {void}
   */
  #notifyUiLayoutReady(): void {
    this.iframeMessengerService.sendMessage({
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.LAYOUT_CHANGE,
        content: {
          layoutReady: true,
        },
      } as MessageContent,
    })
  }

  /**
   * Triggers search from other component(deleted/moved/replace field) to refresh document list
   * @returns {void}
   */
  #selectSearchTrigger(): void {
    this.searchFacade.triggerSearch$
      .pipe(
        debounceTime(50),
        filter((search) => Boolean(search) && this.isReviewPage),
        withLatestFrom(this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$),

        takeUntil(this.toDestroy$)
      )
      .subscribe(([triggerSearch, syntax]) => {
        const searchSyntax = syntax || 'FileId>0'
        this.#performSearch(searchSyntax, this.reviewsetState.reviewSetId())
      })
  }
}
