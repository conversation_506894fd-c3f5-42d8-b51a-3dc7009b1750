<ng-container [formGroup]="searchFormGroup">
  <button
    #searchDuplicateButton
    kendoButton
    kendoTooltip
    title="Search Duplicate Options"
    kendoPopoverAnchor
    [popover]="dedupeOption"
    fillMode="outline"
    class="t-min-h-[2.25rem] t-relative hover:!t-bg-[#1EBADC] enabled:hover:!t-border-[#1EBADC] enabled:hover:!t-text-[#FFFFFF]"
    size="none">
    <div class="t-flex t-px-2 t-relative">
      <span
        class="t-w-full v-invert-btn-color"
        venioSvgLoader
        [parentElement]="searchDuplicateButton.element"
        svgUrl="assets/svg/icon-tagedit-copy.svg"
        height="1rem"
        hoverColor="#FFFFFF"
        width="1rem">
        <kendo-loader size="small"></kendo-loader>
      </span>
      <kendo-svg-icon [icon]="chevronDown"></kendo-svg-icon>
    </div>
  </button>
  <kendo-popover
    [animation]="true"
    [callout]="false"
    #dedupeOption
    position="bottom">
    <ng-template kendoPopoverTitleTemplate>
      <div class="t-text-primary t-mx-[-0.625rem]">DUPLICATE OPTION</div>
    </ng-template>
    <ng-template kendoPopoverBodyTemplate>
      <div class="t-flex t-flex-col t-whitespace-nowrap t-gap-2 t-px-1 t-pb-2">
        <ng-container
          *ngFor="let dupOption of searchDuplicateOptionCollections">
          <label
            *venioHasUserGroupRights="
              defatulSearchDupOption === dupOption.value
                ? []
                : dupOption.allowedPermission
            "
            [ngClass]="{
              't-text-primary t-font-medium':
                searchFormGroup.get('searchDuplicateOption')?.value ===
                dupOption.value
            }">
            <input
              formControlName="searchDuplicateOption"
              [value]="dupOption.value"
              type="radio"
              kendoRadioButton
              size="small" />
            {{ dupOption.text }}
          </label>
        </ng-container>
      </div>
    </ng-template>
  </kendo-popover>
</ng-container>
