export enum DocumentMenuType {
  EDIT = 'Edit Tags/Coding',
  SEND = 'Send',
  SEND_REMOVE = 'Send/Remove Folder',
  REMOVE = 'Remove',
  MOVE = 'Move',
  DELETE = 'Delete',
  REPLACE = 'Replace',
  TALLY = 'Tally',
  EXPORT_TO_FILE = 'Export To File',
  CONVERT = 'Convert',
  PRODUCTION = 'Production',
  TAGS = 'Bulk Tags & Coding',
  NOTES = 'Notes',
  PRINT_DOWNLOAD = 'Print/Download',
  EMAIL_THREADING = 'Email Threading',
  FOLDERING = 'Foldering',
  SHARE_DOCUMENT = 'Share Document',
  SAVE_SEARCH = 'Save Search',
  SWITCH_VIEW = 'Switch to Email Thread View',
  TAG_ALL_INCLUSIVE_EMAIL = 'Tag All Inclusive Emails',
  TAG_WHOLE_THREAD = 'Tag Whole Thread',
  SHOW_INCLUSIVE_EMAIL = 'Show Inclusive Emails',
  TRANSCRIPT = 'Transcript',
  RESPONSIVE_PST = 'Responsive PST',
  BULK_REDACT = 'Bulk Redact',
  SEND_TO_EDAI = 'Review with eDiscovery AI',
  GENERATE_RSMF = 'Generate RSMF',
  ENTITY_EXTRACTION = 'Entity Extraction',
  SEND_TO_ANALYZE = 'Send to Analyze',
}
