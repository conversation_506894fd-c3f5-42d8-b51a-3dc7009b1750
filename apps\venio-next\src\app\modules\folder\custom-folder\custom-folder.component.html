<div class="content">
  <div class="t-flex t-w-full t-flex-col t-mt-3">
    <div
      *ngIf="customFolderErrorMessage"
      class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-3">
      <span>{{ customFolderErrorMessage }}</span>
      <button
        type="button"
        (click)="customFolderErrorMessage = null"
        class="t-border-0 t-cursor-pointer t-text-error">
        close
      </button>
    </div>
    <div class="t-flex t-mt-4 t-flex-wrap t-gap-3 v-custom-grey-bg">
      <ng-container [formGroup]="customFolderFormGroup">
        <!-- new ui -->
        <div class="t-flex t-basis-[32.5%] t-flex-col t-gap-1">
          <kendo-label
            for="DynamicName"
            class="t-text-xs t-uppercase t-tracking-widest">
            Name <span class="t-text-error">*</span>
          </kendo-label>

          <kendo-textbox
            placeholder="Folder Name"
            formControlName="folderName"
            #DynamicName></kendo-textbox>
        </div>

        <!-- end here -->
      </ng-container>

      <div class="t-flex t-basis-[32.5%] t-flex-col t-gap-1">
        <kendo-label
          for="Reviewer"
          class="t-text-xs t-uppercase t-tracking-widest">
          Parent Folder
        </kendo-label>

        <kendo-dropdownlist
          #Reviewer
          [data]="customFolders"
          textField="folderName"
          valueField="folderId"
          [(value)]="selectedCustomFolder">
          <ng-template kendoDropDownListItemTemplate let-dataItem>
            {{ dataItem.folderName }}
            <span *ngIf="dataItem.folderLineage !== ''"
              ><i
                ><small> ( {{ dataItem.folderLineage }} ) </small></i
              >
            </span>
          </ng-template>
        </kendo-dropdownlist>
      </div>
    </div>

    <ng-container [formGroup]="customFolderFormGroup">
      <div class="t-flex t-w-full t-flex-col t-pt-2 v-custom-grey-bg">
        <div class="t-flex t-flex-1 t-flex-col t-gap-1">
          <kendo-label
            for="dynamicNote"
            class="t-text-xs t-uppercase t-tracking-widest">
            Description
          </kendo-label>
          <kendo-textarea
            #dynamicNote
            placeholder="Folder Description"
            formControlName="folderDescription"
            [rows]="3"
            resizable="vertical"></kendo-textarea>
        </div>
      </div>
    </ng-container>

    <div class="t-flex t-mt-4 t-pb-3 t-text-primary t-font-semibold">
      Security
    </div>
    <div class="t-flex t-w-full">
      <kendo-grid class="!t-w-full" [resizable]="true" [data]="securityGroups">
        <kendo-grid-column
          field="groupName"
          headerClass="t-text-primary"
          title="Role"
          [width]="300">
        </kendo-grid-column>
        <kendo-grid-column
          field="permission"
          headerClass="t-text-primary"
          title="Permission">
          <ng-template kendoGridCellTemplate let-dataItem>
            <kendo-dropdownlist
              class="t-w-1/2"
              [data]="securityPermissionData"
              [valuePrimitive]="true"
              textField="text"
              valueField="value"
              [(ngModel)]="dataItem.permission">
            </kendo-dropdownlist>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>
</div>
