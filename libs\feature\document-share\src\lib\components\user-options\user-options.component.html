<form class="t-flex t-flex-row" [formGroup]="documentShareForm">
  <div class="t-flex t-flex-col t-w-1/3">
    <div class="t-flex t-items-end !t-font-medium t-py-2">
      Share To Internal Users
    </div>
    <!-- Internal user grid -->
    <div class="t-flex t-flex-col t-mt-1">
      <div
        class="v-search-box-container t-relative t-inline-block t-w-full t-mb-2 t-mt-[2px]">
        <kendo-textbox
          placeholder="Search For Internal Users"
          id="search_internal_user"
          (input)="onFilter($event)"></kendo-textbox>
      </div>
      <div
        (resize)="onContainerResize($event)"
        class="t-h-full"
        #treeListContainer>
        <kendo-grid
          class="t-flex t-flex-col-reverse t-overflow-y-auto"
          style="height: 12rem"
          [kendoGridBinding]="filteredInternalUsers"
          [kendoGridSelectBy]="internalUserSelectionKey"
          [(selectedKeys)]="selectedInternalUsers"
          data-qa="document-share-internal-users-grid"
          (selectionChange)="onInternalUserSelectionChange()"
          [selectable]="true">
          <kendo-grid-column title="#" [width]="40">
            <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
              {{ rowIndex + 1 }}
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-checkbox-column
            [showSelectAll]="true"
            [columnMenu]="false"
            [width]="40">
          </kendo-grid-checkbox-column>

          @if(!sharedDocData()?.isDocShareEdit){
          <kendo-grid-column
            field="email"
            title="User Email"
            headerClass="t-text-primary">
            <ng-template
              kendoGridFilterCellTemplate
              let-filter
              let-column="column">
              <kendo-grid-string-filter-cell
                class="t-h-4"
                [column]="column"
                [filter]="filter">
              </kendo-grid-string-filter-cell>
            </ng-template> </kendo-grid-column
          >}@else{
          <kendo-grid-column
            field="userName"
            title="User Name"
            headerClass="t-text-primary">
            <ng-template
              kendoGridFilterCellTemplate
              let-filter
              let-column="column">
              <kendo-grid-string-filter-cell
                class="t-h-4"
                [column]="column"
                [filter]="filter">
              </kendo-grid-string-filter-cell>
            </ng-template>
          </kendo-grid-column>
          }

          <ng-template kendoGridNoRecordsTemplate>
            No internal users.
          </ng-template>
        </kendo-grid>
      </div>
    </div>
  </div>
  <div
    class="v-dashed-sperator t-mx-[15px] t-opacity-100 t-w-[1px] t-h-auto"></div>
  <div class="t-flex t-flex-col t-w-2/5">
    <!-- Share to external user checkbox and input textbox -->
    <div
      class="t-flex t-items-center t-gap-2 t-px-1 t-pb-2"
      [ngClass]="{ 't-h-[54px]': sharedDocData()?.isDocShareEdit }">
      @if(!sharedDocData()?.isDocShareEdit){
      <input
        type="checkbox"
        formControlName="shareToExternalUsers"
        data-qa="document-share-share-to-externalusers-checkbox"
        kendoCheckBox
        #shareToExternalUsersLabel />
      <kendo-label
        [for]="shareToExternalUsersLabel"
        text="Share to External Users"
        [ngClass]="{
          't-text-primary t-font-medium':
            documentShareForm.get('shareToExternalUsers')?.value &&
            !shareToExternalUsersLabel.disabled,
          'k-disabled': invitationInProgress$ | async,
          '': (invitationInProgress$ | async) === false
        }"
        data-qa="document-share-share-to-externalusers-label"></kendo-label>
      <kendo-textbox
        formControlName="newEmail"
        class="t-w-1/2"
        placeholder="Add New User Email"
        data-qa="document-share-new-email-input">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            class="t-h-full !t-px-1"
            fillMode="outline"
            size="small"
            [disabled]="!shareToExternalUsers"
            (click)="addExternalUser()"
            [svgIcon]="plusIcon"
            data-qa="document-share-add-new-email-button"
            kendoButton></button>
        </ng-template> </kendo-textbox
      >}
    </div>

    <div
      class="v-search-box-container t-relative t-inline-block t-w-full t-mb-2 t-mt-[1px]">
      <kendo-textbox
        placeholder="Search For External Users"
        id="search_external_user"
        [disabled]="
          (!shareToExternalUsers && !sharedDocData()?.isDocShareEdit) ||
          (invitationInProgress$ | async)
        "
        (input)="onFilterExternalUser($event)"></kendo-textbox>
    </div>
    <!-- External user grid -->
    <div
      (resize)="onContainerResize($event)"
      class="t-h-full"
      #treeListContainer>
      <kendo-grid
        [kendoGridBinding]="filteredExternalUsers"
        [kendoGridSelectBy]="externalUserSelectionKey"
        [(selectedKeys)]="selectedExternalUsers"
        class="t-flex t-flex-col-reverse t-overflow-y-auto"
        style="height: 12rem"
        data-qa="document-share-external-users-grid"
        [ngClass]="{
          'k-disabled t-opacity-50':
            (!shareToExternalUsers && !sharedDocData()?.isDocShareEdit) ||
            (invitationInProgress$ | async),
          't-opacity-100':
            shareToExternalUsers || (invitationInProgress$ | async) === false
        }"
        (selectionChange)="onExternalUserSelectionChange()"
        [selectable]="true">
        <kendo-grid-column title="#" [width]="40">
          <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
            {{ rowIndex + 1 }}
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [columnMenu]="false"
          [width]="40">
        </kendo-grid-checkbox-column>

        @if(!sharedDocData()?.isDocShareEdit){
        <kendo-grid-column
          field="email"
          title="User Email"
          headerClass="t-text-primary">
          <ng-template
            kendoGridFilterCellTemplate
            let-filter
            let-column="column">
            <kendo-grid-string-filter-cell
              class="t-h-4"
              [column]="column"
              [filter]="filter">
            </kendo-grid-string-filter-cell>
          </ng-template> </kendo-grid-column
        >}@else{
        <kendo-grid-column
          field="userName"
          title="User Name"
          headerClass="t-text-primary">
          <ng-template
            kendoGridFilterCellTemplate
            let-filter
            let-column="column">
            <kendo-grid-string-filter-cell
              class="t-h-4"
              [column]="column"
              [filter]="filter">
            </kendo-grid-string-filter-cell>
          </ng-template> </kendo-grid-column
        >}

        <ng-template kendoGridNoRecordsTemplate>
          No external users.
        </ng-template>
      </kendo-grid>
    </div>
  </div>
  <div
    class="v-dashed-sperator t-mx-[15px] t-opacity-100 t-w-[1px] t-h-auto"></div>

  <div class="t-flex t-flex-col t-w-1/4">
    <!-- External user permission -->
    <div class="t-flex t-flex-col t-gap-1">
      <div class="t-flex t-pb-1">
        <kendo-label
          class="!t-font-medium t-mt-2"
          [ngClass]="{
            'k-disabled': !shareToExternalUsers,
            '': shareToExternalUsers
          }"
          text="Permission for external users"></kendo-label>
      </div>
      <div class="t-flex t-pb-1">
        <input
          type="checkbox"
          formControlName="allowToAddNotes"
          class="t-mr-2"
          data-qa="document-share-allow-adding-notes-checkbox"
          [checked]="documentShareForm.get('allowToAddNotes')?.value"
          kendoCheckBox
          #allowAddingNotesCheckbox />
        <kendo-label
          class="flex items-center"
          [for]="allowAddingNotesCheckbox"
          data-qa="document-share-allow-adding-notes-label"
          text="Create document notes visible to all users"
          [ngClass]="{
            't-text-primary t-font-medium':
              documentShareForm.get('allowToAddNotes')?.value &&
              !allowAddingNotesCheckbox.disabled,
            'k-disabled': allowAddingNotesCheckbox.disabled,
            '': !allowAddingNotesCheckbox.disabled
          }"></kendo-label>
      </div>
      <div class="t-flex t-pb-1">
        <input
          type="checkbox"
          formControlName="allowToTag"
          class="t-mr-2"
          data-qa="document-share-allow-tagging-checkbox"
          [checked]="documentShareForm.get('allowToTag')?.value"
          kendoCheckBox
          #allowTaggingCheckbox />
        <kendo-label
          class="flex items-center"
          [for]="allowTaggingCheckbox"
          text="Allow Tag/Untag"
          [ngClass]="{
            't-text-primary t-font-medium':
              documentShareForm.get('allowToTag')?.value &&
              !allowTaggingCheckbox.disabled,
            'k-disabled': allowTaggingCheckbox.disabled,
            '': !allowTaggingCheckbox.disabled
          }"
          data-qa="document-share-allow-tagging-label"></kendo-label>
        <div kendoTooltip>
          <span
            class="t-inline-block t-ml-1 t-m-0.5 !t-w-4 !t-h-4"
            [svgUrl]="infoSvgUrl"
            venioSvgLoader
            data-qa="document-share-allow-tagging-tooltip"
            title="To tag/untag, provide permission of tag(s) from Tag Management."></span>
        </div>
      </div>
      <div class="t-flex t-pb-1">
        <input
          type="checkbox"
          formControlName="allowRedaction"
          class="t-mr-2"
          data-qa="document-share-allow-redaction-checkbox"
          [checked]="documentShareForm.get('allowRedaction')?.value"
          kendoCheckBox
          #allowRedactionCheckbox />
        <kendo-label
          class="flex items-center"
          [for]="allowRedactionCheckbox"
          text="Allow Redaction"
          data-qa="document-share-allow-redaction-label"
          [ngClass]="{
            't-text-primary t-font-medium':
              documentShareForm.get('allowRedaction')?.value &&
              !allowRedactionCheckbox.disabled,
            'k-disabled': allowRedactionCheckbox.disabled,
            '': !allowRedactionCheckbox.disabled
          }"></kendo-label>
      </div>
      <div class="t-flex t-pb-1">
        <input
          type="checkbox"
          formControlName="allowToViewAnalyzePage"
          class="t-mr-2"
          data-qa="document-share-allow-analyze-page-checkbox"
          [checked]="documentShareForm.get('allowToViewAnalyzePage')?.value"
          kendoCheckBox
          #allowAnalyzePageCheckbox />
        <kendo-label
          class="flex items-center"
          [for]="allowAnalyzePageCheckbox"
          text="View Analyze Page"
          data-qa="document-share-view-analyze-page-label"
          [ngClass]="{
            't-text-primary t-font-medium':
              documentShareForm.get('allowToViewAnalyzePage')?.value &&
              !allowAnalyzePageCheckbox.disabled,
            'k-disabled': allowAnalyzePageCheckbox.disabled,
            '': !allowAnalyzePageCheckbox.disabled
          }"></kendo-label>
      </div>
    </div>
  </div>
</form>
