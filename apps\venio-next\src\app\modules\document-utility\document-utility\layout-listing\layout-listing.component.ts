import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DateInputsModule } from '@progress/kendo-angular-dateinputs'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ProgressBarModule } from '@progress/kendo-angular-progressbar'
import { TooltipModule, PopoverModule } from '@progress/kendo-angular-tooltip'
import {
  SvgLoaderDirective,
  DynamicHeightDirective,
} from '@venio/feature/shared/directives'
import { UiPaginationModule } from '@venio/ui/pagination'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { LayoutPanelSelectionComponent } from '../layout-panel-selection/layout-panel-selection.component'
import {
  CompositeLayoutFacade,
  CompositeLayoutState,
  LayoutResponseModel,
  StartupsFacade,
  UserRights,
} from '@venio/data-access/review'
import { ActivatedRoute } from '@angular/router'
import { catchError, EMPTY, filter, switchMap, take } from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { VenioNotificationService } from '@venio/feature/notification'
import { HttpErrorResponse } from '@angular/common/http'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { ConfirmationDialogService } from '../../../../services/confirmation-dialog-service'
import { toSignal } from '@angular/core/rxjs-interop'
import {
  CompositeFilterDescriptor,
  FilterDescriptor,
} from '@progress/kendo-data-query'

@Component({
  selector: 'venio-layout-listing',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    GridModule,
    TooltipModule,
    PopoverModule,
    UiPaginationModule,
    DropDownsModule,
    DateInputsModule,
    SvgLoaderDirective,
    ProgressBarModule,
    DynamicHeightDirective,
    LoaderModule,
    LayoutPanelSelectionComponent,
    DialogsModule,
  ],
  templateUrl: './layout-listing.component.html',
  styleUrl: './layout-listing.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutListingComponent implements OnInit {
  public ngOnInit(): void {
    this.layoutState.showLayoutListing.set(true)
  }

  public showCreateLayoutUI: boolean

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public cases: any[] = []

  public commonActionTypes = CommonActionTypes

  private activatedRoute: ActivatedRoute = inject(ActivatedRoute)

  private layoutFacade: CompositeLayoutFacade = inject(CompositeLayoutFacade)

  public layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  private notificationFacade = inject(VenioNotificationService)

  private confirmationDialogService = inject(ConfirmationDialogService)

  private startupFacade: StartupsFacade = inject(StartupsFacade)

  public selectedLayout: LayoutResponseModel

  public gridLayoutFilter: CompositeFilterDescriptor | null = null

  public selectedAction:
    | CommonActionTypes.CREATE
    | CommonActionTypes.EDIT
    | CommonActionTypes.CLONE

  public selectedGridLayouts: number[] = []

  public capitalizeTitle(title: string): string {
    return title.replace(
      /\w\S*/g,
      (text) => text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
    )
  }

  public hasLayoutManageRight = toSignal(
    this.startupFacade.hasGlobalRight$(UserRights.ALLOW_TO_MANAGE_REVIEW_LAYOUT)
  )

  public get hasDefaultLayoutSelection(): boolean {
    return this.selectedGridLayouts.some((layout) => layout === -1)
  }

  public openCreateLayout(): void {
    this.selectedLayout = null
    this.showCreateLayoutUI = true
  }

  public handleMainLayout(event: string): void {
    if (event === 'true') {
      this.showCreateLayoutUI = false
    }
  }

  public onActionClicked(
    layout: LayoutResponseModel,
    actionType: CommonActionTypes
  ): void {
    if (actionType === CommonActionTypes.EDIT) {
      this.selectedAction = this.commonActionTypes.EDIT
      this.selectedLayout = layout
      this.showCreateLayoutUI = true
    } else if (actionType === CommonActionTypes.CLONE) {
      this.selectedAction = this.commonActionTypes.CLONE
      this.selectedLayout = layout
      this.showCreateLayoutUI = true
    } else if (actionType === CommonActionTypes.DELETE) {
      this.#deleteLayout([layout.layoutId], layout.layoutName)
    } else if (actionType === this.commonActionTypes.FAVOURITE) {
      this.#markAsFavorite(layout.layoutId, !layout.isFavorite)
    }
  }

  #deleteLayout(layoutIds: number[], layoutName?: string): void {
    const title = 'Delete Layout'
    let content = ''
    if (layoutName)
      content =
        'Are you sure you want to delete this layout - "' + layoutName + '"?'
    else content = 'Are you sure you want to delete selected layouts'

    this.confirmationDialogService
      .showConfirmationDialog(title, content)
      .pipe(
        filter((confirmed) => confirmed),
        switchMap(() => {
          return this.layoutFacade.deleteLayout$({
            layoutIds: layoutIds,
          })
        }),
        catchError((err: unknown) => {
          const httpError = err as HttpErrorResponse
          this.notificationFacade.showError(httpError.error.message)
          return EMPTY
        }),
        take(1)
      )
      .subscribe((response: ResponseModel) => {
        this.notificationFacade.showSuccess(response.message)
        this.#fetchAllLayouts()
        this.selectedGridLayouts = []
      })
  }

  #markAsFavorite(layoutId: number, isFavorite: boolean): void {
    this.layoutFacade
      .markLayoutAsFavorite$(this.projectId, layoutId, { isFavorite })
      .pipe(
        catchError((error: unknown) => {
          const httpError = error as HttpErrorResponse
          this.notificationFacade.showError(httpError.error.message)
          return EMPTY
        }),
        take(1)
      )
      .subscribe((response: ResponseModel) => {
        const favLayout = this.layoutState
          .userLayouts()
          .find((f) => f.isFavorite)

        const currentFavLayout = this.layoutState
          .userLayouts()
          .find((f) => f.layoutId === layoutId)
        const updatedLayouts = this.layoutState.userLayouts().map((l) => {
          if (favLayout && l.layoutId === favLayout.layoutId)
            return { ...l, isFavorite: false }
          else if (l.layoutId === currentFavLayout.layoutId)
            return { ...l, isFavorite: isFavorite }
          return l
        })
        this.layoutState.userLayouts.set(updatedLayouts)

        this.notificationFacade.showSuccess(response.message)
      })
  }

  public onDeleteMultipleLayouts(): void {
    this.#deleteLayout(this.selectedGridLayouts)
  }

  #fetchAllLayouts(): void {
    this.layoutFacade
      .fetchLayouts$(this.projectId)
      .pipe(take(1))
      .subscribe((response: ResponseModel) => {
        const layouts: LayoutResponseModel[] = response.data
        this.layoutState.userLayouts.set([
          {
            isPrivate: false,
            layoutName: 'Default',
            layoutId: -1,
            isFavorite: layouts.every((f) => !f.isFavorite),
            allowManageBasedOnClient: false,
          },
          ...layouts,
        ])
      })
  }

  public onLayoutFilterChange(text: string): void {
    const filter: FilterDescriptor = {
      field: 'layoutName',
      operator: 'contains',
      value: text,
    }

    this.gridLayoutFilter = {
      logic: 'and',
      filters: [filter],
    }
  }
}
