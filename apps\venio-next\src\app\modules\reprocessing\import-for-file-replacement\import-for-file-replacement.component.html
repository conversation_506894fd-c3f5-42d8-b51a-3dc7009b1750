<kendo-dialog-titlebar (close)="onCancel()">
  <kendo-label
    text="Import files for Replacement"
    class="t-text-base t-text-[#2F3080]">
  </kendo-label>
</kendo-dialog-titlebar>
<div class="t-flex t-items-start t-space-x-1 t-gap-[5px] t-my-1">
  <div class="t-flex t-flex-col t-gap-[5px]">
    <div class="t-flex t-flex-row t-my-1 t-mx-2">
      <p class="t-font-medium t-pr-[5px]">File Replace Load File</p>
      <span
        class="t-text-center t-cursor-pointer t-rounded-full t-bg-[#2F3080] t-font-black t-text-white t-h-[18px] t-w-[18px]"
        showOn="hover"
        kendoPopoverAnchor
        [popover]="LoadFileHelpPopover"
        >?</span
      >
      <kendo-popover #LoadFileHelpPopover [width]="400" position="right">
        <ng-template kendoPopoverBodyTemplate>
          <kendo-label
            text="Sample load file for replacement"
            class="t-text-base t-font-bold t-text-[#2F3080]">
          </kendo-label>
          <div class="t-flex t-flex-col t-items-start t-gap-4 t-my-4">
            <div class="t-flex t-w-full t-flex-col">
              <kendo-label
                text="Sample load file"
                class="t-text-base t-text-[#2F3080]">
              </kendo-label>
              <div
                class="!t-border-[#979797] !t-border-[1px] !t-border-dashed !t-border-xs t-text-xs t-mt-1 t-font-normal t-pt-1 t-pb-4 t-px-1 t-align-left t-text-left t-text-[#000000]">
                <div class="t-pb-[2px]">FileId, ReplaceFileLocation</div>
                <div class="t-pb-[2px]">
                  1, "\\ShareServer\ShareFolder\ReplacementFile.txt"
                </div>
              </div>
            </div>
            <div class="t-flex t-w-full t-flex-col">
              <kendo-label
                text="Sample load file with no field header in first line"
                class="t-text-base t-text-[#2F3080]">
              </kendo-label>
              <div
                class="!t-border-[#979797] !t-border-[1px] !t-border-dashed !t-border-xs t-text-xs t-mt-1 t-font-normal t-pt-1 t-pb-4 t-px-1 t-align-left t-text-left t-text-[#000000]">
                <div class="t-pb-[2px]">
                  1, "\\ShareServer\ShareFolder\ReplacementFile.txt"
                </div>
              </div>
            </div>
            <div>
              <div>
                <p class="t-pb-[2px] t-text-base t-text-[#ED7425]">
                  <b>Note:</b>
                </p>
                <p class="t-pb-[2px] t-text-[#ED7425]">
                  Please use file location of the file to be replaced in the
                  "ReplaceFileLocation"
                </p>
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-popover>
    </div>
    <div class="t-flex t-flex-row t-gap-[5px] t-my-2">
      <div class="t-flex t-items-center t-gap-2">
        <input
          kendoTextBox
          placeholder="Choose a text file (*.txt, *.csv)"
          class="t-w-[250px]"
          [value]="selectedFileName"
          readonly />
        <button
          kendoButton
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          (click)="csvReader.click()">
          Browse
        </button>
        <input
          #csvReader
          type="file"
          class="t-hidden"
          accept=".csv,.txt"
          onclick="this.value = null"
          (change)="onFileSelected($event)" />
      </div>
      <div>
        <button
          kendoButton
          class="t-rounded-none"
          themeColor="dark"
          (click)="onValidateClick()"
          [disabled]="!file"
          fillMode="outline">
          Validate
        </button>
      </div>
    </div>
    <div class="t-inline-flex t-items-center t-gap-[5px]">
      <kendo-checkbox #LoadingCheckbox [(ngModel)]="noHeader"></kendo-checkbox>
      <kendo-label
        text="No field Header in first line of Load File"
        for="LoadingCheckbox"
        [ngClass]="{ 't-text-primary t-font-medium': noHeader }"></kendo-label>
    </div>
  </div>
</div>
<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      themeColor="secondary"
      fillMode="outline"
      class="v-custom-secondary-button"
      [disabled]="!file || !!formErrorMessage"
      (click)="onSaveClick()">
      Save
    </button>
    <button
      kendoButton
      (click)="onCancel()"
      themeColor="dark"
      fillMode="outline">
      Cancel
    </button>
  </div>
</kendo-dialog-actions>
