<kendo-grid
  class="t-h-[22rem]"
  scrollable="virtual"
  [filterable]="tagGroups?.length > 0"
  [sortable]="true"
  [rowHeight]="42"
  [filter]="filter"
  [pageSize]="pageSize"
  [skip]="skip"
  [trackBy]="tagGroupTrackByFn"
  [loading]="isTagGroupLoading"
  [data]="gridView"
  (filterChange)="filterChange($event)"
  (pageChange)="pageChange($event)">
  <ng-template kendoGridNoRecordsTemplate>
    <p *ngIf="!isTagGroupLoading && !gridView?.total">
      There is no data to display.
    </p>
  </ng-template>
  <kendo-grid-column
    field="tagGroupName"
    headerClass="t-text-primary"
    title="Tag Group">
  </kendo-grid-column>
  <kendo-grid-column
    [filterable]="false"
    [sortable]="false"
    title="Actions"
    headerClass="t-text-primary"
    [width]="140"
    [class]="{ 'text-center': true }"
    [resizable]="false">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-flex t-items-center" kendoTooltip>
        <kendo-buttongroup>
          @for (icon of tagGroupSvgIcons; track icon.actionType) {
          <button
            kendoButton
            #actionGrid
            *venioHasUserGroupRights="icon.allowedPermission"
            class="!t-py-[0.38rem] !t-px-[0.5rem] t-bg-white !t-border !t-border-[#263238] t-rounded-[2px] t-w-[32px] t-h-[25px]"
            [ngClass]="{
              't-opacity-50':
                icon.actionType === commonActionTypes.DELETE &&
                dataItem.isInUse,
              'hover:!t-border-[#ED7425] hover:!t-bg-[#ED7425]':
                icon.actionType === CommonActionTypes.DELETE,
              'hover:!t-border-[#FFBB12] hover:!t-bg-[#FFBB12]':
                icon.actionType === CommonActionTypes.EDIT,
              'hover:!t-border-[#1EBADC] hover:!t-bg-[#1EBADC]':
                icon.actionType === CommonActionTypes.CLONE
            }"
            (click)="onTagGroupAction(icon.actionType, dataItem)"
            [disabled]="
              icon.actionType === commonActionTypes.DELETE && dataItem.isInUse
            "
            fillMode="clear"
            [title]="icon.actionType"
            size="none">
            <span
              [parentElement]="actionGrid.element"
              venioSvgLoader
              [hoverColor]="'#ffffff'"
              color="#979797"
              [svgUrl]="icon.iconPath"
              height="0.9rem"
              width="1rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
          }
        </kendo-buttongroup>
      </div>
    </ng-template>
  </kendo-grid-column>
</kendo-grid>
