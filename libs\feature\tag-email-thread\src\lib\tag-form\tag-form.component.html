<div class="t-flex t-flex-col">
  <!-- add new tag -->
  <div
    class="t-flex t-w-full t-flex-col t-gap-2"
    *venioHasUserGroupRights="
      [
        rights.ADD_NEW_TAG,
        rights.EDIT_EXISTING_TAG,
        rights.ALLOW_TO_MANAGE_TAG
      ];
      anyOfTheGivenPermission: true
    ">
    <div class="t-flex t-flex-1 t-mt-3 t-w-full v-custom-grey-bg">
      <form class="t-flex t-flex-1 t-w-full t-gap-3" [formGroup]="tagFormGroup">
        <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
          <kendo-label
            for="name"
            class="t-text-xs t-uppercase t-tracking-widest">
            NAME <span class="t-text-error">*</span>
          </kendo-label>

          <kendo-textbox
            formControlName="tagName"
            placeholder="Tag Name"
            #name></kendo-textbox>
        </div>

        <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
          <kendo-label
            for="tag-group"
            class="t-text-xs t-uppercase t-tracking-widest">
            TAG GROUP <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-dropdownlist
            id="tag-group"
            formControlName="tagGroupId"
            [filterable]="true"
            [virtual]="{ itemHeight: 28 }"
            [kendoDropDownFilter]="{
              caseSensitive: false,
              operator: 'contains'
            }"
            [data]="tagGroup"
            [valuePrimitive]="true"
            textField="tagGroupName"
            valueField="tagGroupId">
          </kendo-dropdownlist>
        </div>
      </form>
    </div>
  </div>
  <!-- manage group tab-->
  <div class="t-flex t-mb-4">
    <kendo-tabstrip #tagGroupTabStrip class="t-w-full t-mt-3">
      <kendo-tabstrip-tab
        title="Advance Options"
        [selected]="true"
        *venioHasUserGroupRights="
          [
            rights.ALLOW_TO_MANAGE_TAG,
            rights.ADD_NEW_TAG,
            rights.EDIT_EXISTING_TAG,
            rights.ALLOW_TO_MANAGE_ADVANCE_TAG_OPTION
          ];
          anyOfTheGivenPermission: true
        ">
        <ng-template kendoTabContent [formGroup]="tagFormGroup">
          <div
            *venioHasUserGroupRights="
              [
                rights.ALLOW_TO_MANAGE_TAG,
                rights.ADD_NEW_TAG,
                rights.EDIT_EXISTING_TAG
              ];
              anyOfTheGivenPermission: true
            "
            class="t-flex t-w-full t-mt-4 t-flex-col t-gap-3 v-custom-grey-bg">
            <div class="t-flex t-w-full t-flex-wrap t-gap-[1.25%] t-gap-y-3">
              <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                <kendo-label
                  for="parent-tag"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Parent Tag
                </kendo-label>
                <kendo-combobox
                  id="parent-tag"
                  [clearButton]="true"
                  formControlName="parentTagId"
                  [filterable]="true"
                  [virtual]="{ itemHeight: 28 }"
                  [kendoDropDownFilter]="{
                    caseSensitive: false,
                    operator: 'contains'
                  }"
                  (filterChange)="parentTagFilter($event)"
                  [data]="filteredParentTags"
                  [valuePrimitive]="true"
                  textField="tagName"
                  valueField="tagId">
                </kendo-combobox>
              </div>
              <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                <kendo-label
                  for="Reviewer"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Reviewer Comment
                </kendo-label>

                <kendo-dropdownlist
                  formControlName="tagCommentRequirement"
                  [data]="['None', 'Recommended', 'Optional', 'Required']"
                  [valuePrimitive]="true">
                </kendo-dropdownlist>
              </div>

              <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                <kendo-label
                  for="Label"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Label
                </kendo-label>
                <kendo-textbox
                  formControlName="commentLabel"
                  placeholder="Enter Label"></kendo-textbox>
              </div>

              <div class="t-flex t-flex-0 t-basis-[32.5%] t-gap-1 t-flex-col">
                <kendo-label
                  for="Color"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Color
                </kendo-label>
                <div class="t-flex !t-relative">
                  <kendo-textbox
                    (click)="colorPicker.toggle(true)"
                    (focus)="colorPicker.toggle(true)"
                    [readonly]="true"
                    placeholder="Please select a color"
                    [value]="tagFormGroup.get('color').value"
                    class="!t-border-r-0 t-rounded-tr-none t-rounded-br-none"></kendo-textbox>
                  <kendo-colorpicker
                    #colorPicker
                    class="t-relative t-w-20 t-border-l-0 t-rounded-tl-none t-rounded-bl-none"
                    formControlName="color"
                    format="hex"
                    [svgIcon]="colorPalette"></kendo-colorpicker>
                </div>
              </div>
            </div>

            <div class="t-flex t-w-full t-flex-wrap t-gap-3">
              <div class="t-flex t-flex-0 t-basis-[100%] t-gap-1 t-flex-col">
                <kendo-label
                  for="Description"
                  class="t-text-xs t-uppercase t-tracking-widest">
                  Description
                </kendo-label>
                <kendo-textarea
                  formControlName="description"
                  placeholder="Description"
                  [rows]="3"
                  resizable="vertical"></kendo-textarea>
              </div>
            </div>
          </div>
          <venio-project-role
            [hidden]="(isTagSecurityPermissionAllowed$ | async) === false"
            permissionType="READ_WRITE"
            (projectRoleChange)="projectRoleChange($event)"
            [savedGroup]="tagGroupAccessControl.getRawValue()" />
          <div
            class="t-flex t-flex-col t-gap-3 t-mt-3"
            [formGroupName]="'tagPropagationSetting'">
            <div
              class="t-flex-none t-w-full t-font-bold t-text-base t-border t-border-t-0 t-border-l-0 t-border-r-0 t-border-b-1 v-custom-border t-pb-3">
              <span class="t-text-primary">Tag Propagation Settings</span>
            </div>
            <div class="t-flex-none t-font-bold t-text-base t-text-primary">
              Duplicate Propagation Settings
            </div>
            <div
              class="t-flex t-flex-col t-whitespace-nowrap t-gap-2 t-px-4 t-mb-2">
              <label *ngFor="let dupOption of duplicateTagPropagation()">
                <input
                  formControlName="dupTagOption"
                  [value]="dupOption.value"
                  type="radio"
                  kendoRadioButton
                  #dupRadio
                  size="small" />
                <span
                  class="t-pl-1.5"
                  [ngClass]="{
                    't-text-primary t-font-medium':
                      tagFormGroup.get('tagPropagationSetting.dupTagOption')
                        ?.value === dupOption.value && !dupRadio?.disabled
                  }">
                  {{ dupOption.label }}
                </span>
              </label>
            </div>
            <div class="t-flex-none t-font-bold t-text-base t-text-primary">
              Parent/Child Propagation Setting
            </div>
            <div
              class="t-flex t-flex-row t-gap-3 2 t-px-4 t-whitespace-nowrap t-mb-2">
              <input
                formControlName="propagatePCSet"
                type="checkbox"
                size="small"
                kendoCheckBox
                checked
                #propagatePCSet />
              <kendo-label
                [for]="propagatePCSet"
                text="Propagate tags to parent/child in the case"
                [ngClass]="{
                  't-text-primary t-font-medium':
                    (propagatePCSet?.checked ||
                      tagFormGroup.get('tagPropagationSetting.propagatePCSet')
                        ?.value) &&
                    !propagatePCSet?.disabled
                }"></kendo-label>
            </div>
            <div class="t-flex-none t-font-bold t-text-base t-text-primary">
              Email Thread Propagation Setting
            </div>
            <div
              class="t-flex t-flex-row t-gap-3 2 t-px-4 t-whitespace-nowrap t-mb-2">
              <input
                formControlName="includeEmailThread"
                type="checkbox"
                kendoCheckBox
                checked
                size="small"
                #includeEmailThread />
              <kendo-label
                [for]="includeEmailThread"
                text="Propagate tags to all emails of email thread in
                 the case"
                [ngClass]="{
                  't-text-primary t-font-medium':
                    (includeEmailThread?.checked ||
                      tagFormGroup.get(
                        'tagPropagationSetting.includeEmailThread'
                      )?.value) &&
                    !includeEmailThread?.disabled
                }"></kendo-label>
            </div>
            <div class="t-flex-none t-font-bold t-text-base t-text-primary">
              Near Duplicate Tagging Option
            </div>
            <div
              class="t-flex t-flex-col t-whitespace-nowrap t-gap-2 t-px-4 t-pb-4">
              <label *ngFor="let dupOption of nearDuplicateTagPropagation()">
                <input
                  formControlName="nddTagOption"
                  [value]="dupOption.value"
                  type="radio"
                  kendoRadioButton
                  #nddRadio
                  size="small" />
                <span
                  class="t-pl-1.5"
                  [ngClass]="{
                    't-text-primary t-font-medium':
                      tagFormGroup.get('tagPropagationSetting.nddTagOption')
                        ?.value === dupOption.value && !nddRadio?.disabled
                  }">
                  {{ dupOption.label }}
                </span>
              </label>
            </div>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab title="Manage Tag Group">
        <ng-template kendoTabContent>
          <form
            *venioHasUserGroupRights="
              [
                rights.ADD_NEW_TAG,
                rights.EDIT_EXISTING_TAG,
                rights.ALLOW_TO_MANAGE_TAG
              ];
              anyOfTheGivenPermission: true
            "
            [formGroup]="tagGroupFormGroup"
            class="t-flex t-w-full v-custom-grey-bg t-flex-col t-my-3">
            <div class="t-flex t-mt-3 t-flex-0 t-w-[32.5%]">
              <kendo-textbox
                #tagGroupInput
                formControlName="tagGroupName"
                placeholder="Tag group name">
                <ng-template
                  kendoTextBoxSuffixTemplate
                  *venioHasUserGroupRights="
                    [rights.ADD_NEW_TAG, rights.EDIT_EXISTING_TAG];
                    anyOfTheGivenPermission: true
                  ">
                  <button
                    (click)="tagGroupActionClicked(commonActionTypes.ADD)"
                    themeColor="secondary"
                    kendoButton
                    fillMode="clear"
                    title="Save"
                    [disabled]="!tagGroupNameControl.valid"
                    [svgIcon]="
                      tagGroupIdControl.value > 0 ? checkSvg : plusSvg
                    "></button>
                  <button
                    (click)="tagGroupActionClicked(commonActionTypes.RESET)"
                    themeColor="secondary"
                    kendoButton
                    fillMode="clear"
                    title="Reset"
                    [svgIcon]="refreshSvg"></button>
                </ng-template>
              </kendo-textbox>
            </div>
            <div
              class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
              <label
                class="k-checkbox-label"
                for="terms"
                [ngClass]="{
                  't-text-primary t-font-medium':
                    (isExclusiveCheckbox?.checked ||
                      tagGroupFormGroup.get('isExclusive')?.value) &&
                    !isExclusiveCheckbox?.disabled
                }"
                >Apply exclusive tag</label
              >
              <input
                formControlName="isExclusive"
                type="checkbox"
                id="terms"
                [size]="'small'"
                kendoCheckBox
                #isExclusiveCheckbox />
            </div>
          </form>
          <ng-container
            *ngComponentOutlet="
              tagGroupListLazyComponent | async
            "></ng-container>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>
  </div>
</div>
