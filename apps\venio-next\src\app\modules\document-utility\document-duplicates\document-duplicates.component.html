<kendo-grid
  #grid
  [kendoGridBinding]="documentDuplicates"
  [loading]="isDuplicateLoading$ | async"
  scrollable="virtual"
  [sortable]="true"
  [autoSize]="true"
  [rowClass]="rowClass"
  [resizable]="true"
  (dataStateChange)="onDataStateChange()">
  <ng-template kendoGridNoRecordsTemplate>
    <div class="t-flex t-h-min t-w-full">
      <span class="t-text-[#000000BC] t-text-[16px]">No records found</span>
    </div>
  </ng-template>
  <kendo-grid-column
    title="Details"
    [headerClass]="[
      't-text-primary',
      !documentDuplicates?.length ? 't-min-w-[150px]' : ''
    ]"
    [width]="250"
    [minResizableWidth]="100">
    <ng-template kendoGridtHeaderTemplate let-column>
      <span kendoTooltip title="Details">Details</span>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="t-flex t-justify-center t-items-center t-w-full">
        <span
          class="t-cursor-pointer"
          (click)="onDetailsClicked(dataItem)"
          venioSvgLoader
          kendoTooltip
          title="View"
          hoverColor="#FFBB12"
          color="#979797"
          [svgUrl]="'assets/svg/icon-show-fields.svg'"
          height="1rem"
          width="1rem">
          <kendo-loader size="small" />
        </span>
      </div>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column
    *ngFor="let field of headers"
    [field]="field"
    headerClass="t-text-primary">
    <ng-template kendoGridHeaderTemplate let-column>
      <span kendoTooltip [title]="field">{{ field }}</span>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      <span kendoTooltip [title]="dataItem[field]">{{ dataItem[field] }}</span>
    </ng-template>
  </kendo-grid-column>
</kendo-grid>
