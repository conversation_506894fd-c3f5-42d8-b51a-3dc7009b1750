<venio-report-type-dropdown class="t-w-full t-mb-2" />
<div
  class="t-flex t-flex-grow t-space-x-4 t-gap-1 t-relative t-border-b t-border-[#e3e3e3] t-pb-2 t-h-[3rem]">
  @if(isProjectDropdownVisible){
  <venio-report-project-dropdown class="t-grow-1/2 t-self-center" />
  }@else if(isDeletedModeDropdownVisible){
  <venio-delete-mode-dropdown
    class="t-grow-1/2 t-self-center"></venio-delete-mode-dropdown>
  } @else if(isProjectAndUserDropdownVisible) {
  <venio-report-project-dropdown class="t-grow-1/2 t-self-center" />
  <venio-report-user-dropdown class="t-grow-1/2 t-self-center" />
  } @else if(isUserAndProjectDropdownVisible) {
  <venio-report-user-dropdown class="t-grow-1/2 t-self-center" />
  <venio-report-project-dropdown class="t-grow-1/2 t-self-center" />
  } @else if(isLegalHoldReport) {
  <venio-report-legal-hold
    class="t-grow-auto t-self-center"></venio-report-legal-hold>
  } @else if(isActivateDeactivateCustodianReport) {
  <venio-activate-deactivate-custodian-report
    class="t-grow-auto t-self-center"></venio-activate-deactivate-custodian-report>
  } @else {
  <venio-report-user-dropdown class="t-grow-1/2 t-self-center" />
  }
  <venio-report-date-picker
    *ngIf="isDatePickerVisible"
    class="t-grow-1/2 t-self-center" />
  <venio-report-generate-button class="t-grow-auto t-self-center" />
  <venio-report-generate-info
    class="t-flex t-justify-end t-items-end t-flex-col t-basis-auto t-flex-grow t-min-w-0 t-space-x-4" />
</div>

<venio-report-action-toolbar class="t-w-full t-mt-3" />
