<div class="t-relative">
  <div
    *ngIf="!isDataLoaded()"
    class="k-i-loading t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-text-[58px] t-z-10 t-grid t-relative t-h-[400px] t-overflow-hidden t-place-content-center"></div>
  <kendo-grid
    class="v-custom-tagtree !t-max-h-[calc(100vh_-_19.5rem)] t-min-h-[15rem]"
    *ngIf="isDataLoaded()"
    [loading]="isCodingFieldsLoading$ | async"
    scrollable="virtual"
    [filterable]="codingFields?.length > 0"
    [resizable]="true"
    [sortable]="false"
    [navigable]="true"
    [rowHeight]="36"
    [filter]="filter"
    [pageSize]="pageSize"
    [skip]="skip"
    [trackBy]="codingFieldTrackByFn"
    [data]="gridView"
    (filterChange)="filterChange($event)"
    (pageChange)="pageChange($event)"
    [rowReorderable]="true"
    (rowReorder)="onRowReorder($event)">
    <ng-template kendoGridNoRecordsTemplate>
      <p
        *ngIf="
          (isCodingFieldsLoading$ | async) === false && !codingFields?.length
        ">
        There is no data to display.
      </p>
    </ng-template>
    <kendo-grid-rowreorder-column
      class="!t-px-2"
      [columnMenu]="false"
      [autoSize]="false"
      [resizable]="false"
      [width]="40"></kendo-grid-rowreorder-column>

    <kendo-grid-column
      field="fieldName"
      headerClass="t-text-primary"
      title="Field Name">
    </kendo-grid-column>
    <kendo-grid-column
      field="displayName"
      headerClass="t-text-primary"
      title="Display Name">
    </kendo-grid-column>
    <kendo-grid-column
      field="uiInputType"
      headerClass="t-text-primary"
      title="Field Type">
    </kendo-grid-column>
    <kendo-grid-column
      field="description"
      headerClass="t-text-primary"
      title="Description">
    </kendo-grid-column>
    <kendo-grid-column
      title="Action"
      headerClass="t-text-primary"
      [width]="140"
      [class]="{ 'text-center': true }"
      [resizable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div
          class="t-flex t-items-center"
          kendoTooltip
          *ngIf="dataItem.customFieldId > 0">
          <kendo-buttongroup>
            <button
              kendoButton
              #tagAction
              *ngFor="
                let icon of codingFieldActionIcons();
                trackBy: trackByIconFn
              "
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-bg-white !t-border !t-border-[#263238] t-rounded-[2px] t-w-[32px] t-h-[25px]"
              [ngClass]="getAllButtonClasses(icon, dataItem)"
              (click)="codingActionClicked(icon.actionType, dataItem)"
              [disabled]="isButtonDisabled(icon, dataItem)"
              fillMode="clear"
              title="{{ icon.actionType }}"
              size="none">
              <ng-container
                *ngIf="
                  icon.isLoading &&
                    selectedAction.get(dataItem.customFieldId) ===
                      icon.actionType &&
                    selectedCustomField?.customFieldId ===
                      dataItem.customFieldId;
                  else iconElement
                ">
                <kendo-loader
                  class="t-mt-[-0.2rem]"
                  size="small"
                  type="infinite-spinner"></kendo-loader>
              </ng-container>
              <ng-template #iconElement>
                <span
                  [parentElement]="tagAction.element"
                  venioSvgLoader
                  [hoverColor]="'#ffffff'"
                  color="#979797"
                  [svgUrl]="icon.iconPath"
                  height="0.9rem"
                  width="1rem">
                  <kendo-loader size="small"></kendo-loader>
                </span>
              </ng-template>
            </button>
          </kendo-buttongroup>
        </div>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
<!-- <kendo-grid
      [data]="gridData"
      [rowReorderable]="true"
      [pageSize]="15"
      [pageable]="true"
      [height]="420"
      (rowReorder)="onRowReorder($event)"
    >
      <kendo-grid-rowreorder-column [width]="40"></kendo-grid-rowreorder-column>
      <kendo-grid-column
        field="CompanyName"
        [width]="260"
        title="Company Name"
      ></kendo-grid-column>
      <kendo-grid-column
        field="ContactName"
        [width]="180"
        title="Contact Name"
      ></kendo-grid-column>
      <kendo-grid-column field="City" [width]="100"></kendo-grid-column>
      <kendo-grid-column
        field="ContactTitle"
        title="Contact Title"
      ></kendo-grid-column>
    </kendo-grid> -->
