<div class="t-p-4 t-bg-[#F6F6F6] t-mt-3" [formGroup]="edaiFormGroup()">
  <div
    class="t-flex t-space-x-6 t-mb-4 t-items-center"
    formGroupName="privilegeJobModel">
    <div class="t-gap-2 t-w-full">
      <div class="t-w-full t-flex t-gap-4" formArrayName="privilegeTypes">
        <label
          *ngFor="let groupIndex of [0, 1]"
          [formGroupName]="groupIndex"
          class="t-flex t-items-center t-space-x-2"
          [ngClass]="{
            't-text-primary t-font-medium': edaiFormGroup().get(
              'privilegeJobModel.privilegeTypes.' + groupIndex + '.name'
            )?.value
          }">
          <input type="checkbox" kendoCheckBox formControlName="name" />
          <span
            >{{ groupIndex === 0 ? 'Attorney Client' : 'Work Product' }}
          </span>
        </label>
      </div>
      @if(!attorneyOrProductSelected()) {
      <div class="t-flex t-w-full t-mt-1">
        <kendo-formerror>Please select one of the option</kendo-formerror>
      </div>
      }
    </div>
  </div>
  <div
    class="t-flex t-flex-row t-gap-4 t-mb-4"
    formGroupName="privilegeJobModel">
    <div
      formArrayName="privilegeTypes"
      *ngFor="let groupIndex of [2, 3]; let i = index"
      class="t-flex t-flex-col t-w-1/2 t-gap-2">
      <ng-container [formGroupName]="groupIndex">
        <kendo-textbox
          formControlName="name"
          placeholder="Custom Priv type {{ i + 1 }} Name"
          class="t-w-64" />
        <div class="t-w-full">
          <kendo-textarea
            formControlName="description"
            resizable="none"
            [rows]="4"
            placeholder="Custom Priv Type {{
              i + 1
            }} Definition"></kendo-textarea>
          @if(!customTypesDefinitionValidation()[i]) {
          <div class="t-flex t-w-full t-mt-1">
            <kendo-formerror>Definition is required</kendo-formerror>
          </div>
          }
        </div>
      </ng-container>
    </div>
  </div>
  <div class="t-grid t-grid-cols-2 t-gap-4" formGroupName="privilegeJobModel">
    <div class="t-w-full">
      <kendo-textarea
        formControlName="attorneyList"
        placeholder="Attorney List"
        resizable="none"
        [rows]="4" />
      @if(edaiFormGroup().get('privilegeJobModel.attorneyList')?.errors?.maxlength)
      {
      <div class="t-flex t-w-full t-mt-1">
        <kendo-formerror>
          Attorney List:
          {{
            edaiFormGroup().get('privilegeJobModel.attorneyList')?.errors
              ?.maxlength?.actualLength
          }}
          /
          {{
            edaiFormGroup().get('privilegeJobModel.attorneyList')?.errors
              ?.maxlength?.requiredLength
          }}
          - limit exceeded.
        </kendo-formerror>
      </div>
      } @if(edaiFormGroup().get('privilegeJobModel.attorneyList')?.invalid &&
      (!edaiFormGroup().get('privilegeJobModel.attorneyList')?.untouched ||
      edaiFormGroup().get('privilegeJobModel.attorneyList')?.dirty) &&
      !edaiFormGroup().get('privilegeJobModel.attorneyList')?.errors?.maxlength
      ) {
      <div class="t-flex t-w-full t-mt-1">
        <kendo-formerror>Provide Attorney list</kendo-formerror>
      </div>
      }
    </div>
    <div class="t-w-full">
      <kendo-textarea
        formControlName="domains"
        placeholder="Safe Domains"
        resizable="none"
        [rows]="4" />
      @if(edaiFormGroup().get('privilegeJobModel.domains')?.invalid &&
      (!edaiFormGroup().get('privilegeJobModel.domains')?.untouched ||
      edaiFormGroup().get('privilegeJobModel.domains')?.dirty) ) {
      <div class="t-flex t-w-full t-mt-1">
        <kendo-formerror>Provide Safe Domain names</kendo-formerror>
      </div>
      }
    </div>
  </div>
</div>
