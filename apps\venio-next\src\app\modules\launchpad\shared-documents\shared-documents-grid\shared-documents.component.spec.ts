import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SharedDocumentsComponent } from './shared-documents.component'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideMockStore } from '@ngrx/store/testing'
import { of } from 'rxjs'
import { DocumentShareFacade } from '@venio/data-access/review'

describe('SharedDocumentsComponent', () => {
  let component: SharedDocumentsComponent
  let fixture: ComponentFixture<SharedDocumentsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        SharedDocumentsComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        {
          provide: DocumentShareFacade,
          useValue: {
            selectIsSharedDocumentsLoading$: of(false),
            selectSharedDocumentList$: of([
              { documentShareID: 1, shareName: 'Document 1' },
              { documentShareID: 2, shareName: 'Document 2' },
            ]),
            fetchSharedDocumentList$: of([]),
            selectSharedDocDetailPagingInfo$: of({
              pageNumber: 1,
              pageSize: 10,
              total: 2,
            }),
            selectSharedDocumentRequestInfo$: of({
              documentShareID: 1,
              shareName: 'Document 1',
              documentShareStatus: 'shared',
            }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SharedDocumentsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should load grid data on initialization', () => {
    expect(component.gridView().data.length).toBeGreaterThan(0)
    expect(component.gridView().total).toBe(2)
  })
})
