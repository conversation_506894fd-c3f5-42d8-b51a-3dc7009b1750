<div class="t-flex t-flex-col t-w-full t-gap-2 t-mt-0 v-custom-grey-bg">
  <div class="t-font-roboto t-font-bold t-text-base t-leading-[18px] t-pl-1">
    Security
  </div>
  <!--  TODO: maybe more actions for the grid below added here-->
  <kendo-grid
    [loading]="isProjectGroupLoading$ | async"
    [data]="projectGroup"
    [sortable]="true">
    <kendo-grid-column
      headerClass="t-text-primary"
      field="groupName"
      title="Role"
      [width]="300">
    </kendo-grid-column>
    <kendo-grid-column
      [sortable]="false"
      field="permission"
      headerClass="t-text-primary"
      title="Permission">
      <ng-template kendoGridCellTemplate let-data>
        <kendo-dropdownlist
          class="t-w-1/3"
          [(value)]="data['permission']"
          [data]="permissionDropdown"
          textField="label"
          valueField="value"
          (valueChange)="permissionChange()"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
