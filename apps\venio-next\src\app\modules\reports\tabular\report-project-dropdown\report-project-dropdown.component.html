<kendo-multiselect
  [filterable]="true"
  [clearButton]="false"
  [virtual]="true"
  [listHeight]="300"
  [checkboxes]="true"
  [autoClose]="false"
  [tagMapper]="tagMapper"
  [value]="selectedProjects()"
  #userSelection
  (filterChange)="filterProjects($event)"
  (valueChange)="projectSelectionChange($event)"
  class="t-min-w-[18rem] t-max-w-[18rem]"
  [data]="filteredProjects()"
  (removeTag)="removeTag($event)"
  [valuePrimitive]="true"
  valueField="projectId"
  textField="projectName">
  <ng-template kendoMultiSelectHeaderTemplate let-dataItem>
    <div
      class="t-flex t-p-2 t-items-center t-h-full t-w-full t-relative t-z-50"
      (click)="handleAllProjectClick($event, allProjects)">
      <input
        [checked]="isAllProjectChecked()"
        (click)="handleAllProjectClick($event, allProjects)"
        (change)="allProjectSelectionChange($event.target['checked'])"
        type="checkbox"
        #allProjects
        id="all-projects"
        kendoCheckBox />
      <kendo-label
        (click)="handleAllProjectClick($event, allProjects)"
        class="k-checkbox-label t-w-full t-h-full"
        for="all-projects"
        text="All Projects"></kendo-label>
    </div>
  </ng-template>
  <ng-template kendoSuffixTemplate>
    <kendo-svg-icon
      class="t-cursor-pointer t-w-[19px] t-h-[19px] t-text-[#333333]"
      (click)="chevDownIconClick(userSelection)"
      [icon]="chevronDownIcon"></kendo-svg-icon>
  </ng-template>
  <ng-template kendoMultiSelectGroupTagTemplate let-dataItems>
    {{ dataItems.length }} project(s) selected
  </ng-template>
</kendo-multiselect>
