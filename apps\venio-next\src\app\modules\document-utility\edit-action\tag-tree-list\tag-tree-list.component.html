<div class="t-relative">
  <div
    *ngIf="!isDataLoaded()"
    class="k-i-loading t-w-full t-bg-[rgba(255,255,255,0.47)] t-text-[rgba(18,17,17,0.93)] t-text-[58px] t-z-10 t-grid t-relative t-h-[400px] t-overflow-hidden t-place-content-center"></div>
  <kendo-treelist
    #tagTreeComponent
    *ngIf="isDataLoaded()"
    [kendoTreeListFlatBinding]="tagTree()"
    [selectable]="selectableSettings"
    [loading]="isTagTreeLoading$ | async"
    idField="id"
    parentIdField="parentId"
    kendoTreeListExpandable
    [initiallyExpanded]="true"
    kendoTreeListSelectable
    [ngClass]="
      'v-custom-tagtree !t-min-h-[15rem] !t-max-h-[calc(100vh_-_19.5rem)] !t-w-full'
    "
    scrollable="virtual"
    [rowHeight]="30"
    [pageSize]="100"
    [columnMenu]="false"
    [autoSize]="true"
    [navigable]="true"
    [pageable]="false"
    [filterable]="tagTree().length > 0"
    [sortable]="true"
    [trackBy]="trackByTreeFn"
    [rowReorderable]="true"
    (rowReorder)="onRowReorder($event)">
    <!--                    TODO: later sometimes, the commented code will be used-->
    <!--                    <kendo-treelist-checkbox-column-->
    <!--                      headerClass="t-text-primary v-custom-tagheader"-->
    <!--                      class="!t-px-0"-->
    <!--                      title="Tags"-->
    <!--                      [minResizableWidth]="150"-->
    <!--                      [expandable]="true"-->
    <!--                      [checkChildren]="true"-->
    <!--                      [showSelectAll]="true">-->
    <!--                      <ng-template kendoTreeListCellTemplate let-dataItem>-->
    <!--                        <div class="t-inline-block t-absolute t-ml-7">-->
    <!--                          {{ dataItem.tagName }}-->
    <!--                        </div>-->
    <!--                      </ng-template>-->
    <!--                    </kendo-treelist-checkbox-column>-->
    <!--                    TODO: once the reorder and checkbox feature is added, uncomment the above and remove this below-->

    <ng-template kendoTreeListNoRecordsTemplate>
      <p *ngIf="(isTagTreeLoading$ | async) === false && !tagTree().length">
        There is no data to display.
      </p>
    </ng-template>
    <kendo-treelist-rowreorder-column
      class="!t-px-2"
      [columnMenu]="false"
      [autoSize]="false"
      [resizable]="false"
      [width]="40"></kendo-treelist-rowreorder-column>
    <kendo-treelist-column
      field="tagName"
      title="Tags"
      [sortable]="true"
      [autoSize]="true"
      [expandable]="true"
      [columnMenu]="false"
      headerClass="t-text-primary"
      class="!t-flex !t-h-[30px] t-items-center">
      <ng-template kendoTreeListCellTemplate let-data>
        <div class="t-flex t-gap-1 t-min-h-[inherit] t-items-center">
          <span
            class="t-h-2 t-w-2 t-float-left t-mx-px t-rounded-full t-mr-1.5"
            [ngStyle]="{ 'background-color': data.color }"></span>
          {{ data.tagName }}
        </div>
      </ng-template>
    </kendo-treelist-column>
    <kendo-treelist-command-column
      title="Action"
      [width]="110"
      [autoSize]="true"
      [columnMenu]="false"
      headerClass="t-text-primary">
      <ng-template kendoTreeListCellTemplate let-dataItem>
        <div
          class="t-flex t-items-center"
          kendoTooltip
          *ngIf="dataItem.tagId > 0">
          <kendo-buttongroup>
            @for (icon of tagActionIcons(); track icon.actionType){
            <button
              kendoButton
              #tagAction
              *venioHasUserGroupRights="icon.allowedPermission"
              class="!t-py-[0.38rem] !t-px-[0.5rem] t-bg-white !t-border !t-border-[#263238] t-rounded-[2px] t-w-[32px] t-h-[25px]"
              [ngClass]="getAllButtonClasses(icon, dataItem)"
              (click)="tagActionClicked(icon.actionType, dataItem)"
              [disabled]="isButtonDisabled(icon, dataItem)"
              fillMode="clear"
              [title]="icon.actionType"
              size="none">
              <ng-container
                *ngIf="
                  icon.isLoading &&
                    selectedAction.get(dataItem.tagId) === icon.actionType &&
                    selectedTag?.tagId === dataItem.tagId;
                  else iconElement
                ">
                <kendo-loader
                  class="t-mt-[-0.2rem]"
                  size="small"
                  type="infinite-spinner"></kendo-loader>
              </ng-container>

              <ng-template #iconElement>
                <span
                  [parentElement]="tagAction.element"
                  venioSvgLoader
                  [hoverColor]="'#ffffff'"
                  color="#979797"
                  [svgUrl]="icon.iconPath"
                  height="0.9rem"
                  width="1rem">
                  <kendo-loader size="small"></kendo-loader>
                </span>
              </ng-template>
            </button>
            }
          </kendo-buttongroup>
        </div>
      </ng-template>
    </kendo-treelist-command-column>
  </kendo-treelist>
</div>
