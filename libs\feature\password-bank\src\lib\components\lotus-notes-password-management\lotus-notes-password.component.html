<div
  class="t-flex t-flex-col t-items-start t-my-2 t-gap-[5px] t-mt-[15px]"
  [formGroup]="form">
  <div class="t-flex t-flex-row t-justify-between t-items-center t-mb-3">
    <span class="t-flex t-pr-[20px]">
      <kendo-label
        text="Add NSF User Id, Password"
        class="t-text-base t-font-bold t-text-[#2F3080]">
      </kendo-label>
    </span>
    <span class="t-flex">
      <button
        kendoButton
        (click)="openImportNsfUserIdPasswordDialog()"
        class="v-custom-secondary-button"
        themeColor="secondary"
        [disabled]="isAddingPassword()"
        fillMode="outline">
        Import User Id File, Password List
      </button>
    </span>
  </div>
  <div>
    <p>NSF User Id file <span class="t-text-[#DC3545]">*</span></p>
  </div>
  <div class="t-flex t-items-center t-gap-2">
    <input
      kendoTextBox
      placeholder="Browse .id file"
      class="t-w-[250px]"
      [value]="filePath() || ''"
      [disabled]="isAddingPassword()"
      readonly />
    <button
      kendoButton
      class="v-custom-secondary-button"
      themeColor="secondary"
      [disabled]="isAddingPassword()"
      fillMode="outline"
      (click)="fileInput.click()">
      Browse
    </button>
    <input
      #fileInput
      type="file"
      class="t-hidden"
      [disabled]="isAddingPassword()"
      accept=".id"
      (change)="handleFileSelection($event)" />
  </div>
  <div class="t-my-1">
    <div
      class="t-flex t-flex-col t-gap-2 t-items-start t-py-[5px] t-px-[5px] t-my-3">
      <span class="t-inline-flex t-gap-0">
        <input
          type="radio"
          kendoRadioButton
          name="hasNsfPassword"
          #noAssociatedPassword
          [value]="false"
          formControlName="hasNsfPassword" />
        <kendo-label
          class="k-radio-label t-relative"
          [for]="noAssociatedPassword"
          [ngClass]="{
            't-text-primary t-font-medium':
              form.get('hasNsfPassword')?.value === false &&
              !noAssociatedPassword?.disabled
          }"
          text="NSF User Id File does not have associated Password"></kendo-label>
      </span>

      <span class="t-inline-flex t-gap-0">
        <input
          type="radio"
          kendoRadioButton
          name="hasNsfPassword"
          #AssociatedPassword
          [value]="true"
          formControlName="hasNsfPassword" />
        <kendo-label
          class="k-radio-label t-relative"
          [for]="AssociatedPassword"
          [ngClass]="{
            't-text-primary t-font-medium':
              form.get('hasNsfPassword')?.value === true &&
              !AssociatedPassword?.disabled
          }"
          text="NSF User Id File has associated Password"></kendo-label>
      </span>
    </div>

    <div class="t-flex t-flex-row t-gap-[5px]">
      <input
        kendoTextBox
        placeholder="Password"
        formControlName="passwordInput" />
      <button
        kendoButton
        themeColor="secondary"
        fillMode="outline"
        [disabled]="!form.valid || isAddingPassword()"
        class="v-custom-secondary-button"
        (click)="addPassword()">
        Add
        <kendo-loader
          *ngIf="isAddingPassword()"
          type="pulsing"
          themeColor="success" />
      </button>
    </div>
    <div
      *ngIf="
        (form?.controls.passwordInput?.invalid &&
          form?.controls.passwordInput?.dirty &&
          !form?.controls.passwordInput?.untouched) ||
        (form?.controls.passwordInput?.dirty &&
          !form?.controls.passwordInput?.untouched &&
          form?.controls.passwordInput?.value.trim() === '')
      "
      class="t-m-1 t-accent-error t-text-error t-text-[11.23px]">
      {{
        form?.controls.passwordInput.hasError('required')
          ? 'Password is a required field'
          : ''
      }}
    </div>
  </div>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-flex-row t-justify-between t-items-center t-mb-3">
      <p class="t-my-1 t-text-base t-text-[#2F3080] t-font-bold t-mt-4 t-mb-2">
        NSF User Id File Password List
      </p>

      <button
        kendoButton
        themeColor="error"
        (click)="deleteMultiplePasswords()"
        fillMode="outline"
        *ngIf="selectedPasswordIds.length > 0">
        Delete
        <kendo-loader
          *ngIf="deleteInProgress()"
          type="infinite-spinner"
          themeColor="error" />
      </button>
    </div>

    <kendo-grid
      [data]="lotusNotesPasswords()"
      [height]="240"
      [selectable]="true"
      kendoGridSelectBy
      [resizable]="true"
      [selectable]="{ mode: 'multiple', cell: false, checkboxOnly: true }"
      [(selectedKeys)]="selectedPasswordIds"
      (selectedKeysChange)="passwordSelectionChange($event)"
      kendoGridSelectBy="passwordBankId">
      <kendo-grid-checkbox-column
        [width]="45"
        [columnMenu]="false"
        [showSelectAll]="true"
        [resizable]="false"></kendo-grid-checkbox-column>
      <kendo-grid-column
        headerClass="t-text-primary"
        field="originalUserIdFilePath"
        title="NSF User Id  File"></kendo-grid-column>
      <kendo-grid-column
        headerClass="t-text-primary"
        field="password"
        title="Password"></kendo-grid-column>
      <kendo-grid-column
        title="Delete"
        [width]="100"
        headerClass="t-text-primary">
        <ng-template kendoGridCellTemplate let-dataItem>
          <button
            kendoButton
            #actionGridDelete
            data-qa="delete-password"
            class="!t-py-[0.38rem] !t-px-[0.5rem] !t-bg-white !t-border !t-border-[#263238] !t-w-[32px] !t-h-[25px] hover:!t-border-[#ED7425] hover:!t-bg-[#ED7425]"
            (click)="deletePassword(dataItem)"
            kendoTooltip
            [title]="'DELETE'"
            size="none">
            <span
              [parentElement]="actionGridDelete.element"
              venioSvgLoader
              [svgUrl]="deleteSvgUrl"
              hoverColor="#FFF"
              height="0.75rem"
              width="0.6rem"></span>
          </button>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</div>
