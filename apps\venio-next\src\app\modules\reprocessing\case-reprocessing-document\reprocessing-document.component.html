@if(isLoading()) {
<div class="t-flex t-flex-col t-w-full t-px-2">
  <kendo-skeleton width="100%" [height]="100"> </kendo-skeleton>
  <kendo-skeleton
    width="100%"
    [height]="40"
    *ngFor="let y of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]">
  </kendo-skeleton>
</div>
} @else {
<div class="t-flex t-flex-col t-h-[calc(100%-140px)] t-mt-3">
  <kendo-grid
    class="t-w-full t-h-full t-grid t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto !t-flex"
    [kendoGridBinding]="fileInfos()"
    venioDynamicHeight
    [extraSpacing]="40"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    kendoGridSelectBy="fileId"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [pageSize]="pageSize()"
    [sortable]="true"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    (selectionChange)="onSelectionChange($event)"
    [rowSelected]="isRowSelected"
    [(selectedKeys)]="selectedKeys">
    <ng-template kendoPagerTemplate>
      <div class="t-flex t-gap-2">
        <button
          kendoButton
          themeColor="secondary"
          (click)="openCustodianMediaOverlay()"
          [disabled]="isReprocessQueuing()"
          class="t-w-9 t-h-9 hover:t-text-[#FFFFFF] hover:t-bg-[#9BD2A7]"
          fillMode="solid"
          #actionGrid>
          <span
            venioSvgLoader
            [parentElement]="actionGrid.element"
            applyEffectsTo="stroke"
            hoverColor="#FFFFFF"
            color="#FFFFFF"
            svgUrl="assets/svg/icon-reprocessing-mashup.svg"
            height="1.1rem"
            width="1.1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
      </div>
      <kendo-grid-spacer></kendo-grid-spacer>

      <venio-pagination
        [disabled]="totalDocuments() === 0 || isReprocessQueuing()"
        [totalRecords]="totalDocuments()"
        [pageSize]="pageSize()"
        [currentPage]="currentPage()"
        [showPageJumper]="false"
        [showPageSize]="true"
        [showRowNumberInputBox]="true"
        (pageChanged)="pageChanged($event)"
        (pageSizeChanged)="pageSizeChanged($event)"
        class="t-px-5 t-block t-py-2">
      </venio-pagination>
    </ng-template>

    <!-- Checkbox Column -->
    <kendo-grid-checkbox-column [showSelectAll]="true" [width]="40">
    </kendo-grid-checkbox-column>

    <!-- File ID Column -->
    <kendo-grid-column
      field="fileId"
      title="File ID"
      [width]="100"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="File ID"
          >File ID</span
        >
      </ng-template>
    </kendo-grid-column>

    <!-- Media Name Column -->
    <kendo-grid-column
      field="mediaName"
      title="Media Name"
      [width]="150"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Media Name"
          >Media Name</span
        >
      </ng-template>
    </kendo-grid-column>

    <!-- File Name Column -->
    <kendo-grid-column
      field="fileName"
      title="File Name"
      [width]="150"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="File Name"
          >File Name</span
        >
      </ng-template>
    </kendo-grid-column>

    <!-- Original File Path Column -->
    <kendo-grid-column
      field="originalFilePath"
      title="Original File Path"
      [width]="200"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Original File Path"
          >Original File Path</span
        >
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <span kendoTooltip title="{{ dataItem.originalFilePath }}">{{
          dataItem.originalFilePath
        }}</span>
      </ng-template>
    </kendo-grid-column>

    <!-- File Type Column -->
    <kendo-grid-column
      field="fileType"
      title="File Type"
      [width]="120"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="File Type"
          >File Type</span
        >
      </ng-template>
    </kendo-grid-column>

    <!-- Exception Type Column -->
    <kendo-grid-column
      field="replacementType"
      title="Exception Type"
      [width]="160"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Exception Type"
          >Exception Type</span
        >
      </ng-template>
    </kendo-grid-column>

    <!-- Actions Column -->
    <kendo-grid-column
      title="Actions"
      [width]="165"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Actions"
          >Actions</span
        >
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <a href="javascript:void(0)" class="bulk-settings">
          <div class="t-flex t-gap-1 t-ml-2">
            <kendo-dropdownbutton
              #dropdownButton
              fillMode="clear"
              [data]="bulkSettingOptions"
              (itemClick)="onSettingChange($event, dataItem)"
              class="t-w-60 v-custom-dropdown-settings-btn t-border-0 t-bg-transparent">
              <span
                class="t-text-left t-text-sm t-mr-[5px] t-tracking-tight t-text-[#323130]"
                >{{ getSelectedItemText(dataItem.settingsType) }}</span
              >
              <kendo-svg-icon
                class="t-text-right t-h-[20px] t-w-[20px]"
                [icon]="icons.chevronDownIcon"></kendo-svg-icon>
            </kendo-dropdownbutton>
          </div>
        </a>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
<div class="t-h-[70px] t-flex t-items-center">
  <div
    class="t-flex t-justify-between t-items-center t-p-4 t-mt-[4px] t-w-full">
    <div class="t-flex">
      <div *ngIf="totalDocuments() > pageSize()">
        <span class="t-inline-flex t-items-center t-gap-2">
          <input
            type="checkbox"
            #SelectAllPages
            kendoCheckBox
            [checked]="isFileinAllPageSelected()"
            (change)="onAllFileinAllPageChange()" />
          <kendo-label
            class="k-radio-label"
            [for]="SelectAllPages"
            text="Select All from All Pages"
            [ngClass]="{
              't-text-primary t-font-medium': isFileinAllPageSelected()
            }"></kendo-label>
        </span>
      </div>
    </div>
    <button
      kendoButton
      #reprocessButton
      themeColor="secondary"
      fillMode="outline"
      class="v-custom-secondary-button t-cursor-pointer"
      [disabled]="!(selectedDocumentsCount() > 0) || isReprocessQueuing()"
      (click)="reprocessFiles()">
      <span class="t-flex t-items-center t-gap-[5px]">
        <span
          venioSvgLoader
          [parentElement]="reprocessButton.element"
          color="#9BD2A7"
          hoverColor="#ffffff"
          applyEffectsTo="fill"
          svgUrl="assets/svg/icon-fulltext-refresh.svg"
          height="18px"
          width="18px"></span>
        REPROCESS
        <kendo-loader
          *ngIf="isReprocessQueuing()"
          type="pulsing"
          themeColor="success" />
      </span>
    </button>
  </div>
</div>
}

<div
  class="t-fixed t-top-[1px] t-left-0 t-w-full t-h-full t-bg-[#212121] t-opacity-10 t-z-[1999]"
  *ngIf="isOverlayActive"
  (click)="isOverlayActive = !isOverlayActive"></div>
<div
  class="t-fixed t-top-[1px] t-w-[56%] t-h-full t-bg-white t-overflow-hidden t-shadow-[0px_20px_16px_6px_rgba(0,0,0,0.212)] t-z-[2000] t-transition-all t-duration-400 t-p-5"
  [ngClass]="{
    't-right-0': isOverlayActive,
    't-right-[-56%]': !isOverlayActive
  }">
  <div class="t-flex t-justify-between t-items-center t-w-full">
    <span
      class="t-inline-flex t-items-center t-gap-3 t-text-primary t-text-lg t-font-semibold">
      <button
        class="t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-h-[35px] t-flex t-items-center"
        fillMode="clear"
        kendoButton
        [imageUrl]="overlayIconUrl"></button>
      {{ overlayTitle }}
    </span>
    <button
      (click)="closeSettingUpdateDialogEvent(null)"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-text-white t-w-6 t-h-6 t-p-0 t-bg-[#ED7425] t-leading-none">
      <kendo-svg-icon [icon]="icons.closeIcon"></kendo-svg-icon>
    </button>
  </div>
  @defer(when activeComponent === 'settings'){
  <venio-case-reprocessing-settings
    *ngIf="activeComponent === 'settings'"
    [reprocessSettings]="currentReprocessSettings"
    (closeSettingUpdateDialog)="
      closeSettingUpdateDialogEvent($event)
    "></venio-case-reprocessing-settings>
  }@placeholder {
  <div class="t-flex t-flex-col t-gap-1">
    <kendo-skeleton
      *ngFor="let n of [1, 2, 3, 4]"
      height="25px"
      width="25px"
      shape="rectangle"
      class="t-rounded-md"></kendo-skeleton>
  </div>
  }

  <div kendoDialogContainer></div>
</div>
