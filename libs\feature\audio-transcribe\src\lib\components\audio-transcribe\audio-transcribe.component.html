<div class="t-flex t-justify-between t-gap-4 t-mb-2 t-mt-2">
  <!-- Back Button -->
  <button
    kendoButton
    [svgIcon]="chevronLeftIcon"
    fillMode="outline"
    class="t-capitalize !t-border-[#ccc]"
    [disabled]="!isHistoryVisible"
    (click)="isHistoryVisible = false"
    *ngIf="isHistoryVisible">
    Back
  </button>
  <span
    class="t-flex t-items-center t-ml-2 t-font-bold"
    *ngIf="isHistoryVisible">
    <span
      venioSvgLoader
      applyEffectsTo="stroke"
      color="#9BD2A7"
      hoverColor="#FFFFFF"
      svgUrl="assets/svg/icon-recently-viewed.svg"
      class="t-inline-block t-align-middle"
      title="History"
      height="1.3rem"
      width="1.3rem"></span>
    <span class="t-ml-1">History</span>
  </span>

  <!-- History Button -->
  <div class="t-flex-grow"></div>
  <button
    kendoButton
    class="v-custom-secondary-button t-w-28 t-flex t-items-center"
    themeColor="secondary"
    fillMode="outline"
    [disabled]="isHistoryVisible"
    (click)="isHistoryVisible = true"
    *ngIf="!isHistoryVisible"
    #historyBtn>
    <span
      venioSvgLoader
      applyEffectsTo="stroke"
      color="#9BD2A7"
      hoverColor="#FFFFFF"
      [parentElement]="historyBtn.element"
      svgUrl="assets/svg/icon-recently-viewed.svg"
      class="t-inline-block t-align-middle"
      title="History"
      height="1.3rem"
      width="1.3rem"></span>
    History
  </button>
</div>

<div *ngIf="!isHistoryVisible">
  <div class="t-flex t-flex-col t-w-full t-mt-3">
    <div class="t-flex t-p-4 t-flex-col t-w-full t-gap-1 t-bg-[#f6f6f6]">
      <div
        class="t-block t-uppercase t-text-[#1EBADC] t-font-semibold t-border-b-[1px] t-border-b-[#cccccc] t-pb-2">
        Summary
      </div>
      <div *ngIf="isGettingFilesForAudioTranscribe | async; else content">
        <kendo-loader></kendo-loader>
      </div>
      <ng-template #content>
        <ul class="t-list-none t-capitalize">
          <li
            class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
            <div class="t-flex t-w-1/3">Total Search Result</div>
            <div class="t-flex t-w-2/3 flex-1">
              {{ audioTranscribeFiles?.totalSearchResultCount ?? 0 }}
            </div>
          </li>
          <li
            class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
            <div class="t-flex t-w-1/3">
              Documents available for transcribing queue
            </div>
            <div class="t-flex t-w-2/3 flex-1">
              {{ audioTranscribeFiles?.applicableQueueCount ?? 0 }}
            </div>
          </li>
          <li
            class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
            <div class="t-flex t-w-1/3">
              Previously added to transcribing queue
            </div>
            <div class="t-flex t-w-2/3 flex-1">
              {{ audioTranscribeFiles?.previousQueueCount ?? 0 }}
            </div>
          </li>
          <li
            class="t-flex t-p-2 t-gap-3 t-border-b-[1px] t-border-b-[#cccccc]">
            <div class="t-flex t-w-1/3">Remaining to be queued</div>
            <div class="t-flex t-w-2/3 flex-1">
              {{ audioTranscribeFiles?.remainingQueueCount ?? 0 }}
            </div>
          </li>

          <li class="t-flex t-px-2 t-gap-3">
            <label
              class="t-flex t-items-center t-min-w-[8.5rem] t-min-h-[2.25rem]">
              <input
                #showAllFilesCheckbox
                type="checkbox"
                kendoCheckBox
                rounded="small"
                size="small"
                [(ngModel)]="isShowAllTranscribeFiles"
                (change)="getFilesForTranscribe()"
                [disabled]="!hasSelectedDocuments" />
              <span
                class="t-pl-2 t-tracking-tight"
                [ngClass]="{
                  't-text-primary t-font-medium': isShowAllTranscribeFiles
                }">
                Show all files for transcribing
              </span>
            </label>
          </li>
        </ul>
      </ng-template>
    </div>

    <div class="t-flex t-flex-col t-gap-3 t-mt-3">
      <div class="t-block t-uppercase t-text-[#1EBADC] t-font-semibold">
        DETAIL INFORMATION FOR DOCUMENTS REMAINING TO BE QUEUED
      </div>
      <div class="t-block">
        <kendo-grid
          class="t-w-full t-max-h-72 v-hide-scrollbar"
          [data]="fileTypeDetails"
          [loading]="isGettingFilesForAudioTranscribe | async"
          [resizable]="true"
          *ngIf="fileTypeDetails?.length">
          <kendo-grid-column field="selected" title="" [width]="50">
            <ng-template kendoGridHeaderTemplate let-column>
              <input
                #headerCheckbox
                kendoCheckBox
                rounded="small"
                size="small"
                type="checkbox"
                [checked]="isHeaderCheckboxChecked"
                (change)="toggleAll($event)" />
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <input
                #rowCheckbox
                kendoCheckBox
                rounded="small"
                size="small"
                type="checkbox"
                [(ngModel)]="dataItem.selected"
                (change)="onRowCheckboxChange(dataItem)" />
            </ng-template>
          </kendo-grid-column>

          <kendo-grid-column
            title="#"
            headerClass="t-text-primary"
            [width]="90"
            [minResizableWidth]="70">
            <ng-template kendoGridHeaderTemplate>
              <span kendoTooltip title="ID">#</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
              <span kendoTooltip title="ID">{{ rowIndex + 1 }}</span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column
            field="fileType"
            title="File Type"
            headerClass="t-text-primary"
            [width]="150"
            [minResizableWidth]="100">
            <ng-template kendoGridHeaderTemplate>
              <span kendoTooltip title="File Type">File Type</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <span kendoTooltip title="{{ dataItem.fileType }}">{{
                dataItem.fileType
              }}</span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column
            field="count"
            title="Count"
            headerClass="t-text-primary"
            [minResizableWidth]="300">
            <ng-template kendoGridHeaderTemplate>
              <span kendoTooltip title="Count">Count</span>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              <span kendoTooltip title="{{ dataItem.count }}">{{
                dataItem.count
              }}</span>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>

        <!-- No Record template -->
        <div
          class="t-grid t-h-28 t-w-full t-place-content-center"
          *ngIf="!fileTypeDetails.length">
          <div class="t-text-center t-text-[#979797]">
            No records available.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!--History-->
<div *ngIf="isHistoryVisible" class="t-w-full t-mt-5">
  <div class="t-block t-w-full t-mt-5 t-mb-4">
    <kendo-dropdownlist
      [data]="userNames"
      [value]="selectedUser"
      [defaultItem]="defaultUser"
      [valuePrimitive]="true"
      (valueChange)="onUserSelectionChange($event)"
      class="t-w-64">
    </kendo-dropdownlist>
  </div>
  <div class="t-block">
    <kendo-grid
      [data]="gridDataJobDetails.data"
      [loading]="isGettingJobDetailsOfAudioTranscribe | async"
      [pageSize]="pageSize"
      [pageable]="{ type: 'numeric', position: 'top' }"
      [sortable]="true"
      [groupable]="false"
      [reorderable]="true"
      [resizable]="true"
      [skip]="skip">
      <ng-template kendoPagerTemplate>
        <div class="t-flex t-w-full t-justify-start t-items-center t-pl-2">
          <button
            kendoButton
            fillMode="outline"
            class="t-p-1"
            size="small"
            kendoTooltip
            title="Refresh"
            (click)="onRefreshClick()">
            <kendo-svgicon
              [icon]="arrowRotateCwIcon"
              [size]="'large'"></kendo-svgicon>
          </button>
        </div>

        <kendo-grid-spacer></kendo-grid-spacer>

        <venio-pagination
          [currentPage]="currentPage"
          [disabled]="gridDataJobDetails?.data?.length === 0"
          [totalRecords]="gridDataJobDetails?.total"
          [pageSize]="pageSize"
          [showPageJumper]="false"
          [showPageSize]="true"
          [showRowNumberInputBox]="true"
          (pageChanged)="pageChanged($event)"
          (pageSizeChanged)="pageSizeChanged($event)"
          class="t-px-5 t-block t-py-2">
        </venio-pagination>
      </ng-template>
      <kendo-grid-column
        field="jobId"
        title="Job ID"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate let-column>
          <span kendoTooltip title="Job ID">Job ID</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="documentCount"
        [width]="190"
        title="No. of Documents"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate let-column>
          <span kendoTooltip title="No. of Documents">No. of Documents</span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="userName"
        title="Requested By"
        [width]="170"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate let-column>
          <span kendoTooltip title="Requested By">Requested By</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <span kendoTooltip [title]="dataItem.userName">
            {{ dataItem.userName }}
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="startTime"
        title="Start Time"
        [width]="200"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate let-column>
          <span kendoTooltip title="Start Time">Start Time</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <span
            kendoTooltip
            [title]="
              dataItem.startTime === 'N/A'
                ? dataItem.startTime
                : (dataItem.startTime | date : 'HH:mm:ss a')
            ">
            <span>{{
              dataItem.startTime === 'N/A'
                ? dataItem.startTime
                : (dataItem.startTime | date : 'HH:mm:ss a')
            }}</span>
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="endTime"
        title="End Time"
        [width]="200"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate let-column>
          <span kendoTooltip title="End Time">End Time</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <span
            kendoTooltip
            [title]="
              dataItem.endTime === 'N/A'
                ? dataItem.endTime
                : (dataItem.endTime | date : 'HH:mm:ss a')
            ">
            <span>{{
              dataItem.endTime === 'N/A'
                ? dataItem.endTime
                : (dataItem.endTime | date : 'HH:mm:ss a')
            }}</span>
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="totalTime"
        title="Total Time"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate let-column>
          <span kendoTooltip title="Total Time">Total Time</span>
        </ng-template>
        <ng-template kendoGridCellTemplate let-dataItem>
          <span kendoTooltip [title]="formatDuration(dataItem.totalTime)">
            {{ formatDuration(dataItem.totalTime) }}
          </span>
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column
        field="status"
        title="Status"
        headerClass="t-text-primary">
        <ng-template kendoGridHeaderTemplate let-column>
          <span kendoTooltip title="Status">Status</span>
        </ng-template>

        <ng-template kendoGridCellTemplate let-dataItem>
          <span
            [ngClass]="{
              't-text-error': dataItem.status === 'Failed',
              't-text-[#FFBB12]': dataItem.status === 'NotStarted',
              't-text-secondary': dataItem.status === 'Completed'
            }"
            [title]="dataItem.status">
            {{ dataItem.status }}
          </span>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</div>
