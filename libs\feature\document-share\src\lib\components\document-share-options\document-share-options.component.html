<form [formGroup]="documentShareForm">
  <div class="t-flex t-flex-row t-items-start t-items-center t-mb-4">
    @if(!sharedDocData()?.isDocShareEdit){
    <div class="t-w-48 t-flex">
      <kendo-textbox
        formControlName="shareName"
        placeholder="Name"
        id="shareName"
        [maxlength]="50"
        data-qa="document-share-name"></kendo-textbox>
      <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
    </div>
    }

    <div
      class="t-flex t-flex-row t-w-64"
      [ngClass]="{
        't-px-4': !sharedDocData()?.isDocShareEdit,
        't-pr-4': sharedDocData()?.isDocShareEdit
      }">
      <kendo-datepicker
        formControlName="sharedExpiryDate"
        id="linkExpiredDate"
        [format]="'MM/dd/yyyy'"
        placeholder="Valid Upto"
        (valueChange)="onExpiryDateChange($event)"
        data-qa="document-share-link-expiry-date"></kendo-datepicker>
      @if(!sharedDocData()?.isDocShareEdit){
      <span class="!t-text-[#ED7425] t-font-bold t-pl-1">*</span>
      }
    </div>

    <div class="t-flex t-items-center t-w-1/3 t-gap-1">
      <input
        type="checkbox"
        formControlName="sendCopyToMe"
        class="t-relative mr-2"
        data-qa="document-share-send-copy-to-me-checkbox"
        kendoCheckBox
        #sendCopyToMeCheckbox />
      <kendo-label
        [for]="sendCopyToMeCheckbox"
        class="t-relative"
        text="Send a copy to me"
        [ngClass]="{
          't-text-primary t-font-medium':
            documentShareForm.get('sendCopyToMe')?.value &&
            !sendCopyToMeCheckbox.disabled,
          'k-disabled': invitationInProgress$ | async,
          '': (invitationInProgress$ | async) === false
        }"
        data-qa="document-share-send-copy-to-me-label"></kendo-label>
    </div>
  </div>
</form>
