<form [formGroup]="downloadForm" class="t-h-full">
  <fieldset class="t-h-full t-pb-1 t-pt-1">
    <div class="t-py-0 t-px-0" #mainComponent>
      <div
        class="t-pb-5 v-custom-grey-bg t-rounded-sm t-flex t-flex-row t-justify-between">
        <div class="t-mt-6 t-w-1/4">
          <kendo-textbox
            class="t-mt-3 !t-border-[#BEBEBE] t-rounded-sm !t-w-full !t-flex"
            id="native-name"
            placeholder="Native download name"
            [title]="'Native download name'"
            formControlName="nativeDownloadName"></kendo-textbox>
          <div class="t-text-error" *ngIf="!hasNativeDownloadName()">
            Please enter valid native download name
          </div>
        </div>

        <div class="t-flex t-pt-3 t-mt-5 t-mb-1">
          <div
            class="t-bg-green-200 t-w-10 custom-svg-line t-bg-no-repeat t-bg-center"></div>
        </div>

        <div class="t-w-1/4">
          <div class="t-mt-[0.5rem] t-m-1">
            <kendo-label
              id="native-name-lbl"
              text="Download"
              class="t-text-sm t-font-medium"></kendo-label>
          </div>
          <div class="t-flex t-mt-[0.5rem] t-m-1 t-items-center">
            <input
              id="download-original"
              size="small"
              type="radio"
              formControlName="downloadOriginal"
              value="DOWNLOAD_ORIGINAL"
              kendoRadioButton
              #downloadOriginalRadio />
            <kendo-label
              id="download-original-lbl"
              class="t-k-radio-label t-px-[5px] t-relative"
              for="download-original"
              [ngClass]="{
                't-text-primary t-font-medium':
                  downloadForm.get('downloadOriginal')?.value ===
                    'DOWNLOAD_ORIGINAL' && !downloadOriginalRadio?.disabled
              }"
              text="Download original">
              <span class="t-text-[#ED7428] t-text-[11px]">*</span>
            </kendo-label>
          </div>
          <div class="t-flex t-items-center t-mt-[0.5rem] t-m-1 t-ml-6">
            <input
              id="download-with-redaction"
              class="custom-checkbox"
              formControlName="downloadWithRedaction"
              type="checkbox"
              kendoCheckBox
              #downloadWithRedactionCheckbox />
            <kendo-label
              class="k-checkbox-label t-top-[1px] t-relative"
              for="download-with-redaction"
              [ngClass]="{
                't-text-primary t-font-medium':
                  downloadForm.get('downloadWithRedaction')?.value &&
                  !downloadWithRedactionCheckbox?.disabled
              }"
              text="With Redaction">
              <span class="t-text-[#ED7428] t-text-xs">(If available)</span>
            </kendo-label>
          </div>
        </div>

        <div class="t-flex t-pt-3 t-mt-5 t-mb-1">
          <div
            class="t-bg-green-200 t-w-10 custom-svg-line t-bg-no-repeat t-bg-center"></div>
        </div>

        <div class="t-mt-5 t-w-1/2">
          <div class="t-flex t-mt-[1rem] t-m-1 t-items-center t-mt-5">
            <input
              id="original-file-name"
              size="small"
              type="radio"
              formControlName="downloadFileName"
              value="ORIGINAL_FILE_NAME"
              kendoRadioButton />
            <kendo-label
              id="original-filename-lbl"
              class="t-k-radio-label t-px-[5px] t-relative"
              for="original-file-name"
              [ngClass]="{
                't-text-primary t-font-medium':
                  this.downloadForm.get('downloadFileName').value ===
                  'ORIGINAL_FILE_NAME'
              }"
              text="Original file name"></kendo-label>
          </div>
          <div class="t-flex t-mt-[0.5rem] t-m-1 t-items-center">
            <input
              id="internal-file-id"
              size="small"
              type="radio"
              formControlName="downloadFileName"
              value="INTERNAL_FILE_ID"
              kendoRadioButton />
            <kendo-label
              id="internale-file-id-lbl"
              class="t-k-radio-label t-px-[5px] t-relative"
              for="internal-file-id"
              [ngClass]="{
                't-text-primary t-font-medium':
                  this.downloadForm.get('downloadFileName').value ===
                  'INTERNAL_FILE_ID'
              }"
              text="Internal file ID"></kendo-label>
          </div>
          <div class="t-flex t-mt-[0.5rem] t-m-1 t-items-center">
            <input
              id="custom-file-name"
              size="small"
              type="radio"
              formControlName="downloadFileName"
              value="CUSTOM_FILE_NAME"
              kendoRadioButton />
            <kendo-label
              id="custom-filename-lbl"
              class="t-k-radio-label t-px-[5px] t-relative"
              for="custom-file-name"
              [ngClass]="{
                't-text-primary t-font-medium':
                  this.downloadForm.get('downloadFileName').value ===
                  'CUSTOM_FILE_NAME'
              }"
              text="Custom"></kendo-label>
          </div>

          <div class="t-flex t-ml-5" formGroupName="customFileNameSettings">
            <div class="t-w-2/5 t-m-1">
              <kendo-textbox
                id="prefix"
                class="!t-border-[#BEBEBE] t-rounded-sm !t-w-full !t-flex"
                placeholder="Prefix"
                [required]="isCustomFileNameSelected()"
                [title]="'Prefix'"
                formControlName="prefix"></kendo-textbox>
              <div
                class="t-text-error"
                *ngIf="
                  downloadForm.get('customFileNameSettings').get('prefix')
                    .invalid
                ">
                Please enter valid prefix
              </div>
            </div>
            <div class="t-w-1/5 t-m-1">
              <kendo-numerictextbox
                class="!t-border-[#BEBEBE] t-rounded-sm"
                id="starting-number"
                placeholder="Starting Number"
                format="#"
                [min]="0"
                [max]="getMaxValue()"
                [maxlength]="getMaxLength()"
                [autoCorrect]="true"
                [spinners]="true"
                [required]="isCustomFileNameSelected()"
                [title]="'Starting Number'"
                formControlName="startingNumber"></kendo-numerictextbox>
              <div
                class="t-text-error"
                *ngIf="
                  downloadForm
                    .get('customFileNameSettings')
                    .get('startingNumber').invalid
                ">
                Please enter valid starting number
              </div>
            </div>
            <div class="t-w-1/5 t-m-1">
              <kendo-numerictextbox
                class="!t-border-[#BEBEBE] t-rounded-sm"
                id="padding"
                placeholder="padding"
                format="#"
                [min]="1"
                [max]="1000"
                [autoCorrect]="true"
                [spinners]="true"
                [required]="isCustomFileNameSelected()"
                (valueChange)="onPaddingChanged($event)"
                [title]="'Padding'"
                formControlName="padding"></kendo-numerictextbox>
              <div
                class="t-text-error"
                *ngIf="
                  downloadForm.get('customFileNameSettings').get('padding')
                    .invalid
                ">
                Please enter valid padding
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </fieldset>
</form>

<ng-template #nativeDownloadActionTemplate>
  <div class="t-flex t-flex-row t-justify-end">
    <button
      id="save"
      class="t-m-1 v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      type="submit"
      kendoButton
      [disabled]="downloadForm.invalid || disableSaveButton"
      (click)="onDeleteClicked()">
      <span class="k-icon k-i-save"></span> Download
    </button>
  </div>
</ng-template>
