import {
  ChangeDetectionStrategy,
  Component,
  ComponentRef,
  computed,
  effect,
  inject,
  input,
  OnInit,
  signal,
  ViewContainerRef,
} from '@angular/core'
import {
  PasswordBankModel,
  PasswordBankFacade,
} from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'
import { ActivatedRoute } from '@angular/router'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { DialogService, DialogRef } from '@progress/kendo-angular-dialog'
import {
  VenioNotificationService,
  ConfirmationDialogComponent,
} from '@venio/feature/notification'
import { Subject, take, takeUntil } from 'rxjs'
import { ImportPasswordBankComponent } from '../import-password-bank/import-password-bank.component'

@Component({
  selector: 'venio-password-management',
  templateUrl: './password-management.component.html',
  styleUrl: './password-management.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PasswordManagementComponent implements OnInit {
  public projectId = input.required<number>()

  private readonly passwordBankFacade = inject(PasswordBankFacade)

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly notificationService = inject(VenioNotificationService)

  private readonly dialogService = inject(DialogService)

  private readonly formBuilder = inject(FormBuilder)

  private vcr = inject(ViewContainerRef)

  private readonly toDestroy$ = new Subject<void>()

  public form: FormGroup

  private confirmationDialogRef: DialogRef

  public deleteSvgUrl = 'assets/svg/icon_material_delete.svg'

  public selectedPasswordIds: number[] = []

  public readonly passwordBanks = toSignal(
    this.passwordBankFacade.selectPasswordBanks$
  )

  public readonly passwordAddResponse = toSignal(
    this.passwordBankFacade.selectAddPasswordResponse$
  )

  public readonly passwordDeleteResponse = toSignal(
    this.passwordBankFacade.selectDeletePasswordResponse$
  )

  public isAddingPassword = signal(false) // to show spinner on add password button

  public deleteInProgress = signal(false) // to show spinner on delete button

  public readonly passwordList = computed<PasswordBankModel[]>(() =>
    this.passwordBanks().filter((p) => !p.isUserIdFile)
  )

  constructor() {
    this.#initForm()
    this.#resetFormAndReenablePasswordInput()
    this.#resetDeleteSpinner()
  }

  #resetFormAndReenablePasswordInput(): void {
    // to reset the form after successful submission
    effect(
      () => {
        const response = this.passwordAddResponse()
        if (response?.status?.toLocaleLowerCase() === 'success') {
          this.form.reset()
        }
        this.isAddingPassword.set(false)
        this.form.get('passwordInput')?.enable()
      },
      { allowSignalWrites: true }
    )
  }

  #resetDeleteSpinner(): void {
    // to stop delete spinner after successful deletion
    effect(
      () => {
        const response = this.passwordDeleteResponse()
        if (response?.message) {
          this.deleteInProgress.set(false)
          this.selectedPasswordIds = []
        }
      },
      { allowSignalWrites: true }
    )
  }

  public ngOnInit(): void {
    this.#fetchPasswordList()
  }

  public openImportPasswordDialog(): void {
    const dialogRef = this.dialogService.open({
      content: ImportPasswordBankComponent,
      cssClass: 'v-dialog-import-password',
      width: '60rem',
      appendTo: this.vcr,
    })
    const instance: ComponentRef<ImportPasswordBankComponent> =
      dialogRef.content
    instance.setInput('isForLotusNotes', false)
    instance.setInput('projectId', this.projectId())
  }

  public addPassword(): void {
    if (!this.form.valid) {
      this.notificationService.showError('Please fill in all required fields.')
      return
    }

    const pwdControl = this.form.get('passwordInput')
    const passwordBankModel: PasswordBankModel = {
      password: pwdControl?.value,
      isUserIdFile: false,
    }
    pwdControl?.disable() // to disable the input field after submission
    this.isAddingPassword.set(true) // to show spinner on add password button
    this.passwordBankFacade.addPassword(
      this.projectId(),
      passwordBankModel,
      undefined
    )
  }

  // handle delete password button on each row click
  public deletePassword(dataItem: any): void {
    this.#confirmDeletePassword([dataItem?.passwordBankId])
  }

  // handle delete passwords button click above the grid
  public deleteMultiplePasswords(): void {
    this.#confirmDeletePassword(this.selectedPasswordIds)
  }

  #initForm(): void {
    this.form = this.formBuilder.group({
      passwordBankId: null,
      passwordInput: [null, Validators.compose([Validators.required])],
    })
  }

  #fetchPasswordList(): void {
    this.passwordBankFacade.fetchPasswordBanks(this.projectId())
  }

  #confirmDeletePassword(deletePasswordIds: number[]): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
      appendTo: this.vcr,
    })
    this.confirmationDialogRef.content.instance.title = 'Delete Confirmation'
    this.confirmationDialogRef.content.instance.message =
      deletePasswordIds.length > 1
        ? 'Are you sure you want to delete multiple passwords?'
        : 'Are you sure you want to delete this password?'

    this.confirmationDialogRef.result
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe((result) => {
        if (typeof result === 'boolean' && result === true) {
          this.deleteInProgress.set(true)
          this.passwordBankFacade.deletePassword(
            this.projectId(),
            deletePasswordIds
          )
        }
      })
  }

  public passwordSelectionChange(passowrdIds: number[]): void {
    this.selectedPasswordIds = passowrdIds
  }
}
