<kendo-treelist
  [kendoTreeListHierarchyBinding]="parentChild"
  childrenField="children"
  scrollable="virtual"
  [loading]="isParentChildLoading$ | async"
  [hasChildren]="hasChildrenNodes"
  kendoTreeListExpandable
  expandBy="fileId"
  [expandedKeys]="expandedKeys"
  [trackBy]="parentChildTreeTrackByFn"
  [pageSize]="15"
  [rowHeight]="36"
  [autoSize]="true"
  [navigable]="true"
  [rowClass]="rowClass"
  [resizable]="true"
  (expand)="fitColumns()"
  (collapse)="fitColumns()"
  [ngClass]="{ 'v-no-data': parentChild?.length === 0 }"
  class="v-custom-family-treelist">
  <kendo-treelist-column
    title="Details"
    [headerClass]="[
      't-text-primary',
      !parentChild?.length ? 't-min-w-[150px]' : ''
    ]"
    [width]="250"
    [minResizableWidth]="100">
    <ng-template kendoTreeListHeaderTemplate let-column>
      <span kendoTooltip title="Details">Details</span>
    </ng-template>
    <ng-template kendoTreeListCellTemplate let-dataItem>
      <span
        class="t-font-bold t-cursor-pointer hover:t-text-[#1EBADC]"
        (click)="onDetailsClicked(dataItem)"
        ><kendo-svgicon [icon]="icons.eyeIcon"></kendo-svgicon
      ></span>
    </ng-template>
  </kendo-treelist-column>
  <kendo-treelist-column *ngFor="let field of headers" field="{{ field }}">
    <ng-template kendoTreeListHeaderTemplate let-column>
      <span kendoTooltip title="{{ field }}">{{ field }}</span>
    </ng-template>
    <ng-template kendoTreeListCellTemplate let-dataItem>
      <span kendoTooltip title="{{ dataItem[field] }}">{{
        dataItem[field]
      }}</span>
    </ng-template>
  </kendo-treelist-column>
  <ng-template kendoTreeListNoRecordsTemplate>
    <div
      *ngIf="(isParentChildLoading$ | async) === false && !parentChild?.length"
      class="t-flex t-h-min t-w-full">
      <span class="t-text-[#000000BC] t-text-[16px]">No records found</span>
    </div>
  </ng-template>
</kendo-treelist>
