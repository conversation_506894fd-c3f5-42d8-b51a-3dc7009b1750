<div
  kendoPopoverContainer
  #container="kendoPopoverContainer"
  [popover]="shortCodePopover">
  <button
    kendoButton
    class="t-mr-3 t-m-0 t-max-h-[34px] t-p-2 !t-border-[#BEBEBE] !t-rounded hover:!t-bg-[#1EBADC] hover:!t-border-[#FFFFFF]"
    #popoverAnchor
    kendoTooltip
    title="Keyboard Shortcut"
    (click)="togglePopover()"
    fillMode="none">
    <span
      [parentElement]="popoverAnchor.element"
      venioSvgLoader
      hoverColor="#FFFFFF"
      svgUrl="assets/svg/icon-floating-keyboard-mini.svg"
      height="1.1rem"
      width="2.0rem">
    </span>
  </button>
</div>

<kendo-popover #shortCodePopover [width]="480" position="bottom">
  <ng-template kendoPopoverBodyTemplate>
    <div
      class="t-flex t-flex-col t-p-2 t-w-full"
      [ngClass]="{ 'v-shortcut-popover-home': type === 'home' }">
      <div class="t-flex t-w-full t-w-full t-font-semibold t-text-primary">
        <div class="t-flex t-pb-1 t-w-1/5" *ngIf="userAgent !== 'macOS'">
          Windows
        </div>
        <div class="t-flex t-pb-1 t-w-1/3" *ngIf="userAgent !== 'Windows'">
          MAC
        </div>
        <div
          class="t-flex t-flex-1 t-w-9/12 t-pb-1 t-pl-4 t-border-l-[1px] t-border-[#cccccc]">
          Action Performed
        </div>
      </div>

      <ng-container *ngFor="let shortcut of selectedShortcuts()">
        <div class="t-flex t-w-full">
          <div
            *ngIf="userAgent !== 'macOS'"
            class="t-flex t-font-semibold t-w-1/5 t-text-secondary t-pt-1">
            {{ shortcut.windows }}
          </div>
          <div
            *ngIf="userAgent !== 'Windows'"
            class="t-flex t-font-semibold t-text-secondary t-w-1/3 t-pt-1">
            {{ shortcut.mac }}
          </div>
          <div
            class="t-flex t-flex-1 t-capitalize t-pt-1 t-w-9/12 t-pl-4 t-border-l-[1px] t-border-[#cccccc]">
            {{ shortcut.action }}
          </div>
        </div>
      </ng-container>
    </div>
  </ng-template>
</kendo-popover>
