<!-- custom multivalues dialog-->

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [minWidth]="300"
  [width]="'33%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}

      <kendo-popover #toolTipInfo>
        <ng-template kendoPopoverBodyTemplate>
          <div class="t-flex t-w-full">
            <div class="t-flex t-flex-col t-gap-1">
              <div class="t-flex">
                Field Type: {{ selectedField?.uiInputType }}
              </div>
              <div class="t-flex" *ngIf="selectedField?.length > 0">
                Length:
                <span class="t-font-bold">{{ selectedField?.length }}</span>
              </div>
              <div class="t-flex" *ngIf="selectedField?.scale">
                Scale:
                <span class="t-font-bold">{{ selectedField?.scale }}</span>
              </div>
              <div
                class="t-flex"
                *ngIf="selectedField?.allowMultipleCodingValues">
                Delimiter:
                <span class="t-font-bold">{{
                  selectedField?.delimiterForCodingValues
                }}</span>
              </div>
              <div class="t-flex" *ngIf="selectedField.description">
                Description: {{ selectedField?.description }}
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-popover>

      <button
        kendoButton
        showOn="hover"
        class="!t-p-0"
        kendoPopoverAnchor
        [popover]="toolTipInfo"
        fillMode="clear"
        [svgIcon]="infoTooltip"
        data-qa="tooltipInfo"></button>
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-flex-col">
    <div class="t-flex t-gap-5">
      <div class="t-flex t-flex-1 t-flex-col t-w-full">
        <div class="t-flex t-flex-col">
          <div class="t-flex t-w-full t-flex-col t-gap-2">
            <div class="t-flex t-flex-1 t-mt-3 t-w-full v-custom-grey-bg">
              <form
                *ngIf="multiValueForm"
                [formGroup]="multiValueForm"
                class="t-flex t-flex-1 t-w-full t-gap-3 t-flex-wrap">
                <div class="t-flex t-mt-2 t-w-full t-flex-col">
                  <div class="t-flex t-flex-col t-w-full t-gap-2">
                    <div
                      class="t-flex t-flex-wrap t-gap-3"
                      *ngIf="!isBulkDocument">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-w-full t-gap-1 t-flex-col">
                        <kendo-label
                          for="curValue"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          Current Values <span class="t-text-error">*</span>
                        </kendo-label>
                        <ng-container
                          *ngIf="
                            selectedField?.detailDataCount > 0 &&
                            selectedField?.allowMultipleCodingValues
                          ">
                          <div *ngFor="let value of selectedValues">
                            <div class="t-p-1.5 t-px-2.5">
                              {{ value }}
                            </div>
                          </div>
                        </ng-container>

                        <div
                          *ngIf="
                            !(
                              selectedField.detailDataCount > 0 &&
                              selectedField.allowMultipleCodingValues
                            )
                          ">
                          <div class="t-p-1.5 t-px-2.5">
                            {{ selectedField.currentFieldValue }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div
                      class="t-flex t-flex-wrap t-mt-2 t-gap-3"
                      *ngIf="
                        (isAddOrAppendValues && newValueType !== 'checkbox') ||
                        newValueType === 'text-checkbox'
                      ">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-w-full t-gap-1 t-flex-col">
                        <kendo-label
                          for="{{ newValuesCaption }}"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          {{ newValuesCaption }}
                        </kendo-label>

                        <div *ngIf="newValueType === 'text-checkbox'">
                          <kendo-textarea
                            [rows]="1"
                            [(ngModel)]="updateValues"
                            [ngModelOptions]="{
                              standalone: true
                            }"></kendo-textarea>
                        </div>
                        <div *ngIf="newValueType === 'text'">
                          <kendo-textarea
                            [rows]="1"
                            [formControlName]="
                              selectedField.fieldName
                            "></kendo-textarea>
                        </div>
                        <div *ngIf="newValueType === 'select'">
                          <kendo-dropdownlist
                            [data]="selectedField.fieldCodingValues"
                            [formControlName]="selectedField.fieldName">
                          </kendo-dropdownlist>
                        </div>
                      </div>
                    </div>

                    <div
                      class="t-flex t-flex-wrap t-mt-2 t-gap-3"
                      *ngIf="
                        newValueType === 'checkbox' ||
                        newValueType === 'text-checkbox'
                      ">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-w-full t-gap-1 t-flex-col">
                        <kendo-label
                          for="predefined"
                          class="t-text-xs t-uppercase t-tracking-widest">
                          Predefined Coding Values
                        </kendo-label>

                        <div
                          class="t-flex t-flex-row-reverse t-gap-1 t-justify-end t-mt-2 t-items-center"
                          *ngFor="let value of selectedField.fieldCodingValues">
                          <kendo-label
                            class="k-checkbox-label"
                            [for]="value"
                            [ngClass]="{
                              't-text-primary t-font-medium':
                                multiValueForm.get(value)?.value
                            }"
                            >{{ value }}</kendo-label
                          >
                          <input
                            data-qa="check"
                            type="checkbox"
                            [formControlName]="value"
                            (change)="onCodingValueChanged($event)"
                            [value]="value"
                            kendoCheckBox />
                        </div>
                      </div>
                    </div>

                    <div
                      class="t-flex t-flex-wrap t-mt-1 t-gap-3"
                      *ngIf="selectedField.allowMultipleCodingValues">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-w-full t-gap-1 t-flex-col">
                        <ul
                          class="k-radio-list k-list-vertical t-bg-white t-mt-1 t-border t-border-[#979797] t-px-3 t-py-2">
                          <li
                            class="k-radio-item"
                            *ngFor="
                              let value of multipleCodingSettings;
                              let i = index
                            ">
                            <input
                              type="radio"
                              data-qa="value"
                              kendoRadioButton
                              [id]="value"
                              formControlName="updateType"
                              [value]="i.toString()" />
                            <kendo-label [for]="value">{{ value }}</kendo-label>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div
                      class="t-flex t-flex-wrap t-mt-2 t-gap-3"
                      *ngIf="shouldShowDelimiter">
                      <div
                        showHints="always"
                        class="t-flex t-flex-0 t-basis-[90%] t-gap-1 t-flex-col">
                        <span class="t-text-sm"
                          >Multiple values will be seperated by "{{
                            selectedDelimiter
                          }}"</span
                        >
                      </div>
                    </div>

                    <div
                      class="t-flex t-flex-wrap t-mt-4 t-gap-3"
                      *ngIf="isAddOrAppendValues">
                      <div class="t-flex t-mt-2">
                        <span class="t-text-sm"
                          >Note: Bulk update value is only updating after
                          reloading the review page.</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="save()"
        class="v-custom-secondary-button"
        themeColor="secondary"
        fillMode="outline"
        data-qa="save">
        {{ submitCaption }}
      </button>
      <button
        data-qa="cancel"
        kendoButton
        (click)="close('yes')"
        fillMode="outline"
        themeColor="dark">
        CANCEL
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
