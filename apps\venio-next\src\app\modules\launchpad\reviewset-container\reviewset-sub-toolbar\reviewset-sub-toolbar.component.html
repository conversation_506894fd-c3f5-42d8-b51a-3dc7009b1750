<div
  class="t-flex t-flex-row t-justify-between t-items-center t-bg-[#faf9f8] t-pl-4 t-py-2">
  <kendo-textbox
    #searchControl
    (keydown.enter)="searchValueChange(searchControl.value)"
    (valueChange)="searchValueChange($event)"
    class="!t-border-[#ccc] !t-w-[25rem]"
    kendoTooltip
    title="Search Review Sets"
    placeholder="Search"
    [clearButton]="true">
    <ng-template kendoTextBoxSuffixTemplate>
      <button
        [disabled]="isReviewSetLoading() || !searchControl.value?.trim()"
        (click)="searchValueChange(searchControl.value)"
        kendoButton
        fillMode="clear"
        class="t-text-[#1EBADC]"
        imageUrl="assets/svg/icon-updated-search.svg"></button>
    </ng-template>
  </kendo-textbox>

  <venio-pagination
    (pageChanged)="pagingControlChange($event)"
    (pageSizeChanged)="pagingControlChange($event)"
    [disabled]="isReviewSetLoading()"
    [totalRecords]="totalRecords()"
    [pageSize]="pageSize()"
    [showPageJumper]="false"
    [showPageSize]="true"
    [showRowNumberInputBox]="true"
    class="t-px-5 t-block t-py-2">
  </venio-pagination>
</div>
