import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnDestroy,
  OnInit,
  runInInjectionContext,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownsModule,
  MultiSelectComponent,
  RemoveTagEvent,
} from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { distinctUntilChanged, filter, Subject, takeUntil } from 'rxjs'
import { ProjectFacade } from '@venio/data-access/common'
import { CheckBoxModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ReportsFacade } from '@venio/data-access/reports'
import { ReportTypes } from '@venio/shared/models/constants'
import { CaseModel } from '@venio/data-access/review'

@Component({
  selector: 'venio-report-project-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    IconsModule,
    CheckBoxModule,
    LabelModule,
  ],
  templateUrl: './report-project-dropdown.component.html',
  styleUrl: './report-project-dropdown.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportProjectDropdownComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly projectFacade = inject(ProjectFacade)

  private readonly reportFacade = inject(ReportsFacade)

  private injector = inject(Injector)

  @ViewChild(MultiSelectComponent)
  public multiSelect: MultiSelectComponent

  public readonly filteredProjects = signal<CaseModel[]>([])

  public readonly projects = signal<CaseModel[]>([])

  public readonly isAllProjectChecked = signal<boolean>(false)

  public readonly selectedProjects = signal<number[]>([])

  public readonly selectedReportType = signal<ReportTypes>(undefined)

  public readonly chevronDownIcon = chevronDownIcon

  public projectSelectionChange(projectIds: number[]): void {
    this.selectedProjects.set(projectIds)
    if (this.selectedProjects().length) {
      this.isAllProjectChecked.set(false)
    }
  }

  public allProjectSelectionChange(checked: boolean): void {
    this.isAllProjectChecked.set(checked)
    if (checked) {
      this.selectedProjects.set([])
    } else {
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.userID !== -1
      )
    }
  }

  public tagMapper = (items: CaseModel[]): CaseModel[] | CaseModel[][] => {
    if (this.isAllProjectChecked()) {
      // if there is a default value or all projects, which in this case, should be shown as well.
      return [
        {
          projectId: -1,
          projectName: 'All Projects',
        } as CaseModel,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public ngOnInit(): void {
    this.#selectSelectedReportType()
    this.#fetchProjects()
    this.#selectUsers()
    this.#storeSelectedUsers()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public filterProjects(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredProjects.set(
      this.projects().filter((user) =>
        user.projectName.toLowerCase().includes(filterValue)
      )
    )
  }

  public chevDownIconClick(projectSelection: MultiSelectComponent): void {
    projectSelection.toggle()
    projectSelection.focus()
  }

  public handleAllProjectClick(
    event: MouseEvent,
    input: HTMLInputElement
  ): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allProjectSelectionChange(input.checked)
  }

  public removeTag(event: RemoveTagEvent): void {
    if ((event.dataItem as CaseModel).projectId === -1) {
      this.isAllProjectChecked.set(false)
    }
  }

  #fetchProjects(): void {
    this.projectFacade.fetchProjects()
  }

  #setProjects(): void {
    this.selectedProjects.set([])
    this.isAllProjectChecked.set(true)

    this.filteredProjects.set(this.projects())
  }

  #selectUsers(): void {
    this.projectFacade.selectProject$
      .pipe(
        filter((projects) => Boolean(projects)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((projects) => {
        this.projects.set(projects)
        this.#setProjects()
      })
  }

  #storeSelectedUsers(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          const isAll = this.isAllProjectChecked()
          const projectIds = isAll
            ? this.projects().map((p) => p.projectId)
            : this.selectedProjects()
          this.reportFacade.storeSelectedProjects(projectIds)
        },
        { allowSignalWrites: true }
      )
    )
  }

  #selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(
        distinctUntilChanged(),
        filter((type) => Boolean(type)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((type) => {
        this.selectedReportType.set(type)
        this.#setProjects()
      })
  }
}
