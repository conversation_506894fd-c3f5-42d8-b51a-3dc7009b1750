<form
  class="t-flex t-flex-col"
  [formGroup]="loginFormGroup"
  (keydown.enter)="loginClick()">
  <div class="t-mb-4 t-flex t-flex-col t-items-start">
    <kendo-textbox
      #username
      formControlName="username"
      placeholder="Username"
      class="t-w-full t-rounded"
      style="box-shadow: 0 1px 2px 0 rgba(55, 65, 81, 0.08)"></kendo-textbox>
    <div
      class="t-accent-error t-text-error t-m-1 t-text-[11.23px]"
      *ngIf="
        (!formControls.username.untouched &&
          formControls.username.dirty &&
          formControls.username.invalid) ||
        (!formControls.username.untouched &&
          formControls.username.dirty &&
          formControls.username.value?.trim() === '')
      ">
      {{
        formControls.username.hasError('required')
          ? 'Username is required'
          : 'Please enter a valid username'
      }}
    </div>
  </div>

  <div class="t-mb-4">
    <kendo-textbox
      formControlName="password"
      placeholder="Password"
      [type]="togglePasswordVisibility() ? 'text' : 'password'"
      class="t-w-full t-rounded v-input-shadow">
      <ng-template kendoTextBoxSuffixTemplate>
        <button
          [tabIndex]="-1"
          kendoButton
          type="button"
          class="t-pr-2"
          look="clear"
          themeColor="none"
          fillMode="clear"
          (click)="passwordVisibilityClicked($event)">
          <kendo-svgicon
            [icon]="
              togglePasswordVisibility() ? iconEye : iconSlashEye
            "></kendo-svgicon>
        </button>
      </ng-template>
    </kendo-textbox>
    <div
      class="t-accent-error t-text-error t-m-1 t-text-[11.23px]"
      *ngIf="
        (!formControls.password.untouched &&
          formControls.password.dirty &&
          formControls.password.errors !== null) ||
        (!formControls.password.untouched &&
          formControls.password.dirty &&
          formControls.password.value?.trim() === '')
      ">
      {{
        formControls.password.hasError('required')
          ? 'Password is required'
          : 'Please enter a valid password'
      }}
    </div>
  </div>
  <div
    *ngIf="loginServerMessage()"
    class="t-flex t-items-center t-grow t-justify-center t-text-center t-mb-2.5 t-accent-error t-text-error t-text-[12px]">
    {{ loginServerMessage() }}
  </div>
  <!-------------------------------------------------->
  <div
    *ngIf="isTwoFactorAuthenticationNotificationSent()"
    class="t-mb-1 t-flex t-flex-col t-items-start">
    <p class="t-text-sm t-text-[#354099] t-font-semibold t-mb-2">
      {{ verificationCodeSentNotificationMessage() }}
    </p>
    <div class="t-w-full t-mb-0 t-flex t-flex-col t-items-start">
      <kendo-textbox
        #verificationCode
        formControlName="verificationCode"
        placeholder="Verification Code"
        class="t-w-full t-rounded active:!t-drop-shadow hover:!t-drop-shadow"
        style="box-shadow: 0 1px 2px 0 rgba(55, 65, 81, 0.08)"></kendo-textbox>
    </div>
    <div
      class="t-accent-error t-text-error t-m-1 t-text-[11.23px]"
      *ngIf="
        (!formControls.verificationCode.untouched &&
          formControls.verificationCode.dirty &&
          formControls.verificationCode.errors !== null) ||
        (!formControls.verificationCode.untouched &&
          formControls.verificationCode.dirty &&
          formControls.verificationCode.value?.trim() === '')
      ">
      {{
        formControls.verificationCode.hasError('required')
          ? 'Verification code is required'
          : 'Please enter a valid verification code'
      }}
    </div>
    <div class="t-flex t-items-center t-justify-between t-mb-4 t-mt-2">
      <div class="t-flex t-items-center">
        <input
          #rememberCode
          type="checkbox"
          kendoCheckBox
          formControlName="remember2FA"
          class="t-mr-2" />
        <kendo-label
          [for]="rememberCode"
          class="t-text-xs t-text-[#181818] t-font-medium"
          text="Do not ask on this computer for next 30 days" />
      </div>
    </div>
  </div>
  <div
    *ngIf="verificationFailedMessage()"
    class="t-flex t-flex-col t-items-start t-grow t-justify-center t-text-center t-mb-2.5 t-accent-error t-text-error t-text-[12px]">
    <div
      [innerHTML]="verificationFailedMessage()"
      class="t-text-[#ED7425] t-font-semibold t-mb-2"></div>
    <div
      *ngIf="!isUserLocked()"
      class="t-font-medium t-text-sm t-text-[#ED7425] t-mt-1 t-mb-3">
      Click here to

      <button
        class="t-font-black t-text-[#A54608]"
        (click)="resendVerificationCode()">
        resend
      </button>
      the code
    </div>
  </div>
  <span
    *ngIf="
      isTwoFactorAuthenticationNotificationSent() &&
      !verificationFailedMessage()
    "
    class="t-font-normal t-text-sm t-mt-1 t-mb-3"
    >Didn't receive the verification code?
    <button
      class="t-font-normal t-text-[#21857E]"
      (click)="resendVerificationCode()">
      Resend Code
    </button>
  </span>

  <!-------------------------------------------------->
  <div
    [ngClass]="{
      '!t-justify-end': isTwoFactorAuthenticationNotificationSent()
    }"
    class="t-flex t-items-center t-justify-between t-mb-4">
    <div
      class="t-flex t-items-center"
      *ngIf="!isTwoFactorAuthenticationNotificationSent()">
      <input
        #remember
        type="checkbox"
        kendoCheckBox
        formControlName="rememberMe"
        class="t-mr-2" />
      <kendo-label
        [for]="remember"
        class="t-text-xs t-text-primary t-font-medium"
        text="Remember me" />
    </div>
    <button
      type="button"
      [tabIndex]="-1"
      (click)="openForgotPasswordDialog()"
      class="t-text-xs t-text-[#1EBADC] t-font-medium t-shadow-none">
      Forgot Password?
    </button>
  </div>
  <div
    class="t-relative t-block t-w-full t-z-0"
    #responseMessageContainer></div>
  <div>
    <button
      type="button"
      kendoButton
      class="t-bg-[#9BD2A7] t-border-0 t-text-white t-w-full t-py-2 t-px-4 t-mb-3 t-rounded-lg t-drop-shadow-md t-font-sans t-text-sm"
      (click)="loginClick()"
      [disabled]="isLoginLoading()">
      <kendo-loader
        *ngIf="isLoginLoading()"
        themeColor="success"
        type="pulsing" />
      Login
    </button>
  </div>

  <div class="t-flex t-items-center t-justify-between t-mb-6 t-mt-4">
    <label class="t-flex t-items-center">
      <span class="t-text-xs t-font-sans t-font-medium">
        {{ appVersion() }}
      </span>
    </label>
    <button
      type="button"
      [tabIndex]="-1"
      class="t-text-xs t-text-primary t-font-medium"
      (click)="openPasswordResetDialog()">
      Reset Password
    </button>
  </div>

  <div
    *ngIf="showAzureLoginOption || showOktaLoginOption"
    class="t-relative t-flex t-items-center t-justify-center t-w-full t-mb-4">
    <hr class="t-w-full t-border t-border-[#1EBADC]" />
    <span
      class="t-absolute t-text-xs t-px-2 t-py-[2px] t-bg-[#1EBADC] t-text-[#FFFFFF] t-z-10 t-rounded"
      >OR</span
    >
  </div>
  <div class="t-w-full t-flex t-items-center t-justify-center t-gap-4 t-mt-6">
    <button
      *ngIf="showAzureLoginOption"
      type="button"
      class="t-border-[0.5px] t-border-[#F1511B] t-rounded-[3px] t-p-[5px]"
      (click)="redirectToIdPLogin()">
      <span
        class="t-w-full"
        venioSvgLoader
        svgUrl="assets/svg/microsoft.svg"
        height="28px"
        width="28px">
      </span>
    </button>
    <button
      *ngIf="showOktaLoginOption"
      type="button"
      class="t-border-[0.5px] t-border-[#0A7EC2] t-rounded-[3px] t-p-[5px] t-h-[39.45px]"
      (click)="redirectToIdPLogin()">
      <span
        class="t-w-full"
        venioSvgLoader
        svgUrl="assets/svg/icon-login-auth-okta.svg">
      </span>
    </button>
  </div>
</form>
