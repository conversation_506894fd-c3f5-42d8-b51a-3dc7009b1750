<div class="t-flex t-justify-between t-p-[0.44rem] t-items-center">
  <div class="t-w-1/2">
    <div
      *ngIf="
        redactedOCRStatus === 0 ||
        redactedOCRStatus === -1 ||
        redactedOCRStatus === 1 ||
        generatedOCRStatus === 0 ||
        generatedOCRStatus === -1 ||
        generatedOCRStatus === 1
      ">
      <div>
        <div class="v-ocr-warning-alert">
          <ng-container
            *ngIf="
              generatedOCRStatus === 0 ||
                generatedOCRStatus === -1 ||
                generatedOCRStatus === 1;
              then generatedWarning;
              else redactedWarning
            "></ng-container>
          <ng-template #generatedWarning
            >Document is in queue for generated image ocr.</ng-template
          >
          <ng-template #redactedWarning
            >Document is in queue for redacted image ocr.</ng-template
          >
          <button kendoButton class="t-m-1" (click)="refreshViewer()">
            Refresh
          </button>
        </div>
      </div>
    </div>
    <form [formGroup]="fulltextTypeForm">
      <div class="t-flex t-gap-2">
        <div class="t-flex">
          <input
            type="radio"
            value="1"
            formControlName="fulltextType"
            #extracted
            kendoRadioButton />
          <kendo-label
            class="k-radio-label t-relative"
            [for]="extracted"
            [ngClass]="{
              't-text-primary t-font-medium':
                fulltextTypeForm.get('fulltextType')?.value === '1' &&
                !extracted?.disabled
            }"
            text="Extracted"></kendo-label>
        </div>
        <div class="t-flex" *ngIf="showGeneratedTiffOcr">
          <input
            type="radio"
            value="2"
            formControlName="fulltextType"
            #generated
            kendoRadioButton />
          <kendo-label
            class="k-radio-label t-relative"
            [for]="generated"
            [ngClass]="{
              't-text-primary t-font-medium':
                fulltextTypeForm.get('fulltextType')?.value === '2' &&
                !generated?.disabled
            }"
            text="Generated"></kendo-label>
        </div>
        <div class="t-flex" *ngIf="showRedactedTiffOcr">
          <input
            type="radio"
            value="3"
            formControlName="fulltextType"
            #redacted
            kendoRadioButton />
          <kendo-label
            class="k-radio-label t-relative"
            [for]="redacted"
            [ngClass]="{
              't-text-primary t-font-medium':
                fulltextTypeForm.get('fulltextType')?.value === '3' &&
                !redacted?.disabled
            }"
            text="Redacted"></kendo-label>
        </div>
      </div>
    </form>
  </div>
  <div class="t-flex" kendoTooltip>
    <button
      kendoButton
      #navFirst
      class="!t-p-[0.3rem]"
      fillMode="clear"
      size="none"
      title="First"
      *ngIf="showNavigationButton"
      (click)="onActionClick('highlight-first')">
      <span
        [parentElement]="navFirst.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        title="First"
        [svgUrl]="'assets/svg/icon-navigator-first.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>
    <button
      kendoButton
      #navPrev
      class="!t-p-[0.3rem]"
      fillMode="clear"
      size="none"
      title="Previous"
      *ngIf="showNavigationButton"
      (click)="onActionClick('highlight-prev')">
      <span
        [parentElement]="navPrev.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-navigator-previous.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>
    <button
      kendoButton
      #navNext
      class="!t-p-[0.3rem]"
      fillMode="clear"
      size="none"
      title="Next"
      *ngIf="showNavigationButton"
      (click)="onActionClick('highlight-next')">
      <span
        [parentElement]="navNext.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-navigator-next.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>
    <button
      kendoButton
      #navLast
      class="!t-p-[0.3rem]"
      fillMode="clear"
      size="none"
      title="Last"
      *ngIf="showNavigationButton"
      (click)="onActionClick('highlight-last')">
      <span
        [parentElement]="navLast.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-navigator-last.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>
    <button
      kendoButton
      #fulltextRefresh
      class="!t-p-[0.3rem]"
      fillMode="clear"
      size="none"
      title="Refresh"
      (click)="onActionClick('refresh')">
      <span
        [parentElement]="fulltextRefresh.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-preview-refresh.svg'"
        height="1rem"
        width="1.3rem"></span>
    </button>

    <button
      kendoButton
      #fulltextSearch
      class="!t-p-[0.3rem]"
      fillMode="clear"
      title="Search"
      size="none"
      (click)="onActionClick('search-term')">
      <span
        [parentElement]="fulltextSearch.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-fulltext-search.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>

    <button
      kendoButton
      #fulltextHighlightGroup
      *ngIf="highlightGroupSetting?.isHighlightGroupEnabled"
      class="!t-p-[0.3rem]"
      fillMode="clear"
      title="Highlight Group"
      size="none"
      (click)="onActionClick('highlight-group')">
      <span
        [parentElement]="fulltextHighlightGroup.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-highlight-group.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>

    <button
      kendoButton
      #fulltextFields
      class="!t-p-[0.3rem]"
      fillMode="clear"
      size="none"
      title="Show Fields"
      (click)="onActionClick('show-fields')">
      <span
        [parentElement]="fulltextFields.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-show-fields.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>

    <button
      kendoButton
      #parentDownload
      *venioHasUserGroupRights="UserRights.ALLOW_TO_DOWNLOAD_FULLTEXT"
      class="!t-p-[0.3rem]"
      fillMode="clear"
      size="none"
      title="Download"
      (click)="onActionClick('download')">
      <span
        [parentElement]="parentDownload.element"
        venioSvgLoader
        hoverColor="#FFBB12"
        color="#979797"
        [svgUrl]="'assets/svg/icon-fulltext-download.svg'"
        height="1.3rem"
        width="1.3rem"></span>
    </button>
  </div>
</div>
<div
  class="t-flex t-flex-col t-border t-border-l-0 t-border-r-0 t-border-b-0 t-border-t-1 t-border-[#dbdbdb] t-p-4">
  <div class="t-flex t-flex-col t-max-h-[10rem] t-overflow-auto">
    <div *ngFor="let data of selectedMetadata">
      <div class="t-flex">
        <span>{{ data.key }}:</span><span>{{ data.value }}</span>
      </div>
    </div>
  </div>

  <div
    *ngIf="!errorMessage"
    #container
    libTextTermHighlighter
    libHighlightNavigator
    class="viewer-panel"
    id="fulltextContainer"
    infiniteScroll
    [infiniteScrollDistance]="1"
    [infiniteScrollThrottle]="50"
    [scrollWindow]="false"></div>
  <ng-template #placeholder></ng-template>
  <div class="v-warning-alert mb-0" *ngIf="errorMessage">
    <div class="d-flex justify-content-between">
      <div class="mr-3">{{ errorMessage }}</div>
      <!-- <div *ngIf="showRetryButton">
        <button class="btn btn-sm btn-secondary" (click)="onRetry()">
          Retry
        </button>
      </div> -->
    </div>
  </div>

  <kendo-loader *ngIf="showSpinner" size="medium" type="pulsing"></kendo-loader>
</div>
<kendo-popup
  *ngIf="this.textTermHighlighterProperties?.showTextSearch"
  [anchor]="fulltextSearch"
  [anchorAlign]="{ horizontal: 'right', vertical: 'bottom' }"
  [popupAlign]="{ horizontal: 'right', vertical: 'top' }"
  [collision]="collision"
  class="v-find-panel">
  <lib-text-search></lib-text-search>
</kendo-popup>
<kendo-popup
  [hidden]="!this.showHighlightGroup"
  [anchor]="fulltextSearch"
  [anchorAlign]="{ horizontal: 'right', vertical: 'bottom' }"
  [popupAlign]="{ horizontal: 'right', vertical: 'top' }"
  [collision]="collision"
  class="v-find-panel">
  <ng-template #highlightGroupComponent></ng-template>
</kendo-popup>
<div kendoDialogContainer></div>
