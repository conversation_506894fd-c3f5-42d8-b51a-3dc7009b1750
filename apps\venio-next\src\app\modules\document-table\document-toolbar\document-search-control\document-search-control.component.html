<ng-container [formGroup]="searchFormGroup">
  <div class="t-flex t-w-full t-gap-2">
    <div class="t-w-full t-h-full">
      <kendo-textarea
        flow="horizontal"
        resizable="none"
        formControlName="searchExpression"
        (paste)="handlePaste($event)"
        (keydown)="keypressAction($event)"
        [rows]="1"
        #searchInput
        class="!t-border-[#ccc] !t-w-full t-min-h-[2.25rem] v-list-search-textarea"
        placeholder="Enter search terms">
        <kendo-textarea-suffix>
          <kendo-loader
            kendoTooltip
            title="Validating search syntax..."
            *ngIf="isSyntaxValidating()"
            size="medium"
            themeColor="light"
            class="t-mr-1"></kendo-loader>

          <button
            kendoTooltip
            kendoButton
            [disabled]="this.isSyntaxValidating()"
            title="Clear Search Term"
            class="t-h-full !t-px-1"
            fillMode="flat"
            size="small"
            themeColor="secondary"
            *ngIf="searchExpressionControl?.value?.trim()"
            (click)="actionClick('CLEAR_INPUT')"
            [svgIcon]="clearIcon"></button>
          <button
            *ngIf="isAiSearchEnabled()"
            kendoButton
            kendoTooltip
            title="AI Assistant"
            data-qa="AI Assistant"
            fillMode="clear"
            (click)="actionClick('LAUNCH_AI_SEARCH')"
            class="t-h-full t-w-[2.4rem] t-p-0 t-m-0 focus:t-bg-white">
            <span
              class="t-inline-block t-scale-[2] t-translate-y-[5px] t-translate-x-[13px]"
              venioSvgLoader
              svgUrl="assets/svg/icon-ai-smooth-handmade-stars.svg"
              height="1.5rem"
              width="1.5rem"></span>
          </button>
          <button
            #searchBtn
            [disabled]="(isSearchLoading | async) || this.isSyntaxValidating()"
            kendoButton
            class="t-h-full !t-px-3"
            fillMode="flat"
            size="small"
            kendoTooltip
            [title]="
              searchExpressionControl.value?.trim()
                ? 'Search'
                : 'Reload Documents'
            "
            (click)="
              actionClick(
                searchExpressionControl.value?.trim()
                  ? 'SEARCH'
                  : 'RESET_SEARCH'
              )
            ">
            <span
              venioSvgLoader
              applyEffectsTo="fill"
              [color]="searchBtn.disabled ? '#b9b9b9' : '#484848'"
              [svgUrl]="
                searchExpressionControl.value?.trim()
                  ? 'assets/svg/icon-search.svg'
                  : 'assets/svg/icon-grid-action-refresh.svg'
              "
              height="1rem"
              width="1rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
        </kendo-textarea-suffix>
      </kendo-textarea>
    </div>

    <label
      [ngClass]="{
        't-text-primary t-font-medium': searchFormGroup.get('includePC')?.value
      }"
      class="t-border t-bg-white t-border-[#BEBEBE] t-py-1 t-px-2 t-flex t-items-center t-min-w-[8.5rem] t-min-h-[2.25rem] t-rounded-[4px] hover:!t-border-[#1EBADC] hover:!t-bg-[#1EBADC] hover:!t-text-[#FFFFFF]">
      <input
        type="checkbox"
        kendoCheckBox
        formControlName="includePC"
        rounded="small"
        size="small" />
      <span class="t-pl-1 t-text-sm t-tracking-tight">Include Family</span>
    </label>
  </div>
</ng-container>
