import { Injectable, inject } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import * as CreateSlipSheetAction from './create-slipsheet.actions'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { concatMap, of, withLatestFrom, switchMap, map, catchError } from 'rxjs'
import {
  CreateSlipsheetModel,
  DocumentTableSelectionModel,
  FieldModel,
  ResponseMessage,
  SlipsheetSummaryModel,
  SlipsheetTemplateModel,
} from '../../../models/interfaces'
import { ReviewParamService } from '../../../services'
import { SearchFacade } from '../../search'
import { DocumentsFacade } from '../../documents'
import { ConvertDocumentFacade } from '../convert-document.facade'
import { CreateSlipsheetFacade } from './create-slipsheet.facade'
import { CreateSlipsheetService } from '../../../services/convert-document/create-slipsheet.service'
import { HttpErrorResponse } from '@angular/common/http'

@Injectable()
export class CreateSlipsheetEffects {
  private actions$ = inject(Actions)

  private reviewParamService = inject(ReviewParamService)

  private documentsFacade = inject(DocumentsFacade)

  private searchFacade = inject(SearchFacade)

  private convertDocumentFacade = inject(ConvertDocumentFacade)

  private createSlipsheetFacade = inject(CreateSlipsheetFacade)

  private createSlipsheetService = inject(CreateSlipsheetService)

  /*
   * Effect for getting slipsheet summary
   */
  public getSlipsheetSummary$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CreateSlipSheetAction.fetchSlipsheetSummary),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.reviewParamService.projectId,
            this.searchFacade.getSearchTempTables$,
            this.documentsFacade.getIsBatchSelected$,
            this.documentsFacade.getSelectedDocuments$,
            this.documentsFacade.getUnselectedDocuments$
          )
        )
      ),
      switchMap(
        ([
          action,
          projectId,
          tempTables,
          isBatchSelected,
          selectedDocs,
          unSelectedDocs,
        ]) => {
          const payload: DocumentTableSelectionModel = {
            selectedFileIds: selectedDocs,
            unSelectedFileIds: unSelectedDocs,
            isBatchSelected: isBatchSelected,
            searchTempTable: tempTables.searchResultTempTable,
          }

          return this.createSlipsheetService
            .fetchSlipsheetSummary$<ResponseModel>(projectId, payload)
            .pipe(
              map((res: ResponseModel) => {
                const summary: SlipsheetSummaryModel = res.data
                return CreateSlipSheetAction.setSlipSheetSummary({
                  payload: { summary },
                })
              }),
              catchError((error: unknown) => {
                return [
                  CreateSlipSheetAction.fetchSlipsheetSummaryFailed({
                    payload: { error: error },
                  }),
                ]
              })
            )
        }
      )
    )
  )

  /**
   * Effect for getting slipsheet templates
   */
  public getSlipsheetTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CreateSlipSheetAction.fetchSlipsheetTemplateForConversion),
      switchMap(() =>
        this.createSlipsheetService
          .fetchSlipsheetTemplates$<ResponseModel>()
          .pipe(
            map((res: ResponseModel) => {
              const templates: SlipsheetTemplateModel[] = res.data
              return CreateSlipSheetAction.setSlipsheetTemplateForConversion({
                payload: { templates },
              })
            }),
            catchError((error: unknown) => {
              return [
                CreateSlipSheetAction.fetchSlipsheetTemplateForConversionFailed(
                  {
                    payload: { error: error },
                  }
                ),
              ]
            })
          )
      )
    )
  )

  /**
   * Effect for getting slipsheet fields
   */
  public getSlipsheetFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CreateSlipSheetAction.fetchSlipSheetFieldForConversion),
      concatMap((action) =>
        of(action).pipe(withLatestFrom(this.reviewParamService.projectId))
      ),
      switchMap(([action, projectId]) =>
        this.createSlipsheetService
          .fetchSlipsheetFields$<ResponseModel>(projectId)
          .pipe(
            map((res: ResponseModel) => {
              const slipSheetFields: FieldModel[] = res.data
              return CreateSlipSheetAction.setSlipSheetFieldForConversion({
                payload: { slipSheetFields },
              })
            }),
            catchError((error: unknown) => {
              return [
                CreateSlipSheetAction.fetchSlipSheetFieldForConversionFailed({
                  payload: { error: error },
                }),
              ]
            })
          )
      )
    )
  )

  /**
   * Effect to create slipsheet
   */
  public createSlipsheet$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CreateSlipSheetAction.createSlipsheet),
      concatMap((action) =>
        of(action).pipe(
          withLatestFrom(
            this.reviewParamService.projectId,
            this.searchFacade.getSearchTempTables$,
            this.documentsFacade.getIsBatchSelected$,
            this.documentsFacade.getSelectedDocuments$,
            this.documentsFacade.getUnselectedDocuments$
          )
        )
      ),
      switchMap(
        ([
          action,
          projectId,
          tempTables,
          isBatchSelected,
          selectedDocs,
          unSelectedDocs,
        ]) => {
          const createSlipsheetModel: CreateSlipsheetModel = {
            slipsheetPayload: action.payload.slipsheetModel,
            documentOptions: {
              selectedFileIds: selectedDocs,
              unSelectedFileIds: unSelectedDocs,
              isBatchSelected: isBatchSelected,
              searchTempTable: tempTables.searchResultTempTable,
            },
          }
          return this.createSlipsheetService
            .createSlipsheet$<ResponseModel>(projectId, createSlipsheetModel)
            .pipe(
              map((res: ResponseModel) => {
                // empty message is sent when slipsheet is created successfully
                if (res.message === '') {
                  const msg =
                    'The slipsheet job has been queued for selected documents.'
                  this.convertDocumentFacade.setResponseMessage({
                    success: true,
                    message: msg,
                  })
                  return CreateSlipSheetAction.createSlipsheetSuccess({
                    payload: {
                      message: msg,
                    },
                  })
                }

                //set error message
                this.convertDocumentFacade.setResponseMessage({
                  success: false,
                  message: res.message,
                })
                return CreateSlipSheetAction.createSlipsheetFailed({
                  payload: { error: res.message },
                })
              }),
              catchError((error: unknown) => {
                if (error instanceof HttpErrorResponse) {
                  const errorResponse: HttpErrorResponse = error
                  const responseMessage: ResponseMessage = {
                    success: false,
                    message: errorResponse.error.message,
                  }
                  this.convertDocumentFacade.setResponseMessage(responseMessage)
                  return [
                    CreateSlipSheetAction.createSlipsheetFailed({
                      payload: { error: error },
                    }),
                  ]
                }
              })
            )
        }
      )
    )
  )

  /**
   * Effect for getting slipsheet image preview
   */
  public getImagePreview$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CreateSlipSheetAction.fetchImagePreview),
      switchMap((action) =>
        this.createSlipsheetService
          .fetchSlipsheetImagePreview$<ResponseModel>(
            action.payload.slipsheetModel
          )
          .pipe(
            map((res: ResponseModel) => {
              const base64Image: string = res.data
              return CreateSlipSheetAction.setImagePreviewData({
                payload: { base64Image },
              })
            }),
            catchError((error: unknown) => {
              return [
                CreateSlipSheetAction.fetchImagePreviewFailed({
                  payload: { error: error },
                }),
              ]
            })
          )
      )
    )
  )
}
