<kendo-multiselect
  [filterable]="true"
  [clearButton]="false"
  [virtual]="true"
  [listHeight]="300"
  [checkboxes]="true"
  [autoClose]="false"
  [tagMapper]="tagMapper"
  [value]="selectedUsers()"
  #userSelection
  (filterChange)="filterUsers($event)"
  (valueChange)="userSelectionChange($event)"
  class="t-min-w-[18rem] t-max-w-[18rem] v-custom-multiselect-auto-w"
  [data]="filteredUsers()"
  (removeTag)="removeTag($event)"
  [valuePrimitive]="true"
  valueField="userID"
  textField="userName">
  <ng-template kendoMultiSelectHeaderTemplate let-dataItem>
    <div
      class="t-flex t-p-2 t-items-center t-h-full t-w-full t-relative t-z-50"
      (click)="handleAllUserClick($event, allUser)">
      <input
        [checked]="isAllUserChecked()"
        (click)="handleAllUserClick($event, allUser)"
        (change)="allUserSelectionChange($event.target['checked'])"
        type="checkbox"
        #allUser
        id="all-user"
        kendoCheckBox />
      <kendo-label
        (click)="handleAllUserClick($event, allUser)"
        class="k-checkbox-label t-w-full t-h-full"
        for="all-user"
        text="All Users"></kendo-label>
    </div>
  </ng-template>
  <ng-template kendoSuffixTemplate>
    <kendo-svg-icon
      class="t-cursor-pointer t-w-[19px] t-h-[19px] t-text-[#333333]"
      (click)="chevDownIconClick(userSelection)"
      [icon]="chevronDownIcon"></kendo-svg-icon>
  </ng-template>
  <ng-template kendoMultiSelectGroupTagTemplate let-dataItems>
    {{ dataItems.length }} user(s) selected
  </ng-template>
</kendo-multiselect>
