<div #containerElement class="t-flex-1 t-relative">
  <kendo-grid
    class="t-w-full t-min-h-[20rem] t-grid t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
    [data]="gridView()"
    venioDynamicHeight
    [extraSpacing]="20"
    [loading]="isSharedDocLoading()"
    [skip]="skip()"
    [pageSize]="pageSize()"
    [sortable]="true"
    [sort]="sort"
    [groupable]="false"
    [reorderable]="false"
    [resizable]="true"
    [trackBy]="caseTrackByFn"
    (pageChange)="handlePagingForVirtualScroll($event)"
    scrollable="virtual"
    kendoGridSelectBy="DocumentShareID">
    <ng-template kendoGridNoRecordsTemplate>
      <p *ngIf="!isSharedDocLoading()">No records found</p>
    </ng-template>
    <kendo-grid-column
      field="sn"
      [width]="45"
      title="#"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
        {{ rowIndex + 1 }}
      </ng-template>

      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Row Number"
          >#</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="shareName"
      title="Name"
      [sortable]="true"
      headerClass="t-text-primary"
      [width]="100"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Name"
          >Name</span
        >
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [sortable]="false"
      field="fileCountInFolder"
      [width]="80"
      title="Documents"
      [filterable]="false"
      headerClass="t-text-primary">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Documents"
          >Documents</span
        >
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      [sortable]="false"
      field="sharedExpiryDate"
      title="Expire Date"
      [width]="110"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Expire Date"
          >Expire Date</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        <span class="t-text-xs t-text-[#A7A9AA]">
          {{ dataItem?.sharedExpiryDate | date : 'MM dd yyyy' }}
          <span class="t-ml-2"></span>
          {{ dataItem?.sharedExpiryDate | date : 'hh:mm:ss a' }}
        </span>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="allowToAddDocumentNotes"
      title="Add Notes"
      [sortable]="true"
      headerClass="t-text-primary"
      [width]="80"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Add Notes"
          >Add Notes</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        @if(dataItem?.allowToAddDocumentNotes){

        <kendo-svg-icon
          [icon]="icons?.checkIcon"
          themeColor="success"></kendo-svg-icon
        >}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="allowToTagUntag"
      title="Tag/Untag"
      [sortable]="true"
      headerClass="t-text-primary"
      [width]="80"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Tag/Untag"
          >Tag/Untag</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        @if(dataItem?.allowToTagUntag){
        <kendo-svg-icon
          [icon]="icons?.checkIcon"
          themeColor="success"></kendo-svg-icon
        >}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      field="isWrite"
      title="Analyze"
      [sortable]="true"
      [width]="80"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Analyze"
          >Analyze</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        @if(dataItem?.isWrite){
        <kendo-svg-icon
          [icon]="icons?.checkIcon"
          themeColor="success"></kendo-svg-icon
        >}
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      field="shareInstruction"
      title="Instruction"
      [width]="130"
      [sortable]="true"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Instruction"
          >Instruction</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        <span
          class="t-text-xs t-text-[#999999]"
          *ngIf="dataItem?.shareInstruction"
          [innerHTML]="sanitizeShareInstruction(dataItem.shareInstruction)">
        </span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column
      [sortable]="false"
      field="expired"
      [width]="110"
      title="Created By & On"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Created By & On"
          >Created By & On</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-flex-col">
          <p class="t-capitalize t-text-[14px] t-text-[#5E6366]">
            {{ dataItem.sharedByFullName }}
          </p>
          <span class="t-text-xs t-text-[#A7A9AA]">
            {{ dataItem.sharedOn | date : 'MM dd yyyy' }}
            {{ dataItem.sharedOn | date : 'hh:mm:ss a' }}</span
          >
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      title="Actions"
      [width]="100"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Actions"
          >Actions</span
        >
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <ng-template #actionPlaceholder>
          <div class="t-flex t-flex-row t-gap-2">
            <kendo-skeleton
              *ngFor="let n of [1, 2, 3, 4]"
              height="25px"
              width="25px"
              shape="rectangle"
              class="t-rounded-md" />
          </div>
        </ng-template>
        @defer {
        <venio-shared-document-grid-actions
          [rowDataItem]="dataItem"
          (actionInvoked)="forwardActionControlClick($event, dataItem)" />
        } @placeholder {
        <ng-container *ngTemplateOutlet="actionPlaceholder" />
        }
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
