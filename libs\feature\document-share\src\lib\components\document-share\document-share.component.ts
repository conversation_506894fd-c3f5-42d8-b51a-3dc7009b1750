import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Signal,
  signal,
} from '@angular/core'
import { DialogRef } from '@progress/kendo-angular-dialog'
import {
  DocumentShareModel,
  DocumentsFacade,
  SearchFacade,
  DocumentShareFacade,
  SharedDocRequestType,
  SharedDocumentDetailModel,
} from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'
import { Subject, combineLatest, filter, take, takeUntil } from 'rxjs'
import { FormGroup } from '@angular/forms'
import { toSignal } from '@angular/core/rxjs-interop'
import { ResponseModel } from '@venio/shared/models/interfaces'
import {
  DocumentShareDialogModel,
  DocumentShareFormModel,
  SharedDocInjectModel,
} from '../../models/document-share.models'
import { DocumentShareFormService } from '../../services/document-share-form.service'
import {
  NotificationService,
  Type as NotificationType,
} from '@progress/kendo-angular-notification'
import { ActivatedRoute } from '@angular/router'
import dayjs from 'dayjs'

@Component({
  selector: 'venio-document-share',
  templateUrl: './document-share.component.html',
  styleUrl: './document-share.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentShareComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @Input() public documentShareDialogData: DocumentShareDialogModel

  private readonly toDestroy$ = new Subject<void>()

  private readonly notificationService = inject(NotificationService)

  public selectedSharedDoc: SharedDocumentDetailModel

  private internalSelectedRowsCount: number

  private externalSelectedRowsCount: number

  private documentSharedID: number

  public get documentShareForm(): FormGroup {
    return this.documentShareFormService.documentShareForm
  }

  public documentShareOptionsComponent = import(
    '../document-share-options/document-share-options.component'
  ).then(({ DocumentShareOptionsComponent }) => DocumentShareOptionsComponent)

  public userOptionsComponent = import(
    '../user-options/user-options.component'
  ).then(({ UserOptionsComponent }) => UserOptionsComponent)

  public documentShareInstructionComponent = import(
    '../document-share-instruction/document-share-instruction.component'
  ).then(
    ({ DocumentShareInstructionComponent }) => DocumentShareInstructionComponent
  )

  public documentShareOptionsData = signal<SharedDocInjectModel | null>(null)

  public selectUpdatedDocumentResponse = toSignal(
    this.documentShareFacade.selectUpdatedDocumentResponse$,
    { initialValue: null }
  )

  public invitationInProgress$ =
    this.documentShareFacade.getInvitationInProgressFlag$

  public isReharedDisabled: Signal<boolean>

  public isUnshareDisabled: Signal<boolean>

  public selectedDocuments: number | null = null

  private globalTempTable: string

  private isBatchSelected: boolean

  private selectedDocs: number[]

  private unselectedDocs: number[]

  constructor(
    private documentShareFormService: DocumentShareFormService,
    private documentShareFacade: DocumentShareFacade,
    private noticationService: VenioNotificationService,
    private documentFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    public dialog: DialogRef,
    private cdr: ChangeDetectorRef,
    private activatedRoute: ActivatedRoute
  ) {
    this.isReharedDisabled = this.documentShareFormService.isReshareBtnDisabled
    this.isUnshareDisabled = this.documentShareFormService.isUnsharedBtnDisabled
    effect(() => {
      this.internalSelectedRowsCount =
        this.documentShareFormService.getInternalSelectedRowsCount()

      this.externalSelectedRowsCount =
        this.documentShareFormService.getExternalSelectedRowsCount()
    })
  }

  public ngAfterViewInit(): void {
    // disable external users related fields until the user selects the 'Share to External Users' checkbox
    this.documentShareForm.get('allowToTag').disable()
    this.documentShareForm.get('allowToAddNotes').disable()
    this.documentShareForm.get('allowToViewAnalyzePage').disable()
    this.documentShareForm.get('allowRedaction').disable()
    this.documentShareForm.get('newEmail')?.disable()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.documentShareFacade.isEditMode = false
    this.documentShareFormService.resetForm()
    if (!this.documentShareDialogData?.isDocShareEdit) {
      this.documentShareFacade.resetDocumentShareState()
    } else {
      this.documentShareFormService.toggleResharedBtn(false)
      this.documentShareFormService.toggleUnsharedBtn(false)
    }
  }

  private get projectId(): number | undefined {
    const id = this.activatedRoute?.snapshot?.queryParams['projectId']
    return id ? +id : undefined
  }

  public ngOnInit(): void {
    // this.documentShareFacade.resetDocumentShareState();

    if (!this.documentShareDialogData?.isDocShareEdit) {
      if (this.projectId) {
        this.documentShareFacade.fetchDocumentShareUsersInternal(this.projectId)
        this.documentShareFacade.fetchDocumentShareUsersExternal(this.projectId)
      }

      this.getTotalSelectedDocumentCount()

      this.documentShareForm.get('shareName').setValue(String(Date.now()))
      this.documentShareForm
        .get('sharedExpiryDate')
        .setValue(new Date(Date.now()))
    } else {
      this.#updateSelectedDocDataforChild()
    }
    // subscribe to user message
    this.documentShareFacade.getUserMessage$
      .pipe(
        filter((msg): boolean => !!msg && !!msg.message),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response): void => {
        if (response.success) {
          this.noticationService.showSuccess(response.message)
          this.dialog.close()
        } else {
          this.noticationService.showError(response.message)
        }
        this.documentShareFacade.setUserMessage(null, false)
        this.cdr.detectChanges()
      })
  }

  #updateSelectedDocDataforChild(): void {
    this.documentShareFacade.selectSharedDocumentDetail$
      .pipe(
        filter((res: ResponseModel) => !!res?.data),
        takeUntil(this.toDestroy$)
      )
      .subscribe((value) => {
        this.#handleSharedDocumentResponse(value)
      })
  }

  #handleSharedDocumentResponse(value: ResponseModel): void {
    this.selectedSharedDoc = value?.data
    this.#setDocumentShareOptionsData(this.selectedSharedDoc)
    this.#initializeEditFlow(this.selectedSharedDoc)
  }

  #setDocumentShareOptionsData(selectedDoc: SharedDocumentDetailModel): void {
    const sharedDocData = {
      isDocShareEdit: true,
      shareDocData: selectedDoc,
    }
    this.documentShareOptionsData.set(sharedDocData)
  }

  #initializeEditFlow(selectedDoc: SharedDocumentDetailModel): void {
    this.checkAndInitializeEditFlow(selectedDoc)
    this.#toggleDocumentShareButtons(false)
  }

  #toggleDocumentShareButtons(state: boolean): void {
    this.documentShareFormService.toggleResharedBtn(state)
    this.documentShareFormService.toggleUnsharedBtn(state)
  }

  public getTotalSelectedDocumentCount(): void {
    combineLatest([
      this.documentFacade.getIsBatchSelected$,
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getUnselectedDocuments$,
      this.searchFacade.getSearchInitialParameters$,
    ])
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(
        ([
          isBatchSelected,
          selectedDocs,
          unselectedDocs,
          searchParams,
        ]): void => {
          this.globalTempTable = searchParams?.globalTempTableName
          this.selectedDocs = selectedDocs
          this.unselectedDocs = unselectedDocs
          this.isBatchSelected = isBatchSelected

          if (isBatchSelected) {
            this.selectedDocuments =
              searchParams.totalHitCount - unselectedDocs.length
          } else {
            this.selectedDocuments = selectedDocs.length
          }
        }
      )
  }

  public onSendInvitation(): void {
    this.documentShareFacade.setInvitationInProgressFlag(true)
    const documentShareFormModel = this.documentShareFormService.getFormValue()
    if (!this.validateDocumentShareForm(documentShareFormModel)) {
      this.documentShareFacade.setInvitationInProgressFlag(false)
      return
    }

    const payload: DocumentShareModel = {
      allowToAddDocumentNotes: documentShareFormModel.allowToAddNotes,
      allowToApplyRedaction: documentShareFormModel.allowRedaction,
      allowToTagUntag: documentShareFormModel.allowToTag,
      carbonCopySender: documentShareFormModel.sendCopyToMe,
      projectId: this.projectId,
      sharedExpiryDate: this.#formatSharedExpiryDate(
        documentShareFormModel.sharedExpiryDate
      ),
      shareName: documentShareFormModel.shareName,
      sharePermission: documentShareFormModel.allowToViewAnalyzePage
        ? 'Full Rights'
        : 'Read Only',
      sharedBy: -1,
      sharedExtUserInfo: documentShareFormModel.shareToExternalUsers
        ? documentShareFormModel.externalUsers
        : [],
      sharedInstructionMsg: `<pre> ${documentShareFormModel.instruction} </pre>`,
      sharedUserInfo: documentShareFormModel.internalUsers,

      /**
       * documentSelectionType:
       * 0 means all documents except unselected onces
       * 1 means selected documents only
       */
      documentSelectionType: this.isBatchSelected ? 0 : 1,
      sharedDocumentIds: this.isBatchSelected
        ? this.serializeNumberArray(this.unselectedDocs)
        : this.serializeNumberArray(this.selectedDocs),
      searchGlobalTempTableName: this.globalTempTable,
      remainingDaysUntilExpiry: null,
      externalUsers: null,
      internalUsers: null,
      isWrite: null,
      sharedOn: null,
      documentShareID: null,
    }
    this.documentShareFacade.shareDocument(this.projectId, payload)
    // this.dialog.close()
  }

  private serializeNumberArray(arr: number[]): string {
    if (!arr || arr.length === 0) return ''
    return arr.join(',')
  }

  private validateDocumentShareForm(
    documentShare:
      | DocumentShareModel
      | SharedDocumentDetailModel
      | DocumentShareFormModel
  ): boolean {
    // Validate share name
    if (!documentShare?.shareName?.trim()) {
      return this.setErrorMessage('Please enter share name.')
    }

    // Validate expiry date
    const differenceInDays = Math.max(
      dayjs(documentShare?.sharedExpiryDate).diff(dayjs(), 'day'),
      0
    )

    if (differenceInDays > this.documentShareFormService.MAX_LINK_EXPIRY_DAYS) {
      return this.setErrorMessage(
        `Link expiry days should not be more than ${this.documentShareFormService.MAX_LINK_EXPIRY_DAYS} days.`
      )
    }

    if (dayjs(documentShare?.sharedExpiryDate).isSame(dayjs(), 'day')) {
      return this.setErrorMessage(`Please select future date for expiry date.`)
    }

    // Validate external users

    if (
      !this.documentShareDialogData?.isDocShareEdit &&
      (documentShare as DocumentShareFormModel)?.shareToExternalUsers &&
      (!documentShare?.externalUsers ||
        documentShare?.externalUsers.length === 0)
    ) {
      return this.setErrorMessage('Please select at least one external user.')
    }

    // Validate internal users
    if (
      !this.documentShareDialogData?.isDocShareEdit &&
      !(documentShare as DocumentShareFormModel)?.shareToExternalUsers &&
      (!documentShare?.internalUsers ||
        documentShare?.internalUsers.length === 0)
    ) {
      return this.setErrorMessage('Please select at least one internal user.')
    }

    return true
  }

  private setErrorMessage(message: string): boolean {
    this.documentShareFacade.setUserMessage(message, false)
    return false
  }

  public onCancelAction(): void {
    this.documentShareFormService.resetForm()
    this.dialog.close()
  }

  private checkAndInitializeEditFlow(data: SharedDocumentDetailModel): void {
    this.documentShareFormService.initializeFormForEdit(data)
    this.cdr.detectChanges()
  }

  /**
   * On Click of Rehare Button Following block will execute
   * @returns
   */

  public reShare(): void {
    const documentShareFormModel = this.#buildDocumentShareFormModel()

    if (!this.#isValidDocumentShareForm(documentShareFormModel)) {
      this.#handleInvalidForm()
      return
    }

    const formattedModel = this.#prepareDocumentShareFormModel(
      documentShareFormModel
    )

    this.#updateDocumentExpiryDate(formattedModel)
  }

  #buildDocumentShareFormModel(): SharedDocumentDetailModel | any {
    return {
      ...this.selectedSharedDoc,
      ...this.documentShareFormService.getFormValue(
        this.documentShareDialogData?.isDocShareEdit
      ),
    }
  }

  #isValidDocumentShareForm(model: SharedDocumentDetailModel): boolean {
    return this.validateDocumentShareForm(model)
  }

  #handleInvalidForm(): void {
    this.documentShareFacade.setInvitationInProgressFlag(false)
  }

  #prepareDocumentShareFormModel(
    model: SharedDocumentDetailModel
  ): SharedDocumentDetailModel {
    return {
      ...model,
      sharedExpiryDate: this.#formatSharedExpiryDate(model?.sharedExpiryDate),
    }
  }

  #updateDocumentExpiryDate(model: SharedDocumentDetailModel): void {
    this.documentShareFacade.updateExpiryDate(
      this.documentShareDialogData?.projectId,
      model?.documentShareID,
      model
    )

    this.#subscribeToUpdateResponse()
  }

  #subscribeToUpdateResponse(): void {
    this.documentShareFacade.selectUpdatedDocumentResponse$
      .pipe(
        filter((res: ResponseModel) => !!res?.data),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res: ResponseModel) => {
        this.#handleResponse(res)
      })
  }

  #handleResponse(res: ResponseModel): void {
    if (res?.status === 'Success') {
      this.#showNotification(res?.message, { style: 'success' })
      this.onCancelAction()
      this.documentShareFacade.fetchSharedDocuments(
        -1,
        SharedDocRequestType.All
      )
    } else {
      this.#showNotification(res?.message, { style: 'error' })
    }
  }

  /**
   *
   * On Unshare butn click following Code will execute
   */

  public unShare(): void {
    const documentShareFormModel: SharedDocumentDetailModel =
      this.prepareDocumentShareFormModel()

    if (!this.validateSelectedRows(documentShareFormModel)) {
      return
    }

    this.documentSharedID = documentShareFormModel.documentShareID
    this.documentShareFacade.unShareDocument(
      this.documentShareDialogData?.projectId,
      documentShareFormModel.documentShareID,
      documentShareFormModel
    )

    this.handleUnShareResponse(documentShareFormModel)
  }

  private prepareDocumentShareFormModel(): SharedDocumentDetailModel | any {
    const baseFormModel = {
      ...this.selectedSharedDoc,
      ...this.documentShareFormService.getFormValue(
        this.documentShareDialogData?.isDocShareEdit
      ),
    }

    const formattedModel = {
      ...baseFormModel,
      sharedExpiryDate: this.#formatSharedExpiryDate(
        baseFormModel?.sharedExpiryDate
      ),
      internalUsers:
        this.documentShareFormService.getUpdatedInternalUsers() ?? [],
      externalUsers:
        this.documentShareFormService.getUpdatedExternalUsers() ?? [],
    }
    return formattedModel
  }

  private validateSelectedRows(
    documentShareFormModel: SharedDocumentDetailModel
  ): boolean {
    const hasValidInternalUsers =
      documentShareFormModel.internalUsers.length > 0
    const hasValidExternalUsers =
      documentShareFormModel.externalUsers.length > 0

    return hasValidInternalUsers || hasValidExternalUsers
  }

  private handleUnShareResponse(
    documentShareFormModel: SharedDocumentDetailModel
  ): void {
    this.documentShareFacade.selectUnsharedDocumentResponse$
      .pipe(
        filter(
          (res: ResponseModel) =>
            res?.status === 'Success' || res?.status === 'Error'
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((res) => {
        if (res?.status === 'Success') {
          this.handleSuccessResponse(documentShareFormModel, res.message)
        } else {
          this.handleErrorResponse()
        }
      })
  }

  private handleSuccessResponse(
    documentShareFormModel: SharedDocumentDetailModel,
    message: string
  ): void {
    this.#showNotification(message, { style: 'success' })

    const allInternalUsersSelected =
      documentShareFormModel.internalUsers.length ===
      this.internalSelectedRowsCount

    const allExternalUsersSelected =
      !documentShareFormModel.externalUsers.length ||
      documentShareFormModel.externalUsers.length ===
        this.externalSelectedRowsCount

    if (allInternalUsersSelected && allExternalUsersSelected) {
      this.documentShareFacade.fetchSharedDocuments(
        -1,
        SharedDocRequestType.All
      )
      this.onCancelAction()
    } else {
      this.updateDocumentDetails()
    }
  }

  private handleErrorResponse(): void {
    this.documentShareFacade.fetchSharedDocuments(-1, SharedDocRequestType.All)
    this.onCancelAction()
  }

  private updateDocumentDetails(): void {
    this.documentShareFormService.resetForm()
    this.documentShareFacade.fetchSharedDocumentDetail(
      this.documentShareDialogData?.projectId,
      this.documentSharedID
    )
    this.#updateSelectedDocDataforChild()
  }

  #formatSharedExpiryDate(date: Date | string): string {
    const sharedExpiryDate = dayjs(date)
    const formattedDate = sharedExpiryDate.format('MM-DD-YYYY')
    return formattedDate
  }

  #showNotification(content: string, type: NotificationType): void {
    this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })
  }
}
