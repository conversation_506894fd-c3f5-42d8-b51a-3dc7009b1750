import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnDestroy,
  OnInit,
  runInInjectionContext,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownsModule,
  MultiSelectComponent,
  RemoveTagEvent,
} from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { distinctUntilChanged, filter, Subject, takeUntil } from 'rxjs'
import { UserFacade } from '@venio/data-access/common'
import { UserModel } from '@venio/shared/models/interfaces'
import { CheckBoxModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { ReportsFacade } from '@venio/data-access/reports'
import { ReportTypes } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-report-user-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    IconsModule,
    CheckBoxModule,
    LabelModule,
  ],
  templateUrl: './report-user-dropdown.component.html',
  styleUrl: './report-user-dropdown.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportUserDropdownComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly userFacade = inject(UserFacade)

  private readonly reportFacade = inject(ReportsFacade)

  private injector = inject(Injector)

  @ViewChild(MultiSelectComponent)
  public multiSelect: MultiSelectComponent

  public readonly filteredUsers = signal<UserModel[]>([])

  public readonly users = signal<UserModel[]>([])

  public readonly isAllUserChecked = signal<boolean>(true)

  public readonly selectedUsers = signal<number[]>([])

  public readonly selectedReportType = signal<ReportTypes>(undefined)

  public readonly chevronDownIcon = chevronDownIcon

  public userSelectionChange(userIds: number[]): void {
    this.selectedUsers.set(userIds)
    if (this.selectedUsers().length) {
      this.isAllUserChecked.set(false)
    }
  }

  public allUserSelectionChange(checked: boolean): void {
    this.isAllUserChecked.set(checked)
    if (checked) {
      this.selectedUsers.set([])
    } else {
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.userID !== -1
      )
    }
  }

  public tagMapper = (items: UserModel[]): UserModel[] | UserModel[][] => {
    if (this.isAllUserChecked()) {
      // if there is a default value or all users, which in this case, should be shown as well.
      return [
        {
          userId: -1,
          userName: 'All Users',
        } as UserModel,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public ngOnInit(): void {
    this.#selectSelectedReportType()
    this.#fetchUsers()
    this.#selectUsers()
    this.#storeSelectedUsers()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public filterUsers(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredUsers.set(
      this.users().filter((user) =>
        user.userName.toLowerCase().includes(filterValue)
      )
    )
  }

  public chevDownIconClick(userSelection: MultiSelectComponent): void {
    userSelection.toggle()
    userSelection.focus()
  }

  public handleAllUserClick(event: MouseEvent, input: HTMLInputElement): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allUserSelectionChange(input.checked)
  }

  public removeTag(event: RemoveTagEvent): void {
    if (event.dataItem.userID === -1) {
      this.isAllUserChecked.set(false)
    }
  }

  #fetchUsers(): void {
    this.userFacade.fetchUserList()
  }

  #setFilteredUsers(type: ReportTypes): void {
    this.selectedUsers.set([])
    this.isAllUserChecked.set(true)

    switch (type) {
      case ReportTypes.LOG_IN_OUT_REPORTS:
      case ReportTypes.CREATION_AND_DEACTIVATION_REPORTS:
      case ReportTypes.ROLE_CHANGE_REPORTS:
      case ReportTypes.PROJECT_ACCESS_REPORT:
      case ReportTypes.ACTIVITY_REPORT:
        this.filteredUsers.set(this.users())
        break
      case ReportTypes.LOCKED_USERS_REPORTS:
        this.filteredUsers.set(this.users().filter((user) => user.isUserLocked))
        break
      case ReportTypes.MATTER_DETAIL_REPORT:
        break
    }
  }

  #selectUsers(): void {
    this.userFacade.selectUserListSuccessResponse$
      .pipe(
        filter((response) => Boolean(response?.data)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.users.set(response.data)
        // this.filteredUsers.set(response.data)
        this.#setFilteredUsers(this.selectedReportType())
      })
  }

  #storeSelectedUsers(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          this.reportFacade.storeSelectedUsers(this.selectedUsers())
        },
        { allowSignalWrites: true }
      )
    )
  }

  #selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(
        distinctUntilChanged(),
        filter((type) => Boolean(type)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((type) => {
        this.selectedReportType.set(type)
        this.#setFilteredUsers(type)
      })
  }
}
