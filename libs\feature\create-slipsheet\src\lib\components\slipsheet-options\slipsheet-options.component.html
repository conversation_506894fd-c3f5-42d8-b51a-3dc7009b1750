<form [formGroup]="slipsheetForm" class="t-mt-2">
  <div>
    <!--Load from template section-->
    <div class="t-flex t-flex-row t-w-[80%]">
      <div class="t-w-4 t-py-2">
        <input
          size="small"
          type="radio"
          [value]="SlipSheetTypeEnum.usePlaceHolderTemplate"
          formControlName="rdSlipsheetOption"
          id="rdPlaceholderTemplate"
          (change)="onSlipsheetOptionChange($event)"
          data-qa="slipsheet-option-load-from-template"
          kendoRadioButton />
      </div>
      <div class="t-w-auto t-px-1.5 t-py-2">
        <kendo-label
          for="rdPlaceholderTemplate"
          class="t-k-radio-label t-px-[5px] t-relative t-top-[2px] t-flex t-flex-wrap"
          [ngClass]="{
            't-text-primary t-font-medium':
              this.slipsheetForm.get('rdSlipsheetOption').value ===
              SlipSheetTypeEnum.usePlaceHolderTemplate
          }"
          text="Load from a template">
        </kendo-label>
      </div>
      <!-- template selection dropdown-->
      <div class="t-mt-[2px] t-w-[15rem]">
        <kendo-dropdownlist
          id="slipsheetTemplateId"
          formControlName="slipsheetTemplateId"
          [data]="slipsheetTemplates"
          valueField="slipsheetTemplateId"
          textField="slipsheetTemplateName">
        </kendo-dropdownlist>
      </div>
    </div>

    <!--Slipsheet text section-->
    <div class="t-flex t-flex-row t-w-[80%] t-mt-2">
      <div class="t-w-4">
        <input
          size="small"
          type="radio"
          [value]="SlipSheetTypeEnum.usePlaceHolderText"
          formControlName="rdSlipsheetOption"
          id="rdPlaceholderText"
          (change)="onSlipsheetOptionChange($event)"
          data-qa="slipsheet-option-create-from-text"
          kendoRadioButton />
      </div>
      <div class="t-w-1/2 t-px-1.5">
        <kendo-label
          for="rdPlaceholderText"
          class="t-k-radio-label t-px-[5px] t-relative t-top-[2px] t-flex t-flex-wrap"
          [ngClass]="{
            't-text-primary t-font-medium':
              this.slipsheetForm.get('rdSlipsheetOption').value ===
              SlipSheetTypeEnum.usePlaceHolderText
          }"
          text="Text">
        </kendo-label>
      </div>
    </div>

    <div class="t-w-full t-flex t-flex-row t-mt-3">
      <!-- Slipsheet text area div-->
      <div
        class="t-bg-white t-bg-no-repeat t-border-[1px] t-border-solid t-border-[#BEBEBE] t-rounded-[4px] t-opacity-100 t-p-3 t-flex t-flex-col t-w-full"
        [ngClass]="{
          't-pointer-events-none t-cursor-not-allowed t-opacity-50':
            selectedRadiobutton !== 'rdPlaceholderText'
        }">
        <div class="t-px-2 t-flex t-flex-row t-gap-x-2">
          <form
            [formGroup]="fontSelectionForm"
            class="t-flex t-flex-row t-gap-x-2 t-w-[310px]">
            <kendo-dropdownlist
              class="t-w-1/2"
              id="fontName"
              [data]="fontNames"
              formControlName="fontName"
              data-qa="slipsheet-font-name-dropdown"></kendo-dropdownlist>

            <kendo-dropdownlist
              class="t-w-1/4"
              id="fontSize"
              [data]="fontSizes"
              data-qa="slipsheet-font-size-dropdown"
              formControlName="fontSize"></kendo-dropdownlist>

            <kendo-buttongroup [selection]="selectionMode">
              <button
                kendoButton
                [svgIcon]="boldSVG"
                title="Bold"
                [value]="'Bold'"
                [ngClass]="{
                  'k-selected k-focus': isSelected('Bold')
                }"
                (click)="onFontStyleChange('Bold')"></button>
              <button
                kendoButton
                [svgIcon]="italicSVG"
                title="Italic"
                [value]="'Italic'"
                [ngClass]="{
                  'k-selected k-focus': isSelected('Italic')
                }"
                (click)="onFontStyleChange('Italic')"></button>
            </kendo-buttongroup>
          </form>

          <kendo-dropdownlist
            class="t-w-[120px]"
            id="placeHolderPosition"
            formControlName="placeHolderPosition"
            [data]="stampLocationList">
          </kendo-dropdownlist>
          <kendo-dropdownlist
            id="slipsheetField"
            #slipsheetField
            [data]="slipsheetFields"
            [defaultItem]="defaultFieldItem"
            (valueChange)="fieldSelectionChange($event)"
            class="t-w-1/5"
            data-qa="slipsheet-field-dropdownlist"
            [popupSettings]="{ width: 'auto' }"
            [itemDisabled]="disableDefaultItem"></kendo-dropdownlist>
        </div>
        <hr class="t-my-2 t-border-[#0000001F]" />

        <div>
          <kendo-textarea
            [rows]="3"
            resizable="vertical"
            class="textarea-min-height t-border-0 t-border-transparent"
            id="placeHolderText"
            resizable="none"
            formControlName="placeHolderText"></kendo-textarea>
        </div>
      </div>
    </div>
    <!--Slipsheet text validation message-->
    <div
      class="t-w-full t-flex t-flex-row"
      *ngIf="
        displayMessage?.placeHolderText &&
        selectedRadiobutton === 'rdPlaceholderText'
      ">
      <div class="t-flex t-flex-row t-w-[80%] t-my-2 t-text-red-600-temp">
        {{ displayMessage?.placeHolderText }}
      </div>
    </div>
    <div class="t-flex t-flex-row t-w-[80%] t-my-2">
      <!-- 'Replace fulltext with slipsheet text' div-->
      <div class="t-w-full">
        <input
          type="checkbox"
          id="replaceFulltext"
          formControlName="replaceFulltext"
          class="custom-checkbox"
          kendoCheckBox />
        <kendo-label
          for="replaceFulltext"
          class="k-checkbox-label t-align-middle t-relative t-top-[1px]"
          [ngClass]="{
            't-text-primary t-font-medium':
              slipsheetForm.get('replaceFulltext').value
          }"
          text="Replace with text">
        </kendo-label>
      </div>
    </div>

    <!--Slipsheet image section-->
    <div class="t-flex t-flex-col">
      <div class="t-flex t-flex-row t-w-1/5 t-gap-2 t-w-[80%]">
        <div class="t-w-4">
          <input
            size="small"
            type="radio"
            [value]="SlipSheetTypeEnum.usePlaceHolderFile"
            formControlName="rdSlipsheetOption"
            id="rdPlaceholderTiffFile"
            (change)="onSlipsheetOptionChange($event)"
            data-qa="slipsheet-option-create-from-image"
            kendoRadioButton />
        </div>
        <div class="t-pr-2">
          <kendo-label
            for="rdPlaceholderTiffFile"
            class="t-k-radio-label t-pl-[2px] t-relative t-top-[2px] t-flex t-flex-wrap"
            [ngClass]="{
              't-text-primary t-font-medium':
                this.slipsheetForm.get('rdSlipsheetOption').value ===
                SlipSheetTypeEnum.usePlaceHolderFile
            }"
            text="Select a Slipsheet image file">
          </kendo-label>
        </div>
      </div>

      <div class="t-flex t-flex-row t-w-[80%] t-pl-2">
        <!-- Slipsheet image file upload control -->
        <div
          class="v-custom-upload-container t-mt-3"
          [ngClass]="{
            't-pointer-events-none t-cursor-not-allowed t-opacity-50':
              selectedRadiobutton !== 'rdPlaceholderTiffFile'
          }">
          <div
            class="v-upload-area t-flex t-flex-col t-border-2 t-border-dashed t-border-[#BEBEBE] t-p-3 t-min-h-[50px] t-w-[330px] t-flex t-items-center t-justify-center t-text-center t-bg-[#f9f9f9] t-cursor-pointer"
            (dragover)="onDragOver($event)"
            (dragleave)="onDragLeave($event)"
            (drop)="onDrop($event)"
            (click)="triggerFileInput()">
            <p class="t-text-sm t-font-noraml t-text-[#5E6366]">
              Drag & Drop file or Click
              <span
                class="t-font-bold t-text-[#1EBADC] t-text-[#1EBADC] t-cursor-pointer">
                here</span
              >
              to upload the file
            </p>

            <!-- File Preview -->
            <div
              class="t-border-t-2 t-border-[#e0e0e0] t-p-[10px] t-flex t-items-center t-w-full t-mt-[10px]"
              *ngIf="uploadedFile">
              <div class="t-flex t-items-center t-w-full">
                <div class="t-flex-1">
                  <p class="t-text-xs t-font-bold t-m-0">
                    {{ uploadedFile.name }}
                  </p>
                  <p class="t-text-xs t-text-gray-500 t-m-0">
                    {{ uploadedFile.size | number }} KB
                  </p>
                </div>
                <span
                  class="t-text-[10px] t-cursor-pointer t-ml-2.5 hover:t-text-[#d11a2a]"
                  (click)="clearFile($event)"
                  >✖</span
                >
              </div>
            </div>
          </div>
          <input
            type="file"
            #fileInput
            [multiple]="false"
            (change)="onFileSelected($event)"
            hidden />
          <p
            *ngIf="
              errorMessage && selectedRadiobutton === 'rdPlaceholderTiffFile'
            "
            class="t-text-red-600-temp t-mt-2 t-text-xs">
            {{ errorMessage }}
          </p>
        </div>
      </div>

      <!--Replace image of already existing documents with slipsheet-->
      <div class="t-flex t-flex-row k-mt-2 t-items-center">
        <div class="t-w-4">
          <input
            class="custom-checkbox"
            type="checkbox"
            id="replaceTiffofExistingFiles"
            formControlName="replaceTiffofExistingFiles"
            kendoCheckBox />
        </div>

        <div
          class="t-w-full t-pr-4 t-pl-2 t-align-middle t-relative t-top-[1px]">
          <kendo-label
            for="replaceTiffofExistingFiles"
            class="k-checkbox-label"
            [ngClass]="{
              't-text-primary t-font-medium': slipsheetForm.get(
                'replaceTiffofExistingFiles'
              ).value
            }"
            text="Replace image of already existing documents with slipsheet">
          </kendo-label>
        </div>

        <!--Preview button-->
        <button
          #previewButton
          kendoButton
          fillMode="outline"
          themeColor="secondary"
          class="v-custom-secondary-button t-rounded t-text-[#2F3080] t-float-right t-border-[#2F3080]"
          (click)="openPreviewDialog()"
          data-qa="slipsheet-preview-button">
          <div class="t-flex">
            <span
              venioSvgLoader
              [parentElement]="previewButton.element"
              hoverColor="#FFFFFF"
              svgUrl="assets/svg/icon-eye.svg"
              class="t-pr-1 t-pt-0.5"
              height="1rem"
              width="1rem">
            </span>
            PREVIEW
          </div>
        </button>
      </div>
    </div>
  </div>
</form>
