<kendo-daterange class="t-w-full t-block t-relative">
  <div #container class="t-relative t-block">
    <button
      kendoButton
      #anchor
      (click)="openCalendar()"
      class="t-flex t-flex-row-reverse !t-capitalize t-px-3 t-w-auto">
      <div
        [ngStyle]="{
          'width.px': container.getBoundingClientRect().width - 100,
          'min-width.px': 200
        }"
        class="t-flex t-justify-between">
        <span class="t-capitalize t-w-2/3 t-text-left">
          {{ selectedRangeText() || 'Filter By Date' }}
        </span>
        <kendo-svg-icon
          [icon]="downIcon"
          class="t-w-[19px] t-h-[19px] t-text-[#333333]"></kendo-svg-icon>
      </div>
    </button>
  </div>
  <kendo-daterange-popup
    [animate]="true"
    #popup
    (close)="popupOpened = false"
    [anchor]="{ nativeElement: anchor.element }">
    <ng-template kendoDateRangePopupTemplate>
      <div
        class="t-flex t-flex-col t-absolute t-right-[-132px] t-w-[132px] t-h-full t-bg-white t-shadow-[31px_-10px_35px_-25px_rgba(0,0,0,0.2)]">
        <ul
          class="t-list-none t-flex t-flex-col t-w-full t-h-[299px] t-text-[#979797] t-gap-1 t-border-l-[1px] t-border-b-[1px] t-border-b-[#cccccc] t-border-l-[#cccccc] t-text-xs t-pt-5">
          <ng-container *ngFor="let menu of menuItems">
            <ng-container
              *ngTemplateOutlet="
                menuTemplate;
                context: { $implicit: menu }
              "></ng-container>
          </ng-container>
          <ng-template #menuTemplate let-menu>
            <li
              [ngClass]="{
                't-bg-[var(--kendo-custom-secondary-100)] t-text-white hover:!t-text-white':
                  selectedMenu() === menu.value
              }"
              class="hover:t-text-[var(--kendo-neutral-220)] t-cursor-pointer t-px-4 t-py-2"
              (click)="selectRangeMenu(menu.value)">
              {{ menu.label }}
            </li>
          </ng-template>
        </ul>
        <div class="t-flex">
          <div class="t-flex t-p-3 t-w-full t-justify-end">
            <kendo-buttongroup class="t-flex t-gap-3">
              <button
                kendoButton
                fillMode="clear"
                (click)="popup.cancelPopup()"
                [svgIcon]="xIcon"
                class="t-bg-[#FF5F521A] hover:t-bg-[#FF5F5227] t-text-[#EC3737]"></button>
              <button
                kendoButton
                fillMode="clear"
                (click)="setDateRange($event)"
                [svgIcon]="checkIcon"
                class="t-bg-[#BAE36E3D] hover:t-bg-[#BAE36E3A] t-text-[#88B13F]"></button>
            </kendo-buttongroup>
          </div>
        </div>
      </div>

      <!-- Calendar icon should be always visible to prevent breaking the Layout -->
      <div
        class="t-flex t-items-center t-text-right t-w-full t-gap-2 t-justify-end t-p-4 t-pb-1 t-text-[#979797] t-items-center t-h-[40px]">
        @if (selectedRangeText()) {
        <kendo-svg-icon [icon]="calendarDateIcon"></kendo-svg-icon>
        {{ selectedRangeText() }}
        <button
          kendoTooltip
          [showAfter]="200"
          title="Reset Dates"
          (click)="resetDates()"
          kendoButton
          size="small"
          fillMode="clear"
          [svgIcon]="closeIcon"
          class="t-text-[#979797]"></button>
        }
      </div>
      <kendo-multiviewcalendar
        *ngIf="popupOpened"
        class="v-custom-daterange"
        [selectionRange]="selectedRange()"
        (focus)="calendarFocus()"
        (selectionRangeChange)="dateSelectionChange($event)"
        kendoDateRangeSelection>
      </kendo-multiviewcalendar>
      <div
        class="t-flex t-border t-border-[#ccc] t-border-l-0 t-border-b-0 t-border-r-0 t-relative t-p-5 t-justify-center">
        <div class="t-flex t-text-[#979797]">
          Selected: {{ dateDiffInDays() }} Days
        </div>
      </div>
    </ng-template>
  </kendo-daterange-popup>
</kendo-daterange>
