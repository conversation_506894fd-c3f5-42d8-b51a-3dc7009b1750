import { CommonModule } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  Type,
  ViewChild,
  inject,
  signal,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { Dictionary } from '@ngrx/entity'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import {
  DocumentTag,
  DocumentTagFacade,
  DocumentTagService,
  ProjectTag,
  TagRuleInfo,
} from '@venio/data-access/document-utility'
import {
  ReviewSetStateService,
  SearchFacade,
  StartupsFacade,
  UserRights,
  ViewTagRuleConflictModel,
} from '@venio/data-access/review'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
  TextTruncateDirective,
} from '@venio/feature/shared/directives'
import { DebounceTimer } from '@venio/util/utilities'
import { cloneDeep } from 'lodash'
import {
  Observable,
  Subject,
  filter,
  of,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs'
import { TreeListItem, TreeListModule } from '@progress/kendo-angular-treelist'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import {
  ConditionElement,
  ConditionGroup,
  ConditionType,
  GroupStackType,
  UserModel,
} from '@venio/shared/models/interfaces'
import { UuidGenerator } from '@venio/util/uuid'
import { UserFacade } from '@venio/data-access/common'
import { infoCircleIcon, searchIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { IconsModule } from '@progress/kendo-angular-icons'

@Component({
  selector: 'venio-tags-tree-wrapper',
  templateUrl: './tags-tree-wrapper.component.html',
  styleUrls: ['./tags-tree-wrapper.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TreeViewModule,
    InputsModule,
    LabelModule,
    FormsModule,
    IndicatorsModule,
    ButtonModule,
    SvgLoaderDirective,
    TooltipsModule,
    TreeListModule,
    TextTruncateDirective,
    DynamicHeightDirective,
    IconsModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagsTreeWrapperComponent
  implements OnInit, AfterViewInit, OnDestroy, OnChanges
{
  public tagCommentsComponent = import(
    '../tag-comments/tag-comments.component'
  ).then((c) => c.TagCommentsComponent)

  @Input() public projectId: number

  @Input() public treeNodes: any[]

  @Input() public documentTags: Dictionary<DocumentTag>

  @Input() public currentDocumentTagsState: Dictionary<DocumentTag>

  @Input() public fileId: unknown[]

  @Input() public expandedKeys: any[]

  @Input() public isBulkDocument: boolean

  @Input() public searchTag: string

  public tagCommentsComponentItems: { component: any; inputs: any }

  public isTagCommentsComponentReady = false

  public clonedTreeNodes: any[]

  public infoCircleIcon: SVGIcon = infoCircleIcon

  /**
   * Observes tag selection for local state use cases to toggle tag comment input
   * including validation status by filtering the selected values comment prop if
   * the options are set to `required`
   */
  public readonly selectedTags = new Map<
    number,
    {
      node: ProjectTag
      isCommentBox: boolean
      isChecked: boolean
      isAlwaysVisible?: boolean
    }
  >()

  @Input() public treeNodeSelected: (treeNodeEvent: unknown) => void

  @Input() public onExpandedTagIdsChange: (expandedTagIds: number[]) => void

  @Input() public isConflictDialog: boolean

  // for tree view of tags

  private unsubscribed$: Subject<void> = new Subject<void>()

  public isDocumentTagsLoading = false

  public UserRights = UserRights

  public flatProjectTags: ProjectTag[]

  public tagRuleList: TagRuleInfo[]

  private disabledTagIds: number[] = []

  public tagRuleIconState: { [key: number]: boolean } = {}

  public allowToApplyTag$: Observable<boolean> =
    this.startupsFacade.hasGroupRight$(UserRights.ALLOW_TAGGING_UNTAGGING)

  protected readonly searchIconSvg = searchIcon

  private currentUser = signal<Partial<UserModel>>({})

  public get isExternalUser(): boolean {
    return this.currentUser()?.userRole === 'external'
  }

  /**
   * List of tag ids that have write permission
   */
  private writePermissionTagIds: number[] = []

  @ViewChild('tagsParentContainer', { read: HTMLElement, static: true })
  public tagsParentContainer: ElementRef<HTMLElement>

  public tagRuleInfoComp: Promise<Type<unknown>>

  public reviewSetState = inject(ReviewSetStateService)
  constructor(
    private docuemtTagFacade: DocumentTagFacade,
    private startupsFacade: StartupsFacade,
    private breadcrumbFacade: BreadcrumbFacade,
    private searchFacade: SearchFacade,
    private cdr: ChangeDetectorRef,
    private userFacade: UserFacade,
    private documentTagService: DocumentTagService
  ) {}

  public ngOnInit(): void {
    this.#selectFlatProjectTags()
    this.#selectTagRuleList()
    this.searchDocumentTags()
    this.fetchLoadingState()
    this.filterDocumentTags()
    this.#selectCurrentUser()
  }

  public ngAfterViewInit(): void {
    this.#loadLazyLoadComponents()
  }

  #loadLazyLoadComponents(): void {
    this.tagRuleInfoComp = import(
      '../tag-rule-info-content/tag-rule-info-content.component'
    ).then(({ TagRuleInfoContentComponent }) => TagRuleInfoContentComponent)
  }

  public ngOnChanges(changes: SimpleChanges): void {
    // make object mutable
    this.documentTags = cloneDeep(this.documentTags)
    // once the parent data bind has changes on the tags, the current states are
    // invalid, so we need to clear them.
    if (changes['treeNodes'] || changes['documentTags']) {
      this.resetSelectedTagsMap()
      this.clonedTreeNodes = cloneDeep(this.treeNodes)
      this.loadTagCommentsComponent()
      this.setNodeComments()
      // if search tag is available, then search the nodes
      // this logic is to handle the search tag from the popout window panel
      if (this.searchTag?.length > 0) {
        this.searchNodes(this.searchTag)
      }
      this.cdr.markForCheck()

      this.#mapWritePermittedTagIds()
    }
    // reset the disabled tag IDs when the document tags are updated
    if (changes['documentTags']) {
      this.#resetDisabledTagIds()
      if (this.isConflictDialog) {
        this.#resolveTagRuleConflict()
      }
    }
  }

  /**
   * Resolves tag rule conflicts by filtering TagRuleInfo based on conflictTagRuleIds
   * and sets the tagRuleIconState for each baseTag in the filtered list to show info icon.
   */
  #resolveTagRuleConflict(): void {
    // Get tag rule conflict data
    const ruleInfo: ViewTagRuleConflictModel =
      this.documentTagService.getTagRuleData()

    // Filter TagRuleInfo based on conflictTagRuleIds and extract baseTag
    const baseTags = (this.tagRuleList || [])
      .filter((tagRule) =>
        ruleInfo?.conflictTagRuleIds.includes(tagRule.tagRuleId)
      )
      .map((tagRule) => tagRule.baseTag)

    // Filter document tags based on the baseTags and set icon state for valid tags
    const filteredDocumentTags = Object.values(
      this.currentDocumentTagsState
    ).filter(
      (docTag) => baseTags.includes(docTag.tagId) && docTag.tagState === true
    )

    // Set the tagRuleIconState dynamically based on the filtered tags
    filteredDocumentTags.forEach((docTag) => {
      this.tagRuleIconState[docTag.tagId] = true
    })
  }

  public hasTruncatedText = signal<boolean>(false)

  public isTextTruncated(isTruncated: boolean): void {
    this.hasTruncatedText.set(isTruncated)
  }

  public tagTreeTrackByFn(index: number, item: TreeListItem): string {
    return `${item.data['TagName']}_${item.data['TagId']}}`
  }

  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserSuccessResponse$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((success) => {
        this.currentUser.set(success?.data || {})
      })
  }

  public onExpandedIdsChange(ids): void {
    this.onExpandedTagIdsChange(ids)
  }

  #selectFlatProjectTags(): void {
    this.docuemtTagFacade.selectFlatProjectTags$
      .pipe(
        filter((tags) => Boolean(tags)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((tags) => {
        this.flatProjectTags = tags
      })
  }

  #selectTagRuleList(): void {
    this.docuemtTagFacade.selectTagRuleList$
      .pipe(
        filter((tagRuleList) => Boolean(tagRuleList)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((tagRuleList) => {
        this.tagRuleList = tagRuleList
      })
  }

  private searchDocumentTags(): void {
    this.docuemtTagFacade.selectSearchDocumentTag$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((search) => {
        this.searchNodes(search)
      })
  }

  private filterDocumentTags(): void {
    this.docuemtTagFacade.selectFilterDocumentTags$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((search) => {
        this.searchNodes(search)
      })
  }

  public childrenNodes = (dataitem: any): Observable<any[]> =>
    of(dataitem.children)

  public hasChildrenNodes = (dataitem: any): boolean =>
    !!dataitem.children && dataitem.children.length > 0

  private handleCommentBoxVisibility(
    tag: ProjectTag,
    currentCheckTag: DocumentTag,
    isChecked: boolean
  ): void {
    const hasExistingComments = !!(
      this.documentTags[tag.TagId]?.comments || ''
    ).trim()
    const requirement = (tag.TagCommentRequirement || '').toLowerCase()
    /**
     * These options are static and coming was set from admin.
     * Options are: `none`,`required`,`recommended` & `optional`
     * @see TagCreateComponent.initForm
     */
    const isCommentBox =
      requirement === 'required' || requirement === 'recommended'
    const savedState =
      isCommentBox &&
      ((!isChecked && currentCheckTag.tagState) ||
        (isChecked && !currentCheckTag.tagState))

    const commentBoxConfig = {
      node: tag,
      isCommentBox: savedState,
      isChecked,
      isAlwaysVisible: hasExistingComments,
    }
    this.selectedTags.set(tag.TagId, commentBoxConfig)
    this.setRequiredTagCommentValidation()
  }

  @DebounceTimer(1000)
  private setNodeComments(): void {
    const setComments = (node: any[]): any => {
      node?.forEach((n) => {
        const comments = this.documentTags
          ? this.documentTags[n.TagId]?.comments || ''
          : ''
        const isChecked = this.documentTags
          ? this.documentTags[n.TagId]?.tagState
          : false
        n.Comments = comments
        if (n.children?.length) {
          setComments(n.children)
        }

        if (comments.trim()) {
          this.selectedTags.set(n.TagId, {
            node: n,
            isCommentBox: true,
            isAlwaysVisible: true,
            isChecked,
          })

          this.treeNodeSelected({
            tagId: n.TagId,
            isExclusive: n.IsExclusive,
            comments,
            isChecked,
          })
        }
      })
    }
    setComments(this.treeNodes)
    this.cdr.markForCheck()
  }

  @DebounceTimer(500)
  private setRequiredTagCommentValidation(): void {
    const hasInvalidSelection = [...this.selectedTags.values()].some(
      (s) =>
        s.isCommentBox &&
        !s.node.Comments?.trim() &&
        s.node.TagCommentRequirement?.match(/required/gi)
    )

    // This 'store' instance corresponds to the one used for 'SetRequiredTagMissingCommentValidationAction'
    // in the existing VOD app.
    this.docuemtTagFacade.validateTagComments(hasInvalidSelection)
  }

  public toggleOptionalComment(tag: ProjectTag): void {
    const requirement = (tag.TagCommentRequirement || '').toLowerCase()
    const isOptional = requirement === 'optional'
    if (!isOptional) return

    const exist = this.selectedTags.get(tag.TagId)

    this.setRequiredTagCommentValidation()

    if (!exist) return

    /**
     * These options are static and coming was set from admin.
     * Options are: `none`,`required`,`recommended` & `optional`
     * @see TagCreateComponent.initForm
     */
    exist.isCommentBox = isOptional && !exist.isCommentBox
  }

  public onNodeSelected(
    tagId: number,
    event: Event,
    checkboxRef: HTMLInputElement,
    tag: ProjectTag
  ): void {
    const isChecked = (event.target as HTMLInputElement).checked
    this.#applyTag(tagId, checkboxRef, tag, isChecked)
    this.#applyTagRule(tagId, isChecked)
  }

  #applyTag(
    tagId: number,
    checkboxRef: HTMLInputElement,
    tag: ProjectTag,
    isChecked: boolean
  ): void {
    const isExclusive = tag?.IsExclusive
    const isProfileManualTag = tag?.IsProfileManualTag

    // If the tag is exclusive or profile manual tag, manage its selection accordingly.
    // If the tag is checked, also check all parent tags related to it.
    // If the tag is unchecked, and there are checked child tags in the current session, uncheck them. Otherwise, uncheck only the parent tags.
    if (isExclusive || isProfileManualTag) {
      this.#hanldeTagSelection(tagId, isChecked, checkboxRef, tag)
    } else if (isChecked) {
      const tagIds = tag.TagIdLineage.split('\\').filter(Boolean).map(Number)
      // When the tag is checked, only select the tags that have write permission
      // to avoid selecting tags that are not allowed to be selected.
      const ids = tagIds.filter((id) => this.writePermissionTagIds.includes(id))

      this.#hanldeParentTags(ids, isChecked, tag)
    } else {
      this.#handleChildTags(tagId, tag, isChecked, checkboxRef)
    }
  }

  /**
   * Maps the tag ids that have write permission to the `writePermissionTagIds` array.
   * This is used to filter the tags that have write permission when selecting tags.
   *
   * @returns {void}
   */
  #mapWritePermittedTagIds(): void {
    const resultIds: number[] = []
    const traverse = (nodes: ProjectTag[]): void => {
      nodes?.forEach((node) => {
        if (node.WritePermission) {
          resultIds.push(node.TagId)
        }
        if (node.children?.[0]) {
          traverse(node.children)
        }
      })
    }

    traverse(this.treeNodes)

    this.writePermissionTagIds = resultIds
  }

  // hanldes the parent tags selection for the selected tag
  #hanldeParentTags = (
    tagIds: number[],
    isChecked: boolean,
    tag: ProjectTag
  ): void => {
    tagIds.forEach((tagId) => {
      const selecteTag = document.getElementById('tag_' + tagId)
      const checkBoxTagRef: HTMLInputElement = <HTMLInputElement>selecteTag
      const tagNode = this.flatProjectTags.find((c) => c.TagId === tagId)
      this.#hanldeTagSelection(tagId, isChecked, checkBoxTagRef, tagNode)
    })
  }

  // handles the child tags selection for the selected tag
  #handleChildTags = (
    tagId: number,
    tag: ProjectTag,
    isChecked: boolean,
    checkboxRef: HTMLInputElement
  ): void => {
    const tags = this.#getChildTags(tag)
    const childTagIds: number[] = tags.filter(
      (tagId) => this.currentDocumentTagsState[tagId]?.reviewTagTriState
    )
    // If there are child tags currently checked in the session prior saving, uncheck them.
    // Otherwise, uncheck the respectvive tag only.
    if (childTagIds.length > 0) {
      this.#hanldeParentTags(childTagIds, isChecked, tag)
    } else {
      this.#hanldeTagSelection(tagId, isChecked, checkboxRef, tag)
    }
  }

  // get all child tags for the selected tag
  #getChildTags = (tag: any): any[] => {
    const tags = []
    const getChildren = (tag: any): void => {
      tags.push(tag.TagId)
      if (tag.children) {
        tag.children.forEach((child) => {
          getChildren(child)
        })
      }
    }
    getChildren(tag)
    return tags
  }

  // handles the tag selection
  #hanldeTagSelection(
    tagId: number,
    isChecked: boolean,
    checkboxRef: HTMLInputElement,
    tag: any
  ): void {
    const documentTags = Object.values(this.documentTags)
    const currentCheckTag = documentTags.find((c) => c.tagId === tagId)

    const isExclusive: boolean = tag?.IsExclusive
    const isProfileManualTag: boolean = tag?.IsProfileManualTag

    const treeNodeEvent = {
      tagId,
      isChecked,
      isExclusive,
      comments: this.documentTags[tagId]?.comments || '',
    }
    // logic inside if condition - makes checkbox behave like radio button for exclusive enabled tags
    if (isExclusive) {
      const className: string =
        'checkbox-' +
        this.fileId +
        '-' +
        tag?.TagGroupName?.toLowerCase()?.replace(/\s/g, '-')

      // gets all exclusive tag checkbox input
      const selectedTagChckBox = document.getElementsByClassName(className)

      /** uncheck all checkbox
       * #26863 - allow to choose only one exclusive enabled tag option
       */
      for (let i = 0; i < selectedTagChckBox.length; i++) {
        const exclusiveTag: HTMLInputElement = <HTMLInputElement>(
          selectedTagChckBox[i]
        )

        exclusiveTag.checked = false
        exclusiveTag.indeterminate = false

        // Update the currentDocumentTagsState to match the UI state
        const tagId = +exclusiveTag.getAttribute('id')
        if (
          this.currentDocumentTagsState &&
          this.currentDocumentTagsState[tagId]
        ) {
          this.currentDocumentTagsState[tagId].tagState = false
          this.currentDocumentTagsState[tagId].reviewTagTriState = false
        }
      }

      // Mark for check to refresh the UI state
      this.cdr.markForCheck()
      // sets true/false of selected exclusive enabled tag
      checkboxRef.checked = isChecked
      const selectedTagChildren = this.clonedTreeNodes.find(
        (x) => x.TagName === tag.TagGroupName
      )?.children
      treeNodeEvent['selectedTagChildrenId'] = selectedTagChildren.map(
        (x: { TagId: any }) => x.TagId
      )
    }
    if (isProfileManualTag) {
      const className: string =
        'checkbox-' + tag?.TagGroupName?.toLowerCase()?.replace(/\s/g, '-')

      const selectedTagChckBox = document.getElementsByClassName(className)

      // Call the facade to fetch data and process it with proper reactive approach
      // This avoids creating a new subscription each time the method is called
      of(true)
        .pipe(
          tap(() =>
            this.docuemtTagFacade.fetchTagsFromProfileCategory(
              this.projectId,
              tag?.TagGroupId
            )
          ),
          switchMap(() =>
            this.docuemtTagFacade.selectTagsProfileCategory$.pipe(
              filter((c) => !!c),
              take(1)
            )
          ),
          take(1)
        )
        .subscribe({
          next: (res: number[]) => {
            this.cdr.markForCheck()
            for (let i = 0; i < selectedTagChckBox.length; i++) {
              const exclusiveTag: HTMLInputElement = selectedTagChckBox[
                i
              ] as HTMLInputElement // Corrected casting
              const nodeIdValue = +exclusiveTag.getAttribute('id') // Corrected getting attribute
              if (res.includes(nodeIdValue)) {
                exclusiveTag.checked = false
                exclusiveTag.indeterminate = false

                // Update the currentDocumentTagsState to match the UI state
                const tagId = +exclusiveTag.getAttribute('id')
                if (
                  this.currentDocumentTagsState &&
                  this.currentDocumentTagsState[tagId]
                ) {
                  this.currentDocumentTagsState[tagId].tagState = false
                  this.currentDocumentTagsState[tagId].reviewTagTriState = false
                }
              }
            }
            // sets true/false of selected enabled tag
            checkboxRef.checked = isChecked
          },
        })
    }

    this.handleCommentBoxVisibility(tag, currentCheckTag, isChecked)
    this.treeNodeSelected(treeNodeEvent)
    this.setRequiredTagCommentValidation()
  }

  // check if tagId exists in the ruleTags list
  #applyTagRule(tagId: number, isChecked: boolean): void {
    // Check if the tagId exists in the ruleTags list
    // Filter the tagRules list based on the conditions
    const matchingRules: TagRuleInfo[] = this.tagRuleList?.filter(
      (rule) => rule.baseTag === tagId
    )

    // Check if any matching rules exist
    if (matchingRules?.[0]) {
      this.#showHideTagRuleIcon(tagId, isChecked)
      // Iterate over the matching rules and apply your logic
      matchingRules.forEach((rule) => {
        // If the rule is exclusive, uncheck and disable related checkboxes
        if (!rule.isInclusiveRule) {
          this.#applyExclusiveRule(rule, isChecked)
        }
      })
    }
  }

  #showHideTagRuleIcon(baseTagId: number, isChecked: boolean): void {
    if (isChecked) {
      this.tagRuleIconState[baseTagId] = true
    } else {
      this.tagRuleIconState[baseTagId] = false
      this.clearTagRuleInfo()
    }
  }

  #applyExclusiveRule(rule: TagRuleInfo, isChecked: boolean): void {
    rule.ruleTags.forEach((ruleTagId) => {
      const checkboxElement = document.getElementById(
        `tag_${ruleTagId}`
      ) as HTMLInputElement
      if (!checkboxElement) return

      this.#updateCheckboxState(checkboxElement, ruleTagId, isChecked)

      // Tag rule logic is applicable only when the base tag is selected
      if (!isChecked) {
        return
      }
      this.#applyTagToNode(ruleTagId, checkboxElement)
    })
  }

  #updateCheckboxState(
    checkbox: HTMLInputElement,
    tagId: number,
    isChecked: boolean
  ): void {
    if (isChecked) {
      checkbox.checked = false
      checkbox.indeterminate = false
      checkbox.disabled = true
      this.disabledTagIds.push(tagId)
      this.#showHideTagRuleIcon(tagId, false)
    } else {
      checkbox.disabled = false
      this.disabledTagIds = this.disabledTagIds.filter((id) => id !== tagId)
    }
    this.cdr.markForCheck()
  }

  #applyTagToNode(tagId: number, checkbox: HTMLInputElement): void {
    const tagNode = this.flatProjectTags.find((c) => c.TagId === tagId)
    this.#applyTag(tagId, checkbox, tagNode, false)
  }

  public showTagRulePopOver(node: { TagId: number }): void {
    this.docuemtTagFacade.filterTagRuleId(node.TagId, false)
  }

  public clearTagRuleInfo(): void {
    this.docuemtTagFacade.resetDocumentTagState([
      'filterTagRuleId',
      'isTagHeaderRequired',
    ])
  }

  #resetDisabledTagIds(): void {
    this.disabledTagIds.forEach((tagId) => {
      const checkboxElement = document.getElementById(
        `tag_${tagId}`
      ) as HTMLInputElement
      checkboxElement.disabled = false
    })
    this.tagRuleIconState = {}
  }

  public getClassNameStringFromTagGroup(tagGroupName: string): string {
    return 'checkbox' + '-' + tagGroupName?.toLowerCase()?.replace(/\s/g, '-')
  }

  public getClassForTagName(node: { TagId: string | number }): string {
    if (this.documentTags && this.currentDocumentTagsState) {
      if (
        this.documentTags[node.TagId]?.tagState &&
        !this.currentDocumentTagsState[node.TagId]?.tagState
      )
        return 't-text-error t-font-bold'
      else if (
        !this.documentTags[node.TagId]?.tagState &&
        this.currentDocumentTagsState[node.TagId]?.tagState
      )
        return 't-text-success t-font-bold '
      return 't-text-dark'
    }
    return ''
  }

  public showTagCommentIcon(node: { TagCommentRequirement?: string }): boolean {
    return node.TagCommentRequirement?.toLowerCase() !== 'none'
  }

  public isTagCommentRequired(node: {
    TagCommentRequirement?: string
  }): boolean {
    return node.TagCommentRequirement?.toLowerCase() === 'required'
  }

  private resetSelectedTagsMap(): void {
    this.selectedTags.clear()
  }

  public searchNodes(filterTerm: string): void {
    const clonedNodes = cloneDeep(this.clonedTreeNodes)
    if (filterTerm.trim() === '') {
      this.treeNodes = clonedNodes
    } else {
      this.treeNodes = this.filterNodes(clonedNodes, filterTerm)
    }

    this.#mapWritePermittedTagIds()

    this.cdr.markForCheck()
  }

  private filterNodes(
    nodes: any,
    searchText: string,
    includeParent = false
  ): any {
    return nodes?.filter((node) => {
      const matches = node.TagName.toLowerCase().includes(
        searchText.toLowerCase()
      )

      if (matches && includeParent) {
        return true
      }

      if (node.children) {
        const filteredChildren = this.filterNodes(
          node.children,
          searchText,
          matches
        )
        if (filteredChildren.length > 0 || matches) {
          node.children = filteredChildren
          return true
        }
      }

      return false
    })
  }

  public fetchLoadingState(): void {
    this.docuemtTagFacade.isDocumentTagLoading$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((loading) => {
        this.isDocumentTagsLoading = loading
        this.cdr.markForCheck()
      })
  }

  public onTagCommentChanged(comments: string, tagId: number): void {
    const selected = this.selectedTags.get(tagId)
    const { isChecked, node } = selected
    node.Comments = comments
    const isExclusive: boolean = node?.IsExclusive
    this.documentTags[tagId].comments = comments
    this.treeNodeSelected({
      tagId,
      isExclusive,
      comments,
      isChecked,
    })
    this.setRequiredTagCommentValidation()
  }

  private loadTagCommentsComponent(): void {
    this.tagCommentsComponentItems = {
      component: this.tagCommentsComponent,
      inputs: {
        dataItem: {},
        tagCommentChanged: (comments: string, dataItem: ProjectTag): void => {
          this.onTagCommentChanged(comments, dataItem.TagId)
        },
      },
    }
    this.isTagCommentsComponentReady = true
    this.cdr.markForCheck()
  }

  public getTagCommentInputValue(dataItem: ProjectTag, inputs: any): any {
    inputs.dataItem = dataItem
    return inputs
  }

  public showTagCommentsComponent(dataItem: ProjectTag): boolean {
    const requirement = dataItem?.TagCommentRequirement?.toLowerCase()
    const tagId = dataItem?.TagId
    const selectedTag = this.selectedTags?.get(tagId)
    return (
      requirement &&
      requirement !== 'none' &&
      this.isTagCommentsComponentReady &&
      (selectedTag?.isAlwaysVisible || selectedTag?.isCommentBox)
    )
  }

  public searchByTag(data): void {
    this.#performSearch(`TAGS("${data.TagName}")`)
  }

  #performSearch(searchExpression): void {
    this.addBreadcrumb(searchExpression)

    this.breadcrumbFacade.selectCompleteBreadcrumbSyntax$
      .pipe(take(1), takeUntil(this.unsubscribed$))
      .subscribe((searchExpression) => {
        // If the search expression is empty, default to FileId>0
        searchExpression = searchExpression || 'FileId>0'
        const payload = {
          searchExpression: searchExpression,
          isResetBaseGuid: true,
        }
        this.searchFacade.search(payload)
        this.searchFacade.exitViewer$.next()
      })
  }

  private addBreadcrumb(conditionSyntax: string): void {
    // If the condition syntax is FileId>0, do not add a breadcrumb as it is the default search condition
    if (
      (conditionSyntax || '').replace(/\s+/g, '').trim().toLowerCase() ===
      'fileid>0'
    )
      return

    const payload: ConditionGroup = {
      id: UuidGenerator.uuid,
      conditions: [{ conditionSyntax }] as ConditionElement[],
      conditionType: ConditionType.Group,
      groupStackType: GroupStackType.QUICK_SEARCH,
      checked: true,
    }
    this.breadcrumbFacade.storeBreadcrumbs([payload])
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
