@layer {
  // .k-animation-container {
  //   @apply t-z-10 #{!important};
  // }

  .k-popup {
    // common for all popups
    @apply t-rounded-sm t-shadow-[0_6px_35px_-7px_rgba(0,0,0,0.2)] #{!important};
    &:not(:has(.k-tooltip)) {
      // Styles for .k-popup without a .k-tooltip child
      @apply t-overflow-hidden #{!important};
    }
  }

  .k-popover {
    .k-popover-body {
      @apply t-p-3 #{!important};
    }
  }

  .k-grid-filter-popup {
    .k-filter-menu {
      .k-filter-menu-container {
        @apply t-pt-3 #{!important};
        .k-filter-and {
          @apply t-w-full #{!important};
        }
        .k-button-group {
          @apply t-flex #{!important};
          .k-button {
            @apply t-flex-1 #{!important};
            &.k-button-solid-primary {
              @include k-button-outline-secondary-styles;
              &:hover {
                background-color: #{$custom-secondary-100} !important ;
              }
              &:disabled {
                @apply t-bg-white #{!important};
              }
            }
          }
        }
      }
    }
  }

  kendo-popup {
    .k-popover {
      @apply t-rounded-lg t-border-[1px] t-border-[#D5D5D5] #{!important};
      .k-popover-callout {
        @apply t-border-[1px] t-border-[#D5D5D5] #{!important};
      }

      .k-popover-body:has(.v-error-popover) {
        @apply t-bg-[#F5B991] #{!important};
      }

      .k-popover-body:has(.v-success-popover) {
        @apply t-bg-[#E1F1E4] t-border-[#E1F1E4] #{!important};
      }
    }
    .k-popover:has(.v-error-popover) .k-popover-callout {
      @apply t-bg-[#F5B991] #{!important};
    }
    .k-popover:has(.v-success-popover) .k-popover-callout {
      @apply t-bg-[#E1F1E4] #{!important};
    }

    /* Apply custom positioning (for triangle) for shortcuts in home */
    .k-popover:has(.v-shortcut-popover-home) {
          .k-popover-callout.k-callout-n{
            left: inherit !important;
            @apply t-right-[18%];
          }
        }

    // Hide shadow and outlines on focus
    .k-list-item:focus,
    .k-list-optionlabel:focus,
    .k-list-item.k-focus,
    .k-focus.k-list-optionlabel {
      @apply t-shadow-none t-outline-none #{!important};
    }

    // For grid custom filter menu
    .k-grid-filter-popup{
      @apply t-border-[1px] t-border-[#979797] t-rounded #{!important};
    }

    // to remove the default item from the dropdown list and use the placeholder
    .v-hide-placeholder{
      .k-list-optionlabel{
        @apply t-hidden #{!important};
      }
    }

    // Custom dropdown popover to remove fixed header and add border between groups
    .v-fixed-header-dropdown{
      .k-list-group-sticky-header{
        @apply t-hidden #{!important};
      }
      .k-list-group-item{

        border-top-style: dashed !important;
        border-bottom-style: dashed !important;
        border-top-width: 1px !important;
        border-bottom-width: 1px !important;
        border-color: #ececeb !important;
        @apply t-leading-[0.5] #{!important};
      }
    }
  }

  .v-custom-dropdown-case-title{
      &::before{
        content: " ";
        position: relative;
        width: 100%;
        @apply t-text-[#1DBADC] t-border-b-[1px] t-border-b-[#ececeb] t-border-dashed t-pt-0 t-px-2 t-pb-1.5 t-font-semibold #{!important};
      }

   }

   .v-tags-multiselect{
    .k-list-filter{
      .k-textbox{
        @apply t-border-[1px] t-px-3 t-flex t-flex-row-reverse #{!important};
      }
    }
    .k-check-all{
      @apply t-flex t-items-center t-w-full t-relative #{!important};
      .k-checkbox-label{
        @apply t-text-[#ffffff] #{!important};
        &::after{
          content: "Select All";
          @apply  t-text-[#212121] t-w-[100px] t-absolute t-left-0 #{!important};
        }
      }

    }
   }


}
