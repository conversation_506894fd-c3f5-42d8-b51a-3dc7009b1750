import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  OnDestroy,
  OnInit,
  Optional,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { GridDataResult, GridModule } from '@progress/kendo-angular-grid'
import {
  NotificationModule,
  NotificationService,
  Type as NotificationType,
} from '@progress/kendo-angular-notification'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { PageArgs, UiPaginationModule } from '@venio/ui/pagination'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LayoutModule } from '@progress/kendo-angular-layout'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import {
  CreatePSTRequest,
  DocumentsFacade,
  ListFilesForPstRequest,
  ListFilesForPstResponse,
  ResponsivePstFacade,
  ResponsivePstJob,
  SearchFacade,
  SelectedEmailFile,
  DocSelectionTypeEnum,
} from '@venio/data-access/review'
import { Subject, combineLatest, filter, map, take, takeUntil } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { ControlSettingService } from '@venio/data-access/control-settings'
import dayjs from 'dayjs'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { saveAs } from '@progress/kendo-file-saver'
import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule } from '@angular/forms'
import { LoaderModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-responsive-pst-dialog',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    TooltipsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    UiPaginationModule,
    NotificationModule,
    SvgLoaderDirective,
    LabelModule,
    FormsModule,
    DynamicHeightDirective,
    LoaderModule,
  ],
  templateUrl: './responsive-pst-dialog.component.html',
  styleUrl: './responsive-pst-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResponsivePstDialogComponent implements OnInit, OnDestroy {
  @ViewChild('appendNotification', { read: ViewContainerRef, static: false })
  public appendTo: ViewContainerRef

  public dialogTitle = 'Responsive PST'

  private readonly toDestroy$ = new Subject<void>()

  public showCreatePSTTab = true

  public selectedTabIndex = 0

  public selectionTypeEnum: DocSelectionTypeEnum

  public fileIdList: number[] = []

  public tempSearchResultTable: string

  public payload: ListFilesForPstRequest

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public validFileList: SelectedEmailFile[] = []

  public pstStatus: ResponsivePstJob[] = []

  public tabStatus = 0

  public showWarningMessage = false

  public warningMessage = ''

  public isResponsivePstCreating =
    this.responsivePstFacade.getIsResponsivePstCreating$

  public isGettingFilesForResponsivePst =
    this.responsivePstFacade.getIsGettingFilesForResponsivePst$

  public isDeletingPst = this.responsivePstFacade.getIsDeletingPst$

  public isDownloadingPstInfo =
    this.responsivePstFacade.getIsDownloadingPstInfo$

  public svgIconForGridControls = [
    {
      actionType: 'Download PST',
      iconPath: 'assets/svg/icon-material-round-sim-card-download.svg',
    },
    {
      actionType: 'Download PST Log',
      iconPath: 'assets/svg/icon-color-excel.svg',
    },
    {
      actionType: 'Delete',
      iconPath: 'assets/svg/icon-note-ui-delete.svg',
    },
    {
      actionType: 'Download Excluded Files',
      iconPath: 'assets/svg/icon-fulltext-download.svg',
    },
  ]

  public refreshSvgUrl = 'assets/svg/refresh-svgrepo-com.svg'

  private downloadFileName: string

  public gridDataPST: GridDataResult = { data: [], total: 0 }

  public gridDataStatus: GridDataResult = { data: [], total: 0 }

  public currentPage = 1

  public pageSize = 10

  public skip = 0

  public currentPageStatus = 1

  public pageSizeStatus = 10

  public skipStatus = 0

  // Notification Config
  public content = ''

  public type: NotificationType = { style: 'error', icon: true }

  public hideAfter = 3500

  public width = 520

  public generateSeparatePstPerCustodian: boolean

  constructor(
    private dialogService: DialogService,
    private notificationService: NotificationService,
    private controlSettingService: ControlSettingService,
    private activatedRoute: ActivatedRoute,
    private documentsFacade: DocumentsFacade,
    private searchFacade: SearchFacade,
    private responsivePstFacade: ResponsivePstFacade,
    private cdr: ChangeDetectorRef,
    @Inject(WINDOW)
    private windowRef: Window,
    @Optional()
    private dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.validFileList = []
    this.loadValidFiles()
    this.#handleDocumentSelection()
    this.#populateFilesForResponsivePst()
    this.#getPSTFiles()
    this.#populatePstStatus()
    this.#selectResponsivePstResponses()
  }

  /**
   * Handles document selection.
   * It takes the latest document selection and if not available hide responsive PST tab.
   * @returns {void}
   */
  #handleDocumentSelection(): void {
    combineLatest([
      this.documentsFacade.getIsBatchSelected$,
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.getUnselectedDocuments$,
      this.searchFacade.getTotalHitCount$,
      this.searchFacade.getSearchTempTables$,
    ])
      .pipe(
        map(
          ([
            isBatchSelected,
            selectedDocs,
            unselectedDocs,
            totalHitCount,
            searchTempTables,
          ]) => {
            const selectedDocCount = isBatchSelected
              ? totalHitCount - unselectedDocs.length
              : selectedDocs.length

            if (selectedDocCount > 0) {
              this.selectionTypeEnum = DocSelectionTypeEnum.SelectedFilesOnly

              if (isBatchSelected && unselectedDocs.length > 0) {
                this.selectionTypeEnum =
                  DocSelectionTypeEnum.AllFilesExceptSelected
                this.fileIdList = unselectedDocs
              } else if (isBatchSelected && unselectedDocs.length <= 0) {
                this.selectionTypeEnum = DocSelectionTypeEnum.AllFiles
              } else {
                this.selectionTypeEnum = DocSelectionTypeEnum.SelectedFilesOnly
                this.fileIdList = selectedDocs
              }
              this.tempSearchResultTable =
                searchTempTables.searchResultTempTable
            }

            return selectedDocCount > 0
          }
        ),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((hasSelectedDocs) => {
        this.showCreatePSTTab = hasSelectedDocs
        this.selectedTabIndex = hasSelectedDocs ? 0 : 1

        if (hasSelectedDocs) {
          this.payload = {
            selectionType: this.selectionTypeEnum,
            fileIdList: this.fileIdList,
            globalTempTableName: this.tempSearchResultTable,
          }

          this.responsivePstFacade.getFilesForResponsivePst(
            this.payload,
            this.projectId
          )
        }
      })
  }

  public isSelected(tabIndex: number): boolean {
    return this.selectedTabIndex === tabIndex
  }

  #populateFilesForResponsivePst(): void {
    this.responsivePstFacade.getResponsivePstFiles$
      .pipe(
        filter((responsivePstFiles) => !!responsivePstFiles),
        takeUntil(this.toDestroy$)
      )
      .subscribe((responsivePstFiles: ListFilesForPstResponse) => {
        this.validFileList = responsivePstFiles.selectedEmailFiles
        this.showWarningMessage = responsivePstFiles.showWarningMessage
        this.warningMessage = responsivePstFiles.warningMessage

        this.loadValidFiles()
        this.#resetResponsivePstState()
      })
  }

  #populatePstStatus(): void {
    this.responsivePstFacade.getPSTStatusSuccessResponse$
      .pipe(
        filter((responseModel) => !!responseModel),
        takeUntil(this.toDestroy$)
      )
      .subscribe((responseModel: ResponseModel) => {
        this.pstStatus = responseModel.data

        this.loadPstStatus()
      })
  }

  #getPSTFiles(): void {
    this.responsivePstFacade.getPSTFiles(this.projectId)
  }

  private loadValidFiles(): void {
    this.cdr.markForCheck()
    this.gridDataPST = {
      data: this.validFileList.slice(this.skip, this.skip + this.pageSize),
      total: this.validFileList.length,
    }
  }

  private loadPstStatus(): void {
    this.cdr.markForCheck()
    this.gridDataStatus = {
      data: this.pstStatus.slice(
        this.skipStatus,
        this.skipStatus + this.pageSizeStatus
      ),
      total: this.pstStatus.length,
    }
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
  }

  public pageChanged(args: PageArgs): void {
    if (this.tabStatus === 0 && this.showCreatePSTTab) {
      this.currentPage = args.pageNumber
      this.skip = (args.pageNumber - 1) * args.pageSize
      this.loadValidFiles()
    } else {
      this.currentPageStatus = args.pageNumber
      this.skipStatus = (args.pageNumber - 1) * args.pageSize
      this.loadPstStatus()
    }
  }

  public pageSizeChanged(args: PageArgs): void {
    if (this.tabStatus === 0 && this.showCreatePSTTab) {
      this.currentPage = args.pageNumber
      this.pageSize = args.pageSize
      this.skip = (args.pageNumber - 1) * args.pageSize
      this.loadValidFiles()
    } else {
      this.currentPageStatus = args.pageNumber
      this.pageSizeStatus = args.pageSize
      this.skipStatus = (args.pageNumber - 1) * args.pageSize
      this.loadPstStatus()
    }
  }

  public close(status: string): void {
    this.dialogRef.close()
  }

  private showNotification(
    content: string,
    type: NotificationType,
    delay: number,
    width: number
  ): void {
    this.notificationService.show({
      appendTo: this.appendTo,
      content: content,
      animation: { type: 'fade', duration: 300 },
      type: type, // Use Kendo Notification Type
      cssClass: 'v-custom-save-notification v-custom-notification-multiline',
      position: { horizontal: 'center', vertical: 'top' },
      hideAfter: delay,
      closable: false,
    })
  }

  public browseActionClicked(
    actionType: any,
    dataItem: ResponsivePstJob
  ): void {
    switch (actionType) {
      case 'Download PST':
        {
          this.downloadFileName = dataItem.pstFileName
          const url =
            this.controlSettingService.getControlSetting.WEB_BASE_URL +
            '/Download.aspx?Download=responsivepst&PstId=' +
            dataItem.jobId +
            '&ProjectId=' +
            this.projectId
          const win = this.windowRef.open(url, '_blank')
          win.blur()
          this.windowRef.focus()
        }
        break
      case 'Download PST Log':
        this.downloadFileName = dataItem.pstLogFileName
        this.responsivePstFacade.downloadPSTInfo(
          dataItem.jobId,
          this.projectId,
          'pstlog'
        )
        break
      case 'Delete':
        this.#confirmPstDeletion(dataItem.jobId)
        break
      case 'Download Excluded Files':
        this.downloadFileName = dataItem.excludedLogFileName
        this.responsivePstFacade.downloadPSTInfo(
          dataItem.jobId,
          this.projectId,
          'excludedfileslog'
        )
        break
    }
  }

  #confirmPstDeletion(jobId: number): void {
    const dialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
    })

    this.#setDialogInput(dialogRef.content.instance)

    dialogRef.result
      .pipe(
        filter((result) => typeof result === 'boolean' && result === true),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => this.#performTaskAfterConfirmation(jobId))
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Confirm Responsive PST Deletion'
    instance.message = `Are you sure you want to delete ?`
  }

  #performTaskAfterConfirmation(jobId: number): void {
    this.responsivePstFacade.deletePST(jobId, this.projectId)
  }

  public getErrorFiles(): void {
    this.responsivePstFacade.getErrorFilesCSV(this.payload, this.projectId)
  }

  private downLoadFile(data: any, fileName: string): void {
    const blob = new Blob([data], { type: 'application/octet-stream' })

    saveAs(blob, fileName)
  }

  public createPST(): void {
    const dict = {}

    if (this.validFileList.length > 0)
      this.validFileList.forEach((element) => {
        dict[element.fileId] = element.rootFolder
      })

    const requestPayload: CreatePSTRequest = {
      fileRootFolderCollection: dict,
      globalTempTableName: this.tempSearchResultTable,
      selectionType: this.selectionTypeEnum,
      fileIdList: this.fileIdList,
      splitPstPerCustodian: this.generateSeparatePstPerCustodian,
    }

    this.responsivePstFacade.createPST(requestPayload, this.projectId)
  }

  public onRefreshClick(): void {
    this.responsivePstFacade.getPSTFiles(this.projectId)
  }

  #selectResponsivePstResponses(): void {
    //Download EXcluded File
    this.responsivePstFacade.getDownloadExcludedFileInfoSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        if (success) {
          const currentDate = dayjs().format('YYYYMMDDHHmmssSSS')
          const fileName = 'PstExcludedFiles-' + currentDate + '.csv'
          this.downLoadFile(success, fileName)
        }
        this.#resetResponsivePstState()
      })

    this.responsivePstFacade.getDownloadExcludedFileInfoFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.content = error.message
        this.type = { style: 'error', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetResponsivePstState()
      })

    //Create PST
    this.responsivePstFacade.getCreatePstSuceessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        this.content = 'PST created successfully.'
        this.type = { style: 'success', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetResponsivePstState()

        this.#getPSTFiles()
      })

    this.responsivePstFacade.getCreatePstFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.content = error.message
        this.type = { style: 'error', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetResponsivePstState()
      })

    //Get files for PST
    this.responsivePstFacade.getFilesForPstFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.content = error.message
        this.type = { style: 'error', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.showCreatePSTTab = false
        this.selectedTabIndex = 1
        this.#resetResponsivePstState()
      })

    //PST status
    this.responsivePstFacade.getPSTStatusFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.content = error.message
        this.type = { style: 'error', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetResponsivePstState()
      })

    //Delete PST
    this.responsivePstFacade.deletePstSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        this.content = success.message
        this.type = { style: 'success', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetResponsivePstState()

        this.#getPSTFiles()
      })

    this.responsivePstFacade.deletePstFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.content = error.message
        this.type = { style: 'error', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetResponsivePstState()
      })

    //Download PST
    this.responsivePstFacade.downloadPstInfoSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.toDestroy$)
      )
      .subscribe((success) => {
        this.downLoadFile(success, this.downloadFileName)

        this.#resetResponsivePstState()
      })

    this.responsivePstFacade.downloadPstInfoFailureResponse$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.content = error.message
        this.type = { style: 'error', icon: true }
        this.showNotification(
          this.content,
          this.type,
          this.hideAfter,
          this.width
        )
        this.#resetResponsivePstState()
      })
  }

  #resetResponsivePstState(): void {
    this.responsivePstFacade.resetResponsivePstState([
      'createPstSuccessResponse',
      'createPstFailureResponse',
      'responsivePstFiles',
      'getFilesForPstFailureResponse',
      'getPSTStatusFailureResponse',
      'deletePstSuccessResponse',
      'deletePstFailureResponse',
      'downloadPstInfoSuccessResponse',
      'downloadPstInfoFailureResponse',
      'downloadExcludedFileInfoSuccessResponse',
      'downloadExcludedFileInfoFailureResponse',
    ])
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
