<form [formGroup]="inviteUploadForm()">
  <div class="t-flex t-flex-col t-gap-2">
    <div class="t-flex t-gap-3 t-mt-1">
      <!-- Internal Users Section -->
      <div class="t-w-1/2">
        <kendo-label
          class="t-flex t-items-center t-h-[50px] t-mb-[22px] t-font-semibold t-text-[#263238] t-mb-0.5">
          Share To Internal Users
        </kendo-label>

        <div class="t-flex t-items-center t-mb-4">
          <kendo-textbox
            class="!t-border-[#ccc] !t-w-full"
            placeholder="Search For Internal Users"
            [clearButton]="true"
            (input)="onInternalUserFilter($event)">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                kendoButton
                fillMode="clear"
                class="t-text-[#1EBADC]"
                imageUrl="assets/svg/icon-updated-search.svg"></button>
            </ng-template>
          </kendo-textbox>
        </div>
        @defer {
        <venio-invite-upload-user-grid [gridType]="GridTypes.INTERNAL" />
        <div class="t-flex t-items-center t-h-[15px] t-w-full t-mt-3">
          <div
            *ngIf="hasNoUsersSelected() && !allowExternalUserUpload()"
            class="t-text-error t-text-xs">
            Please select the user.
          </div>
        </div>
        } @placeholder {
        <div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1, 2]">
          <kendo-skeleton [height]="10" shape="circle" [width]="'2%'" />
          <kendo-skeleton [height]="10" shape="rectangle" [width]="'97%'" />
        </div>
        }
      </div>

      <div class="t-flex">
        <div
          class="t-h-full t-bg-green-200 t-w-10 t-h-full custom-svg-line t-bg-no-repeat t-bg-center"></div>
      </div>

      <!-- External Users Section -->
      <div class="t-w-1/2" *ngIf="allowExternalUserUpload()">
        <div class="t-flex t-justify-between t-gap-3 t-mb-2.5">
          <kendo-label class="t-flex t-items-center t-pb-1">
            <input
              type="checkbox"
              formControlName="shareToExternalUsers"
              kendoCheckBox
              class="t-mr-2" />
            Share To External Users
          </kendo-label>

          <div class="t-flex t-flex-col t-flex-1 t-pl-6 t-item-start">
            <div class="t-flex t-items-start">
              <kendo-textbox
                formControlName="newEmail"
                class="t-w-full"
                placeholder="Add New User Email"
                (keydown.enter)="addExternalUser()">
                <ng-template kendoTextBoxSuffixTemplate>
                  <button
                    class="t-h-full !t-px-1"
                    fillMode="outline"
                    size="small"
                    (click)="addExternalUser()"
                    [svgIcon]="plusIcon"
                    kendoButton></button>
                </ng-template>
              </kendo-textbox>
            </div>
            <div class="t-h-[25px] t-flex t-flex-col">
              @if (inviteUploadForm().get('newEmail').hasError('required')) {
              <div class="t-text-error t-text-xs t-w-full t-pt-2">
                Please enter a email address.
              </div>
              } @if
              (inviteUploadForm().get('newEmail').hasError('invalidEmail')) {
              <div class="t-text-error t-text-xs t-w-full t-pt-2">
                Please enter a valid email address.
              </div>
              } @if (inviteUploadForm().get('newEmail').hasError('emailExists'))
              {
              <div class="t-text-error t-text-xs t-w-full t-pt-2">
                This email already exists in the list.
              </div>
              }
            </div>
          </div>
        </div>

        <div class="t-flex t-items-center t-mb-4">
          <kendo-textbox
            class="!t-border-[#ccc] !t-w-full"
            placeholder="Search For External Users"
            [clearButton]="true"
            [disabled]="!shareToExternalUsers()"
            (input)="onExternalUserFilter($event)">
            <ng-template kendoTextBoxSuffixTemplate>
              <button
                kendoButton
                fillMode="clear"
                class="t-text-[#1EBADC]"
                imageUrl="assets/svg/icon-updated-search.svg"></button>
            </ng-template>
          </kendo-textbox>
        </div>
        @defer {
        <venio-invite-upload-user-grid [gridType]="GridTypes.EXTERNAL" />
        <div class="t-flex t-items-center t-h-[15px] t-w-full t-mt-3">
          <div *ngIf="hasNoUsersSelected()" class="t-text-error t-text-xs">
            Please select the user.
          </div>
        </div>
        } @placeholder {
        <div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1, 2]">
          <kendo-skeleton [height]="10" shape="circle" [width]="'2%'" />
          <kendo-skeleton [height]="10" shape="rectangle" [width]="'97%'" />
        </div>
        }
      </div>
    </div>

    <div class="t-flex t-flex-col t-w-full t-justify-start">
      <!-- Instruction Section -->
      @defer {
      <venio-invite-upload-editor [inviteUploadForm]="inviteUploadForm()" />
      } @placeholder {
      <div class="t-flex t-flex-row t-gap-5 t-my-3" *ngFor="let n of [1]">
        <kendo-skeleton [height]="200" shape="rectangle" [width]="'99%'" />
      </div>
      }
      <div class="t-mt-1 t-flex t-flex-col t-items-start t-gap-2">
        <kendo-label class="t-mr-4 t-w-36 t-text-[#707070]"
          >Valid up-to</kendo-label
        >
        <kendo-numerictextbox
          formControlName="validity"
          placeholder="validity"
          data-qa="validity"
          class="t-w-36"
          [min]="1"
          [step]="1"
          [decimals]="0"
          format="number"></kendo-numerictextbox>
        @if (inviteUploadForm().get('validity').hasError('min')) {
        <div class="t-flex t-items-center t-h-[15px] t-w-full t-mt-1">
          <div class="t-text-error t-text-xs">
            Validity must be at least 1 day.
          </div>
        </div>
        }
      </div>
    </div>
  </div>
</form>
