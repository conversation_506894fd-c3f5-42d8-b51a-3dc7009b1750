import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
  Injector,
  OnDestroy,
  OnInit,
  runInInjectionContext,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownsModule,
  MultiSelectComponent,
  RemoveTagEvent,
} from '@progress/kendo-angular-dropdowns'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { ReportsFacade } from '@venio/data-access/reports'
import { ReportTypes } from '@venio/shared/models/constants'
import { Subject, filter, takeUntil, distinctUntilChanged } from 'rxjs'
import { DeletedExportFacade } from '@venio/data-access/common'
import { DeletedExportModeModel } from '@venio/shared/models/interfaces'
import { IconsModule } from '@progress/kendo-angular-icons'
import { CheckBoxModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'

@Component({
  selector: 'venio-delete-mode-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    IconsModule,
    CheckBoxModule,
    LabelModule,
  ],
  templateUrl: './report-delete-mode-dropdown.component.html',
  styleUrl: './report-delete-mode-dropdown.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportDeleteModeDropdownComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly deletedExportFacade = inject(DeletedExportFacade)

  private readonly reportFacade = inject(ReportsFacade)

  private injector = inject(Injector)

  @ViewChild(MultiSelectComponent)
  public multiSelect: MultiSelectComponent

  public readonly filteredDeletedModes = signal<DeletedExportModeModel[]>([])

  public readonly deletedModes = signal<DeletedExportModeModel[]>([])

  public readonly isAllDeletedModesChecked = signal<boolean>(true)

  public readonly selectedDeletedModes = signal<number[]>([])

  public readonly selectedReportType = signal<ReportTypes>(undefined)

  public readonly chevronDownIcon = chevronDownIcon

  public deletedModeSelectionChange(modeIds: number[]): void {
    this.selectedDeletedModes.set(modeIds)
    if (this.selectedDeletedModes().length) {
      this.isAllDeletedModesChecked.set(false)
    }
  }

  public allDeletedModesSelectionChange(checked: boolean): void {
    this.isAllDeletedModesChecked.set(checked)
    if (checked) {
      this.selectedDeletedModes.set([])
    } else {
      this.multiSelect.tags = this.multiSelect.tags.filter(
        (tag) => tag.modeID !== -1
      )
    }
  }

  public tagMapper = (
    items: DeletedExportModeModel[]
  ): DeletedExportModeModel[] | DeletedExportModeModel[][] => {
    if (this.isAllDeletedModesChecked()) {
      return [
        {
          modeID: -1,
          modeName: 'All',
        } as DeletedExportModeModel,
      ]
    }

    if (!items?.[0]) return []

    return items.length === 1 ? items : [items]
  }

  public ngOnInit(): void {
    this.#initializeData()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public filterDeletedModes(value: string): void {
    const filterValue = value.toLowerCase()
    this.filteredDeletedModes.set(
      this.deletedModes().filter((mode) =>
        mode.modeName.toLowerCase().includes(filterValue)
      )
    )
  }

  public chevDownIconClick(deletedModeSelection: MultiSelectComponent): void {
    deletedModeSelection.toggle()
    deletedModeSelection.focus()
  }

  public handleAllDeletedModesClick(
    event: MouseEvent,
    input: HTMLInputElement
  ): void {
    if (event.target['nodeName'] !== 'INPUT') {
      event.stopPropagation()
      event.preventDefault()
      input.checked = !input.checked
    }

    this.allDeletedModesSelectionChange(input.checked)
  }

  public removeTag(event: RemoveTagEvent): void {
    if (event.dataItem.modeID === -1) {
      this.isAllDeletedModesChecked.set(false)
    }
  }

  #initializeData(): void {
    this.#selectSelectedReportType()
    this.#fetchDeletedModes()
    this.#selectDeletedModes()
    this.#storeSelectedDeletedModes()
    this.allDeletedModesSelectionChange(true)
  }

  #fetchDeletedModes(): void {
    this.deletedExportFacade.fetchDeletedExportModeList()
  }

  #setFilteredDeletedModes(type: ReportTypes): void {
    this.selectedDeletedModes.set([])
    this.isAllDeletedModesChecked.set(true)

    switch (type) {
      case ReportTypes.DELETED_EXPORTS:
        this.filteredDeletedModes.set(this.deletedModes())
        break
      // Add more cases if needed for different report types
    }
  }

  #selectDeletedModes(): void {
    this.deletedExportFacade.selectDeletedExportModeListSuccessResponse$
      .pipe(
        filter((response) => Boolean(response?.data)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.deletedModes.set(response.data)
        this.#setFilteredDeletedModes(this.selectedReportType())
      })
  }

  #storeSelectedDeletedModes(): void {
    runInInjectionContext(this.injector, () =>
      effect(
        () => {
          this.reportFacade.storeSelectedDeleteMode(this.selectedDeletedModes())
        },
        { allowSignalWrites: true }
      )
    )
  }

  #selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(
        distinctUntilChanged(),
        filter((type) => Boolean(type)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((type) => {
        this.selectedReportType.set(type)
        this.#setFilteredDeletedModes(type)
      })
  }
}
