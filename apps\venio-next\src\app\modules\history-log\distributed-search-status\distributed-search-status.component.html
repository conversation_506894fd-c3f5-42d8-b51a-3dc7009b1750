<div class="t-flex t-mt-4 t-flex-col t-w-full">
  <kendo-grid
    [kendoGridBinding]="gridViewData"
    kendoGridSelectBy="id"
    [loading]="isDSJobStatusLoading$ | async"
    [filterable]="true"
    [pageSize]="pageSize"
    [pageable]="{ type: 'numeric', position: 'top' }"
    [sortable]="true"
    [groupable]="false"
    [reorderable]="true"
    [resizable]="true"
    [skip]="skip"
    class="t-h-[calc(100vh-14rem)]">
    <ng-template
      kendoPagerTemplate
      let-totalPages="totalPages"
      let-currentPage="currentPage"
      let-total="total">
      <div class="t-flex t-items-center t-gap-2">
        <div class="t-flex t-items-center">
          <input
            type="checkbox"
            #refresh
            kendoCheckBox
            [(ngModel)]="isAutoRefreshChecked" />
          <kendo-label
            class="k-checkbox-label"
            [for]="refresh"
            [ngClass]="{ 't-text-primary t-font-medium': isAutoRefreshChecked }"
            text="Refresh in every "></kendo-label>
        </div>
        <div>
          <kendo-numerictextbox
            class="!t-border-[#ccc] !t-w-20"
            size="large"
            placeholder="1"
            format="##"
            [min]="0"
            [step]="1"
            [(value)]="refreshIntervalInSeconds"
            [disabled]="!isAutoRefreshChecked"></kendo-numerictextbox>
        </div>
        <div>second(s)</div>
      </div>
      <kendo-grid-spacer></kendo-grid-spacer>
      <venio-pagination
        [disabled]="totalPages === 0"
        [totalRecords]="totalRecords"
        [pageSize]="pageSize"
        [showPageJumper]="false"
        [showPageSize]="true"
        [showRowNumberInputBox]="false"
        (pageChanged)="pageChanged($event)"
        (pageSizeChanged)="pageSizeChanged($event)"
        class="t-px-5 t-block t-py-2">
      </venio-pagination>
    </ng-template>

    <ng-template kendoGridToolbarTemplate>
      <kendo-multiselect
        adaptiveMode="auto"
        [(ngModel)]="defaultSelectedStatus"
        [checkboxes]="true"
        [autoClose]="false"
        [data]="statusOptions"
        textField="label"
        valueField="value"
        (valueChange)="onStatusSelectChange($event)"
        class="!t-w-[35rem]"></kendo-multiselect>
      <kendo-grid-spacer></kendo-grid-spacer>
    </ng-template>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="searchName"
      title="Search Name"
      [filterable]="true"
      [width]="220">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="completedLine"
      title="Completed Line"
      [filterable]="false"
      [width]="220">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="completedPercentage"
      title="Completed %"
      [filterable]="false"
      [width]="100">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="status"
      title="Status"
      [filterable]="true"
      [width]="130">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="ETA"
      title="ETA"
      [filterable]="false"
      [width]="200">
    </kendo-grid-column>

    <kendo-grid-column
      headerClass="t-text-primary"
      field="searchedBy"
      title="Searched By"
      [filterable]="true"
      [width]="200">
    </kendo-grid-column>
  </kendo-grid>
</div>
